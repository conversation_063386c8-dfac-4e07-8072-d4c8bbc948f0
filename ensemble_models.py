#!/usr/bin/env python3
"""
Ensemble Methods - Voting, Stacking, Blending
En iyi modelleri birleştirerek daha gü<PERSON>ü ta<PERSON>inler oluşturur
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, KFold
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import VotingRegressor, StackingRegressor
from sklearn.linear_model import Ridge
import warnings
warnings.filterwarnings('ignore')

# Best performing models
from sklearn.neural_network import MLPRegressor
import lightgbm as lgb
import catboost as cb
from sklearn.linear_model import LinearRegression
import tensorflow as tf
from tensorflow import keras

import json
import pickle

def load_model_data():
    """Model için hazır veriyi yükle"""
    print("📊 Model verisi yükleniyor...")
    
    X_train = pd.read_csv('X_train_model_ready.csv', index_col='user_session')
    X_test = pd.read_csv('X_test_model_ready.csv', index_col='user_session')
    y_train = pd.read_csv('y_train_model_ready.csv', index_col='user_session')['session_value']
    
    return X_train, X_test, y_train

def prepare_data(X_train, X_test, y_train):
    """Veri hazırlığı"""
    from sklearn.preprocessing import StandardScaler
    
    # Train-validation split
    X_tr, X_val, y_tr, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )
    
    # Scaling
    scaler = StandardScaler()
    X_tr_scaled = scaler.fit_transform(X_tr)
    X_val_scaled = scaler.transform(X_val)
    X_test_scaled = scaler.transform(X_test)
    
    return X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled, X_test_scaled, scaler

def create_base_models():
    """En iyi performans gösteren base modelleri oluştur"""
    print("🔧 Base modeller oluşturuluyor...")
    
    base_models = {
        'lightgbm': lgb.LGBMRegressor(n_estimators=100, random_state=42, n_jobs=-1, verbose=-1),
        'catboost': cb.CatBoostRegressor(iterations=100, random_state=42, verbose=False),
        'linear_reg': LinearRegression(),
        'mlp': MLPRegressor(hidden_layer_sizes=(100, 50), max_iter=500, random_state=42, early_stopping=True)
    }
    
    return base_models

def create_tensorflow_model(input_dim):
    """TensorFlow modeli oluştur"""
    model = keras.Sequential([
        keras.layers.Dense(128, activation='relu', input_shape=(input_dim,)),
        keras.layers.Dropout(0.3),
        keras.layers.Dense(64, activation='relu'),
        keras.layers.Dropout(0.3),
        keras.layers.Dense(32, activation='relu'),
        keras.layers.Dense(1)
    ])
    
    model.compile(optimizer='adam', loss='mse', metrics=['mae'])
    return model

def voting_ensemble(X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled):
    """Voting Ensemble"""
    print("\n🗳️ VOTING ENSEMBLE")
    print("-" * 40)
    
    # Base modeller (scaled data gerektirmeyenler)
    base_models = [
        ('lightgbm', lgb.LGBMRegressor(n_estimators=100, random_state=42, n_jobs=-1, verbose=-1)),
        ('catboost', cb.CatBoostRegressor(iterations=100, random_state=42, verbose=False)),
        ('linear_reg', LinearRegression())
    ]
    
    # Voting Regressor
    voting_reg = VotingRegressor(base_models)
    
    print("🔄 Voting ensemble eğitiliyor...")
    voting_reg.fit(X_tr, y_tr)
    
    # Predictions
    y_pred_train = voting_reg.predict(X_tr)
    y_pred_val = voting_reg.predict(X_val)
    
    # Metrics
    train_rmse = np.sqrt(mean_squared_error(y_tr, y_pred_train))
    val_rmse = np.sqrt(mean_squared_error(y_val, y_pred_val))
    train_r2 = r2_score(y_tr, y_pred_train)
    val_r2 = r2_score(y_val, y_pred_val)
    
    print(f"✅ Voting Ensemble: Val RMSE = {val_rmse:.3f}, Val R² = {val_r2:.3f}")
    
    return voting_reg, val_rmse, val_r2

def stacking_ensemble(X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled):
    """Stacking Ensemble"""
    print("\n📚 STACKING ENSEMBLE")
    print("-" * 40)
    
    # Base modeller
    base_models = [
        ('lightgbm', lgb.LGBMRegressor(n_estimators=100, random_state=42, n_jobs=-1, verbose=-1)),
        ('catboost', cb.CatBoostRegressor(iterations=100, random_state=42, verbose=False)),
        ('linear_reg', LinearRegression())
    ]
    
    # Meta-learner (Ridge regression)
    meta_learner = Ridge(alpha=1.0, random_state=42)
    
    # Stacking Regressor
    stacking_reg = StackingRegressor(
        estimators=base_models,
        final_estimator=meta_learner,
        cv=5,
        n_jobs=-1
    )
    
    print("🔄 Stacking ensemble eğitiliyor...")
    stacking_reg.fit(X_tr, y_tr)
    
    # Predictions
    y_pred_train = stacking_reg.predict(X_tr)
    y_pred_val = stacking_reg.predict(X_val)
    
    # Metrics
    train_rmse = np.sqrt(mean_squared_error(y_tr, y_pred_train))
    val_rmse = np.sqrt(mean_squared_error(y_val, y_pred_val))
    train_r2 = r2_score(y_tr, y_pred_train)
    val_r2 = r2_score(y_val, y_pred_val)
    
    print(f"✅ Stacking Ensemble: Val RMSE = {val_rmse:.3f}, Val R² = {val_r2:.3f}")
    
    return stacking_reg, val_rmse, val_r2

def blending_ensemble(X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled):
    """Blending Ensemble (Manual weighted average)"""
    print("\n🎨 BLENDING ENSEMBLE")
    print("-" * 40)
    
    # Base modelleri eğit ve tahminleri al
    models = {}
    predictions_train = {}
    predictions_val = {}
    
    # LightGBM
    print("🔄 LightGBM eğitiliyor...")
    lgb_model = lgb.LGBMRegressor(n_estimators=100, random_state=42, n_jobs=-1, verbose=-1)
    lgb_model.fit(X_tr, y_tr)
    models['lightgbm'] = lgb_model
    predictions_train['lightgbm'] = lgb_model.predict(X_tr)
    predictions_val['lightgbm'] = lgb_model.predict(X_val)
    
    # CatBoost
    print("🔄 CatBoost eğitiliyor...")
    cb_model = cb.CatBoostRegressor(iterations=100, random_state=42, verbose=False)
    cb_model.fit(X_tr, y_tr)
    models['catboost'] = cb_model
    predictions_train['catboost'] = cb_model.predict(X_tr)
    predictions_val['catboost'] = cb_model.predict(X_val)
    
    # Linear Regression
    print("🔄 Linear Regression eğitiliyor...")
    lr_model = LinearRegression()
    lr_model.fit(X_tr, y_tr)
    models['linear'] = lr_model
    predictions_train['linear'] = lr_model.predict(X_tr)
    predictions_val['linear'] = lr_model.predict(X_val)
    
    # MLP (scaled data)
    print("🔄 MLP eğitiliyor...")
    mlp_model = MLPRegressor(hidden_layer_sizes=(100, 50), max_iter=500, random_state=42, early_stopping=True)
    mlp_model.fit(X_tr_scaled, y_tr)
    models['mlp'] = mlp_model
    predictions_train['mlp'] = mlp_model.predict(X_tr_scaled)
    predictions_val['mlp'] = mlp_model.predict(X_val_scaled)
    
    # TensorFlow NN
    print("🔄 TensorFlow NN eğitiliyor...")
    tf_model = create_tensorflow_model(X_tr_scaled.shape[1])
    early_stopping = keras.callbacks.EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)
    tf_model.fit(X_tr_scaled, y_tr, validation_data=(X_val_scaled, y_val), 
                epochs=100, batch_size=32, callbacks=[early_stopping], verbose=0)
    models['tensorflow'] = tf_model
    predictions_train['tensorflow'] = tf_model.predict(X_tr_scaled, verbose=0).flatten()
    predictions_val['tensorflow'] = tf_model.predict(X_val_scaled, verbose=0).flatten()
    
    # Optimal weights bulma (validation set üzerinde)
    print("🔄 Optimal weights bulunuyor...")
    
    # Grid search for weights
    best_rmse = float('inf')
    best_weights = None
    
    # Basit grid search (5 model için)
    from itertools import product
    
    weight_options = [0.1, 0.2, 0.3, 0.4]
    weight_combinations = []
    
    for w1 in weight_options:
        for w2 in weight_options:
            for w3 in weight_options:
                for w4 in weight_options:
                    for w5 in weight_options:
                        if abs(w1 + w2 + w3 + w4 + w5 - 1.0) < 0.01:  # Sum should be 1
                            weight_combinations.append([w1, w2, w3, w4, w5])
    
    # En iyi weight kombinasyonunu bul
    for weights in weight_combinations[:100]:  # İlk 100 kombinasyonu test et
        blended_pred = (weights[0] * predictions_val['lightgbm'] + 
                       weights[1] * predictions_val['catboost'] + 
                       weights[2] * predictions_val['linear'] + 
                       weights[3] * predictions_val['mlp'] + 
                       weights[4] * predictions_val['tensorflow'])
        
        rmse = np.sqrt(mean_squared_error(y_val, blended_pred))
        if rmse < best_rmse:
            best_rmse = rmse
            best_weights = weights
    
    # En iyi weights ile final predictions
    final_train_pred = (best_weights[0] * predictions_train['lightgbm'] + 
                       best_weights[1] * predictions_train['catboost'] + 
                       best_weights[2] * predictions_train['linear'] + 
                       best_weights[3] * predictions_train['mlp'] + 
                       best_weights[4] * predictions_train['tensorflow'])
    
    final_val_pred = (best_weights[0] * predictions_val['lightgbm'] + 
                     best_weights[1] * predictions_val['catboost'] + 
                     best_weights[2] * predictions_val['linear'] + 
                     best_weights[3] * predictions_val['mlp'] + 
                     best_weights[4] * predictions_val['tensorflow'])
    
    # Metrics
    train_rmse = np.sqrt(mean_squared_error(y_tr, final_train_pred))
    val_rmse = np.sqrt(mean_squared_error(y_val, final_val_pred))
    train_r2 = r2_score(y_tr, final_train_pred)
    val_r2 = r2_score(y_val, final_val_pred)
    
    print(f"✅ Optimal weights: {[f'{w:.2f}' for w in best_weights]}")
    print(f"✅ Blending Ensemble: Val RMSE = {val_rmse:.3f}, Val R² = {val_r2:.3f}")
    
    return models, best_weights, val_rmse, val_r2

def main():
    """Ana fonksiyon"""
    print("🚀 ENSEMBLE METHODS")
    print("=" * 50)
    
    # Veri yükleme
    X_train, X_test, y_train = load_model_data()
    X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled, X_test_scaled, scaler = prepare_data(
        X_train, X_test, y_train
    )
    
    ensemble_results = []
    
    # Voting Ensemble
    voting_model, voting_rmse, voting_r2 = voting_ensemble(X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled)
    ensemble_results.append({'method': 'Voting', 'val_rmse': voting_rmse, 'val_r2': voting_r2})
    
    # Stacking Ensemble
    stacking_model, stacking_rmse, stacking_r2 = stacking_ensemble(X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled)
    ensemble_results.append({'method': 'Stacking', 'val_rmse': stacking_rmse, 'val_r2': stacking_r2})
    
    # Blending Ensemble
    blending_models, blending_weights, blending_rmse, blending_r2 = blending_ensemble(X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled)
    ensemble_results.append({'method': 'Blending', 'val_rmse': blending_rmse, 'val_r2': blending_r2})
    
    # Sonuçları özetle
    print("\n📊 ENSEMBLE RESULTS SUMMARY")
    print("=" * 50)
    
    df_ensemble = pd.DataFrame(ensemble_results).sort_values('val_rmse')
    
    print(f"{'Method':<15} {'Val RMSE':<10} {'Val R²':<8}")
    print("-" * 35)
    for _, row in df_ensemble.iterrows():
        print(f"{row['method']:<15} {row['val_rmse']:<10.3f} {row['val_r2']:<8.3f}")
    
    # En iyi ensemble
    best_ensemble = df_ensemble.iloc[0]
    print(f"\n🏆 En iyi ensemble: {best_ensemble['method']}")
    print(f"   Val RMSE: {best_ensemble['val_rmse']:.3f}")
    print(f"   Val R²: {best_ensemble['val_r2']:.3f}")
    
    # Sonuçları kaydet
    df_ensemble.to_csv('ensemble_results.csv', index=False)
    print("\n✅ ensemble_results.csv kaydedildi")
    
    print("\n✨ ENSEMBLE METHODS TAMAMLANDI!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
MSE Focused Analysis
MSE metric'i için <PERSON>zel analiz ve düzel<PERSON>er
"""

import pandas as pd
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Models
import lightgbm as lgb
import catboost as cb
from sklearn.linear_model import Ridge, HuberRegressor
from sklearn.ensemble import RandomForestRegressor

def load_data():
    """Veri yükle"""
    print("📊 Veri yükleniyor...")
    
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    train_features = pd.read_csv('train_features.csv', index_col='user_session')
    test_features = pd.read_csv('test_features.csv', index_col='user_session')
    
    return train_df, test_df, train_features, test_features

def analyze_mse_impact(train_df):
    """MSE impact analizi - outlier'ların et<PERSON><PERSON> göster"""
    print("\n🎯 MSE IMPACT ANALYSIS")
    print("=" * 50)
    
    train_session_values = train_df.groupby('user_session')['session_value'].first()
    
    # Outlier thresholds
    q99 = train_session_values.quantile(0.99)
    q95 = train_session_values.quantile(0.95)
    q90 = train_session_values.quantile(0.90)
    
    print(f"📊 Outlier Thresholds:")
    print(f"   90th percentile: {q90:.2f}")
    print(f"   95th percentile: {q95:.2f}")
    print(f"   99th percentile: {q99:.2f}")
    
    # Outlier counts
    outliers_90 = train_session_values[train_session_values > q90]
    outliers_95 = train_session_values[train_session_values > q95]
    outliers_99 = train_session_values[train_session_values > q99]
    
    print(f"\n📈 Outlier Counts:")
    print(f"   >90th percentile: {len(outliers_90):,} sessions ({len(outliers_90)/len(train_session_values)*100:.1f}%)")
    print(f"   >95th percentile: {len(outliers_95):,} sessions ({len(outliers_95)/len(train_session_values)*100:.1f}%)")
    print(f"   >99th percentile: {len(outliers_99):,} sessions ({len(outliers_99)/len(train_session_values)*100:.1f}%)")
    
    # MSE impact simulation
    mean_pred = train_session_values.mean()
    
    # Normal MSE
    normal_mse = mean_squared_error(train_session_values, [mean_pred] * len(train_session_values))
    
    # MSE if we predict outliers perfectly but others with mean
    perfect_outlier_pred = train_session_values.copy()
    perfect_outlier_pred[perfect_outlier_pred <= q99] = mean_pred
    perfect_outlier_mse = mean_squared_error(train_session_values, perfect_outlier_pred)
    
    print(f"\n🔥 MSE Impact:")
    print(f"   Normal MSE (all mean): {normal_mse:.2f}")
    print(f"   Perfect outlier MSE: {perfect_outlier_mse:.2f}")
    print(f"   Improvement: {(normal_mse - perfect_outlier_mse)/normal_mse*100:.1f}%")
    
    # Top outliers
    top_outliers = train_session_values.nlargest(20)
    print(f"\n🚨 Top 20 Outliers:")
    for i, (session, value) in enumerate(top_outliers.items(), 1):
        print(f"   {i:2d}. Session {session}: {value:.2f}")
    
    return train_session_values, q99, q95, q90

def create_outlier_focused_features(train_df, test_df):
    """Outlier'ları yakalamaya odaklı feature'lar"""
    print("\n🎯 OUTLIER-FOCUSED FEATURES")
    print("=" * 50)
    
    def extract_outlier_features(df):
        # Convert event_time to datetime
        df = df.copy()
        df['event_time'] = pd.to_datetime(df['event_time'])

        features = []

        for session_id, group in df.groupby('user_session'):
            # Basic counts
            buy_count = (group['event_type'] == 'BUY').sum()
            add_cart_count = (group['event_type'] == 'ADD_CART').sum()
            view_count = (group['event_type'] == 'VIEW').sum()
            total_events = len(group)
            
            # Outlier indicators
            features.append({
                'user_session': session_id,
                'buy_count': buy_count,
                'add_cart_count': add_cart_count,
                'view_count': view_count,
                'total_events': total_events,
                
                # High-value indicators
                'has_multiple_buys': int(buy_count > 1),
                'has_many_buys': int(buy_count > 2),
                'high_buy_ratio': buy_count / max(total_events, 1),
                'high_activity': int(total_events > 10),
                'very_high_activity': int(total_events > 20),
                
                # Product diversity
                'unique_products': group['product_id'].nunique(),
                'unique_categories': group['category_id'].nunique(),
                'product_per_event': group['product_id'].nunique() / max(total_events, 1),
                
                # Conversion patterns
                'perfect_conversion': int(buy_count == add_cart_count and buy_count > 0),
                'high_conversion': int(buy_count / max(add_cart_count, 1) > 0.5 and add_cart_count > 0),
                
                # Time patterns
                'session_span': (group['event_time'].max() - group['event_time'].min()).total_seconds() / 3600,
                'events_per_hour': total_events / max((group['event_time'].max() - group['event_time'].min()).total_seconds() / 3600, 0.1),
            })
        
        return pd.DataFrame(features).set_index('user_session')
    
    print("🔄 Train features çıkarılıyor...")
    train_features = extract_outlier_features(train_df)
    
    print("🔄 Test features çıkarılıyor...")
    test_features = extract_outlier_features(test_df)
    
    print(f"✅ Train: {train_features.shape}, Test: {test_features.shape}")
    
    return train_features, test_features

def train_robust_models(train_features, train_session_values, test_features):
    """MSE için robust modeller"""
    print("\n🛡️ ROBUST MODELS FOR MSE")
    print("=" * 50)
    
    X_train = train_features
    y_train = train_session_values
    X_test = test_features
    
    # Train-validation split
    X_tr, X_val, y_tr, y_val = train_test_split(X_train, y_train, test_size=0.2, random_state=42)
    
    models = {}
    results = []
    
    # 1. Huber Regressor (robust to outliers)
    print("🔄 Huber Regressor (outlier-robust)...")
    huber = HuberRegressor(epsilon=1.35, alpha=0.01)
    huber.fit(X_tr, y_tr)
    y_pred_huber = huber.predict(X_val)
    huber_mse = mean_squared_error(y_val, y_pred_huber)
    huber_mae = mean_absolute_error(y_val, y_pred_huber)
    
    models['Huber'] = huber
    results.append({'model': 'Huber', 'val_mse': huber_mse, 'val_mae': huber_mae})
    print(f"✅ Huber: MSE={huber_mse:.2f}, MAE={huber_mae:.2f}")
    
    # 2. Ridge with different alphas
    for alpha in [0.1, 1.0, 10.0]:
        print(f"🔄 Ridge (alpha={alpha})...")
        ridge = Ridge(alpha=alpha)
        ridge.fit(X_tr, y_tr)
        y_pred_ridge = ridge.predict(X_val)
        ridge_mse = mean_squared_error(y_val, y_pred_ridge)
        ridge_mae = mean_absolute_error(y_val, y_pred_ridge)
        
        models[f'Ridge_{alpha}'] = ridge
        results.append({'model': f'Ridge_{alpha}', 'val_mse': ridge_mse, 'val_mae': ridge_mae})
        print(f"✅ Ridge_{alpha}: MSE={ridge_mse:.2f}, MAE={ridge_mae:.2f}")
    
    # 3. LightGBM with MSE focus
    print("🔄 LightGBM (MSE optimized)...")
    lgb_model = lgb.LGBMRegressor(
        objective='regression',
        metric='mse',
        n_estimators=500,
        learning_rate=0.05,
        max_depth=6,
        num_leaves=31,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42,
        verbose=-1
    )
    lgb_model.fit(X_tr, y_tr)
    y_pred_lgb = lgb_model.predict(X_val)
    lgb_mse = mean_squared_error(y_val, y_pred_lgb)
    lgb_mae = mean_absolute_error(y_val, y_pred_lgb)
    
    models['LightGBM'] = lgb_model
    results.append({'model': 'LightGBM', 'val_mse': lgb_mse, 'val_mae': lgb_mae})
    print(f"✅ LightGBM: MSE={lgb_mse:.2f}, MAE={lgb_mae:.2f}")
    
    # 4. Random Forest
    print("🔄 Random Forest...")
    rf_model = RandomForestRegressor(
        n_estimators=200,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    )
    rf_model.fit(X_tr, y_tr)
    y_pred_rf = rf_model.predict(X_val)
    rf_mse = mean_squared_error(y_val, y_pred_rf)
    rf_mae = mean_absolute_error(y_val, y_pred_rf)
    
    models['RandomForest'] = rf_model
    results.append({'model': 'RandomForest', 'val_mse': rf_mse, 'val_mae': rf_mae})
    print(f"✅ RandomForest: MSE={rf_mse:.2f}, MAE={rf_mae:.2f}")
    
    # Results summary
    df_results = pd.DataFrame(results).sort_values('val_mse')
    
    print(f"\n📊 MODEL COMPARISON (MSE focused):")
    print(f"{'Model':<15} {'Val MSE':<10} {'Val MAE':<10}")
    print("-" * 35)
    for _, row in df_results.iterrows():
        print(f"{row['model']:<15} {row['val_mse']:<10.2f} {row['val_mae']:<10.2f}")
    
    # Best model
    best_model_name = df_results.iloc[0]['model']
    best_model = models[best_model_name]
    
    print(f"\n🏆 Best model: {best_model_name}")
    
    # Final training on full dataset
    print(f"🔄 Final training on full dataset...")
    best_model.fit(X_train, y_train)
    
    # Predictions
    test_predictions = best_model.predict(X_test)
    
    return best_model, test_predictions, df_results

def create_mse_optimized_submission(test_predictions, test_features):
    """MSE optimize edilmiş submission"""
    print("\n📁 MSE OPTIMIZED SUBMISSION")
    print("=" * 50)
    
    # Sample submission
    sample_sub = pd.read_csv('sample_submission.csv')
    
    # Basic submission
    mse_submission = sample_sub.copy()
    mse_submission['session_value'] = test_predictions
    
    # Clip extreme values (MSE için önemli)
    train_df = pd.read_csv('train.csv')
    train_session_values = train_df.groupby('user_session')['session_value'].first()
    
    # Conservative clipping
    min_val = train_session_values.quantile(0.01)
    max_val = train_session_values.quantile(0.99)
    
    mse_submission['session_value'] = mse_submission['session_value'].clip(min_val, max_val)
    
    mse_submission.to_csv('mse_optimized_submission.csv', index=False)
    
    print(f"✅ mse_optimized_submission.csv oluşturuldu")
    print(f"📊 Predictions stats:")
    print(f"   Mean: {test_predictions.mean():.2f}")
    print(f"   Std: {test_predictions.std():.2f}")
    print(f"   Min: {test_predictions.min():.2f}")
    print(f"   Max: {test_predictions.max():.2f}")
    print(f"📊 Clipped stats:")
    print(f"   Mean: {mse_submission['session_value'].mean():.2f}")
    print(f"   Min: {mse_submission['session_value'].min():.2f}")
    print(f"   Max: {mse_submission['session_value'].max():.2f}")
    
    return mse_submission

def main():
    """Ana fonksiyon"""
    print("🎯 MSE FOCUSED ANALYSIS & OPTIMIZATION")
    print("=" * 60)
    
    # Veri yükleme
    train_df, test_df, _, _ = load_data()
    
    # MSE impact analizi
    train_session_values, q99, q95, q90 = analyze_mse_impact(train_df)
    
    # Outlier-focused features
    train_features, test_features = create_outlier_focused_features(train_df, test_df)
    
    # Target'ı features ile align et
    train_session_values = train_session_values.reindex(train_features.index)
    
    # Robust models
    best_model, test_predictions, results = train_robust_models(
        train_features, train_session_values, test_features
    )
    
    # MSE optimized submission
    mse_submission = create_mse_optimized_submission(test_predictions, test_features)
    
    print(f"\n✨ MSE OPTIMIZATION TAMAMLANDI!")
    print(f"📁 Ana dosya: mse_optimized_submission.csv")
    print(f"💡 Bu submission MSE metric'i için optimize edildi")
    print(f"🎯 Outlier handling ve robust modeling kullanıldı")

if __name__ == "__main__":
    main()

model,val_rmse,params
<PERSON><PERSON>,20.010877493044976,"{""n_estimators"": 368, ""max_depth"": 8, ""learning_rate"": 0.15073825006256605, ""num_leaves"": 36, ""min_child_samples"": 23, ""subsample"": 0.6303948466591162, ""colsample_bytree"": 0.8659292142233562, ""reg_alpha"": 2.407917073506192, ""reg_lambda"": 3.2215530880610315}"
CatBoost,19.149367906545866,"{""iterations"": 409, ""depth"": 3, ""learning_rate"": 0.13892474422969375, ""l2_leaf_reg"": 1.4650278915612143, ""border_count"": 105, ""bagging_temperature"": 0.45433321801523724, ""random_strength"": 8.995621424629297}"
M<PERSON>,18.349570468205613,"{""max_iter"": 500, ""learning_rate_init"": 0.01, ""hidden_layer_sizes"": [100, 50], ""alpha"": 0.1}"
TensorFlow NN,17.942,"""default"""

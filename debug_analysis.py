#!/usr/bin/env python3
"""
Debug Analysis - Public Score Neden Kötü?
Predictions ve feature'ları detaylı analiz eder
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

def load_all_data():
    """Tüm veriyi yükle"""
    print("📊 Tüm veri yükleniyor...")
    
    # Original data
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    
    # Features
    train_features = pd.read_csv('train_features.csv', index_col='user_session')
    test_features = pd.read_csv('test_features.csv', index_col='user_session')
    
    # Predictions
    ensemble_sub = pd.read_csv('ensemble_submission.csv')
    sample_sub = pd.read_csv('sample_submission.csv')
    
    print(f"✅ Train: {train_df.shape}, Test: {test_df.shape}")
    print(f"✅ Train features: {train_features.shape}, Test features: {test_features.shape}")
    print(f"✅ Predictions: {ensemble_sub.shape}")
    
    return train_df, test_df, train_features, test_features, ensemble_sub, sample_sub

def analyze_target_distribution(train_df, ensemble_sub):
    """Target dağılımını analiz et"""
    print("\n🎯 TARGET DISTRIBUTION ANALYSIS")
    print("=" * 50)
    
    # Train session values
    train_session_values = train_df.groupby('user_session')['session_value'].first()
    
    print("📊 TRAIN Session Values:")
    print(f"   Count: {len(train_session_values):,}")
    print(f"   Mean: {train_session_values.mean():.2f}")
    print(f"   Median: {train_session_values.median():.2f}")
    print(f"   Std: {train_session_values.std():.2f}")
    print(f"   Min: {train_session_values.min():.2f}")
    print(f"   Max: {train_session_values.max():.2f}")
    
    # Quantiles
    quantiles = [0.01, 0.05, 0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99]
    print(f"\n📈 Train Quantiles:")
    for q in quantiles:
        value = train_session_values.quantile(q)
        print(f"   {q*100:4.0f}%: {value:8.2f}")
    
    # Predictions
    pred_values = ensemble_sub['session_value']
    
    print(f"\n🔮 PREDICTIONS:")
    print(f"   Count: {len(pred_values):,}")
    print(f"   Mean: {pred_values.mean():.2f}")
    print(f"   Median: {pred_values.median():.2f}")
    print(f"   Std: {pred_values.std():.2f}")
    print(f"   Min: {pred_values.min():.2f}")
    print(f"   Max: {pred_values.max():.2f}")
    
    print(f"\n📈 Prediction Quantiles:")
    for q in quantiles:
        value = pred_values.quantile(q)
        print(f"   {q*100:4.0f}%: {value:8.2f}")
    
    # Comparison
    print(f"\n⚖️ COMPARISON:")
    print(f"   Mean diff: {pred_values.mean() - train_session_values.mean():.2f}")
    print(f"   Std ratio: {pred_values.std() / train_session_values.std():.3f}")
    
    # Outlier analysis
    train_outliers = train_session_values[train_session_values > train_session_values.quantile(0.99)]
    pred_outliers = pred_values[pred_values > pred_values.quantile(0.99)]
    
    print(f"\n🚨 OUTLIERS (>99th percentile):")
    print(f"   Train outliers: {len(train_outliers)} sessions")
    print(f"   Train outlier max: {train_outliers.max():.2f}")
    print(f"   Pred outliers: {len(pred_outliers)} sessions")
    print(f"   Pred outlier max: {pred_outliers.max():.2f}")
    
    return train_session_values, pred_values

def analyze_features_quality(train_features, test_features):
    """Feature kalitesini analiz et"""
    print("\n🔍 FEATURE QUALITY ANALYSIS")
    print("=" * 50)
    
    # Missing values
    train_missing = train_features.isnull().sum()
    test_missing = test_features.isnull().sum()
    
    if train_missing.sum() > 0 or test_missing.sum() > 0:
        print("⚠️ MISSING VALUES:")
        for col in train_features.columns:
            if train_missing[col] > 0 or test_missing[col] > 0:
                print(f"   {col}: Train={train_missing[col]}, Test={test_missing[col]}")
    else:
        print("✅ No missing values")
    
    # Infinite values
    train_inf = np.isinf(train_features.select_dtypes(include=[np.number])).sum()
    test_inf = np.isinf(test_features.select_dtypes(include=[np.number])).sum()
    
    if train_inf.sum() > 0 or test_inf.sum() > 0:
        print("\n⚠️ INFINITE VALUES:")
        for col in train_features.select_dtypes(include=[np.number]).columns:
            if train_inf[col] > 0 or test_inf[col] > 0:
                print(f"   {col}: Train={train_inf[col]}, Test={test_inf[col]}")
    else:
        print("✅ No infinite values")
    
    # Feature distribution comparison
    print(f"\n📊 FEATURE DISTRIBUTION COMPARISON:")
    
    numeric_cols = train_features.select_dtypes(include=[np.number]).columns
    problematic_features = []
    
    for col in numeric_cols[:10]:  # İlk 10 feature'ı kontrol et
        train_mean = train_features[col].mean()
        test_mean = test_features[col].mean()
        train_std = train_features[col].std()
        test_std = test_features[col].std()
        
        mean_diff_ratio = abs(train_mean - test_mean) / (abs(train_mean) + 1e-8)
        std_ratio = test_std / (train_std + 1e-8)
        
        if mean_diff_ratio > 0.5 or std_ratio > 2 or std_ratio < 0.5:
            problematic_features.append(col)
            print(f"   ⚠️ {col}:")
            print(f"      Train: mean={train_mean:.3f}, std={train_std:.3f}")
            print(f"      Test:  mean={test_mean:.3f}, std={test_std:.3f}")
            print(f"      Mean diff ratio: {mean_diff_ratio:.3f}")
            print(f"      Std ratio: {std_ratio:.3f}")
    
    if not problematic_features:
        print("✅ Feature distributions look similar")
    
    return problematic_features

def analyze_predictions_sanity(ensemble_sub, train_df):
    """Predictions'ların mantıklı olup olmadığını kontrol et"""
    print("\n🔮 PREDICTIONS SANITY CHECK")
    print("=" * 50)
    
    pred_values = ensemble_sub['session_value']
    train_session_values = train_df.groupby('user_session')['session_value'].first()
    
    # Negative predictions
    negative_preds = pred_values[pred_values < 0]
    if len(negative_preds) > 0:
        print(f"⚠️ NEGATIVE PREDICTIONS: {len(negative_preds)} sessions")
        print(f"   Min negative: {negative_preds.min():.2f}")
    else:
        print("✅ No negative predictions")
    
    # Extremely high predictions
    train_max = train_session_values.max()
    extreme_preds = pred_values[pred_values > train_max * 2]
    if len(extreme_preds) > 0:
        print(f"⚠️ EXTREMELY HIGH PREDICTIONS: {len(extreme_preds)} sessions")
        print(f"   (>2x train max = {train_max * 2:.2f})")
        print(f"   Max prediction: {extreme_preds.max():.2f}")
        print(f"   Top 5 extreme: {extreme_preds.nlargest(5).values}")
    else:
        print("✅ No extremely high predictions")
    
    # Zero predictions
    zero_preds = pred_values[pred_values == 0]
    if len(zero_preds) > 0:
        print(f"⚠️ ZERO PREDICTIONS: {len(zero_preds)} sessions")
    else:
        print("✅ No zero predictions")
    
    # Very low predictions
    train_min = train_session_values.min()
    very_low_preds = pred_values[pred_values < train_min / 2]
    if len(very_low_preds) > 0:
        print(f"⚠️ VERY LOW PREDICTIONS: {len(very_low_preds)} sessions")
        print(f"   (<0.5x train min = {train_min / 2:.2f})")
        print(f"   Min prediction: {very_low_preds.min():.2f}")
    else:
        print("✅ No very low predictions")

def create_simple_baseline(train_df, test_df):
    """Basit baseline modeller oluştur"""
    print("\n📏 SIMPLE BASELINE MODELS")
    print("=" * 50)
    
    # Train session values
    train_session_values = train_df.groupby('user_session')['session_value'].first()
    
    # Test session count
    test_sessions = test_df['user_session'].unique()
    
    baselines = {}
    
    # Mean baseline
    mean_baseline = np.full(len(test_sessions), train_session_values.mean())
    baselines['mean'] = mean_baseline
    print(f"📊 Mean baseline: {train_session_values.mean():.2f}")
    
    # Median baseline
    median_baseline = np.full(len(test_sessions), train_session_values.median())
    baselines['median'] = median_baseline
    print(f"📊 Median baseline: {train_session_values.median():.2f}")
    
    # Mode baseline (en sık görülen değer)
    mode_value = train_session_values.mode().iloc[0] if len(train_session_values.mode()) > 0 else train_session_values.median()
    mode_baseline = np.full(len(test_sessions), mode_value)
    baselines['mode'] = mode_baseline
    print(f"📊 Mode baseline: {mode_value:.2f}")
    
    # Random baseline (train dağılımından sample)
    np.random.seed(42)
    random_baseline = np.random.choice(train_session_values.values, size=len(test_sessions))
    baselines['random'] = random_baseline
    print(f"📊 Random baseline: mean={random_baseline.mean():.2f}, std={random_baseline.std():.2f}")
    
    # Baseline submissions oluştur
    sample_sub = pd.read_csv('sample_submission.csv')
    
    for name, baseline in baselines.items():
        baseline_sub = sample_sub.copy()
        baseline_sub['session_value'] = baseline
        baseline_sub.to_csv(f'baseline_{name}_submission.csv', index=False)
        print(f"✅ baseline_{name}_submission.csv oluşturuldu")
    
    return baselines

def main():
    """Ana fonksiyon"""
    print("🚨 DEBUG ANALYSIS - PUBLIC SCORE NEDEN KÖTÜ?")
    print("=" * 60)
    
    # Veri yükleme
    train_df, test_df, train_features, test_features, ensemble_sub, sample_sub = load_all_data()
    
    # Target distribution analizi
    train_session_values, pred_values = analyze_target_distribution(train_df, ensemble_sub)
    
    # Feature kalite analizi
    problematic_features = analyze_features_quality(train_features, test_features)
    
    # Predictions sanity check
    analyze_predictions_sanity(ensemble_sub, train_df)
    
    # Simple baselines
    baselines = create_simple_baseline(train_df, test_df)
    
    # Summary
    print(f"\n📋 DEBUG SUMMARY")
    print("=" * 50)
    print(f"🎯 Target Analysis:")
    print(f"   Train mean: {train_session_values.mean():.2f}")
    print(f"   Pred mean: {pred_values.mean():.2f}")
    print(f"   Difference: {abs(pred_values.mean() - train_session_values.mean()):.2f}")
    
    print(f"\n🔍 Feature Issues:")
    if problematic_features:
        print(f"   Problematic features: {len(problematic_features)}")
        for feat in problematic_features[:5]:
            print(f"   - {feat}")
    else:
        print("   No major feature issues detected")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"   1. Test baseline submissions first")
    print(f"   2. Check for data leakage in features")
    print(f"   3. Verify target scaling/transformation")
    print(f"   4. Consider simpler models")
    print(f"   5. Check evaluation metric (RMSE vs MAE)")
    
    print(f"\n✨ DEBUG ANALYSIS TAMAMLANDI!")
    print(f"📁 Baseline submissions oluşturuldu - bunları test edin!")

if __name__ == "__main__":
    main()

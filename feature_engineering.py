#!/usr/bin/env python3
"""
Feature Engineering
EDA bulgularına göre güçlü feature'lar <PERSON>
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Veri setlerini yükle"""
    print("📊 Veri setleri yükleniyor...")
    
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    
    # Event time'ı datetime'a çevir
    train_df['event_time'] = pd.to_datetime(train_df['event_time'])
    test_df['event_time'] = pd.to_datetime(test_df['event_time'])
    
    print(f"✅ Train: {train_df.shape}, Test: {test_df.shape}")
    return train_df, test_df

def create_session_event_features(df):
    """Session bazlı event features - EDA'da en güçlü korelasyonlar"""
    print("🎯 Session Event Features oluşturuluyor...")
    
    # Her session için event türü sayıları
    session_events = df.groupby('user_session')['event_type'].value_counts().unstack(fill_value=0)
    
    # Eksik event türlerini 0 ile doldur
    for event_type in ['VIEW', 'ADD_CART', 'REMOVE_CART', 'BUY']:
        if event_type not in session_events.columns:
            session_events[event_type] = 0
    
    # Feature isimleri düzenle
    session_events.columns = [f'event_{col.lower()}_count' for col in session_events.columns]
    
    # Toplam event sayısı
    session_events['total_events'] = session_events.sum(axis=1)
    
    # Event oranları (EDA'da BUY en güçlü korelasyon: 0.844)
    session_events['buy_ratio'] = session_events['event_buy_count'] / session_events['total_events']
    session_events['add_cart_ratio'] = session_events['event_add_cart_count'] / session_events['total_events']
    session_events['remove_cart_ratio'] = session_events['event_remove_cart_count'] / session_events['total_events']
    session_events['view_ratio'] = session_events['event_view_count'] / session_events['total_events']
    
    # Conversion features (kritik!)
    session_events['cart_to_buy_ratio'] = np.where(
        session_events['event_add_cart_count'] > 0,
        session_events['event_buy_count'] / session_events['event_add_cart_count'],
        0
    )
    
    session_events['view_to_cart_ratio'] = np.where(
        session_events['event_view_count'] > 0,
        session_events['event_add_cart_count'] / session_events['event_view_count'],
        0
    )
    
    # Cart abandonment
    session_events['cart_abandonment'] = (
        session_events['event_add_cart_count'] - session_events['event_buy_count']
    ).clip(lower=0)
    
    # Funnel progression
    session_events['has_view'] = (session_events['event_view_count'] > 0).astype(int)
    session_events['has_add_cart'] = (session_events['event_add_cart_count'] > 0).astype(int)
    session_events['has_buy'] = (session_events['event_buy_count'] > 0).astype(int)
    session_events['funnel_depth'] = (
        session_events['has_view'] + 
        session_events['has_add_cart'] + 
        session_events['has_buy']
    )
    
    return session_events

def create_product_category_features(df):
    """Ürün ve kategori features"""
    print("🛍️ Product & Category Features oluşturuluyor...")
    
    session_features = []
    
    for session_id, group in df.groupby('user_session'):
        features = {
            'user_session': session_id,
            # Benzersiz sayılar
            'unique_products': group['product_id'].nunique(),
            'unique_categories': group['category_id'].nunique(),
            
            # Çeşitlilik
            'product_diversity': group['product_id'].nunique() / len(group),
            'category_diversity': group['category_id'].nunique() / len(group),
            
            # Tekrar eden ürün/kategori davranışı
            'repeated_products': len(group) - group['product_id'].nunique(),
            'repeated_categories': len(group) - group['category_id'].nunique(),
            
            # En sık kullanılan kategori (encode edilecek)
            'most_frequent_category': group['category_id'].mode().iloc[0] if len(group['category_id'].mode()) > 0 else 'UNKNOWN'
        }
        session_features.append(features)
    
    return pd.DataFrame(session_features).set_index('user_session')

def create_temporal_features(df):
    """Zaman bazlı features - EDA'da saat bazlı pattern'lar bulundu"""
    print("⏰ Temporal Features oluşturuluyor...")
    
    session_features = []
    
    for session_id, group in df.groupby('user_session'):
        group_sorted = group.sort_values('event_time')
        
        # Temel zaman features
        first_event = group_sorted.iloc[0]['event_time']
        last_event = group_sorted.iloc[-1]['event_time']
        
        features = {
            'user_session': session_id,
            # Saat bazlı (EDA'da 19:00, 18:00, 11:00 en aktif)
            'hour_first_event': first_event.hour,
            'hour_last_event': last_event.hour,
            'hour_avg': group['event_time'].dt.hour.mean(),
            
            # Gün bazlı
            'day_of_week': first_event.dayofweek,  # 0=Monday
            'is_weekend': int(first_event.dayofweek >= 5),
            
            # Session süresi
            'session_duration_minutes': (last_event - first_event).total_seconds() / 60,
            'session_duration_hours': (last_event - first_event).total_seconds() / 3600,
            
            # Aktivite yoğunluğu
            'events_per_minute': len(group) / max((last_event - first_event).total_seconds() / 60, 1),
            
            # Peak hours (EDA bulgularına göre)
            'is_peak_hour': int(first_event.hour in [18, 19, 11, 10, 12]),
            'is_evening': int(18 <= first_event.hour <= 21),
            'is_morning': int(9 <= first_event.hour <= 12),
            'is_afternoon': int(13 <= first_event.hour <= 17),
            
            # Zaman dağılımı
            'time_span_hours': group['event_time'].dt.hour.max() - group['event_time'].dt.hour.min(),
        }
        session_features.append(features)
    
    return pd.DataFrame(session_features).set_index('user_session')

def create_user_behavior_features(df):
    """Kullanıcı davranış features"""
    print("👤 User Behavior Features oluşturuluyor...")
    
    # Kullanıcı bazlı istatistikler
    user_stats = df.groupby('user_id').agg({
        'user_session': 'nunique',
        'event_type': 'count',
        'product_id': 'nunique',
        'category_id': 'nunique'
    }).rename(columns={
        'user_session': 'user_total_sessions',
        'event_type': 'user_total_events',
        'product_id': 'user_unique_products',
        'category_id': 'user_unique_categories'
    })
    
    # Session'a user bilgilerini ekle
    session_user_map = df.groupby('user_session')['user_id'].first()
    session_features = session_user_map.to_frame().join(user_stats, on='user_id')
    
    # Kullanıcı davranış metrikleri
    session_features['user_avg_events_per_session'] = (
        session_features['user_total_events'] / session_features['user_total_sessions']
    )
    
    session_features['user_product_exploration'] = (
        session_features['user_unique_products'] / session_features['user_total_events']
    )
    
    return session_features.drop('user_id', axis=1)

def create_statistical_features(df, session_events):
    """İstatistiksel ve derived features"""
    print("📊 Statistical Features oluşturuluyor...")
    
    # Global istatistikler (tüm dataset bazında)
    global_stats = {
        'global_avg_events': df.groupby('user_session').size().mean(),
        'global_avg_products': df.groupby('user_session')['product_id'].nunique().mean(),
        'global_avg_categories': df.groupby('user_session')['category_id'].nunique().mean(),
    }
    
    statistical_features = pd.DataFrame(index=session_events.index)
    
    # Z-scores (normalization için önemli)
    statistical_features['events_zscore'] = (
        session_events['total_events'] - global_stats['global_avg_events']
    ) / session_events['total_events'].std()
    
    # Percentile rankings
    statistical_features['events_percentile'] = session_events['total_events'].rank(pct=True)
    statistical_features['buy_count_percentile'] = session_events['event_buy_count'].rank(pct=True)
    
    # Interaction features
    statistical_features['buy_view_interaction'] = (
        session_events['event_buy_count'] * session_events['event_view_count']
    )
    
    statistical_features['cart_view_interaction'] = (
        session_events['event_add_cart_count'] * session_events['event_view_count']
    )
    
    # Complexity score
    statistical_features['session_complexity'] = (
        session_events['total_events'] * 
        session_events['funnel_depth'] * 
        (1 + session_events['buy_ratio'])
    )
    
    return statistical_features

def encode_categorical_features(train_features, test_features):
    """Kategorik değişkenleri encode et"""
    from sklearn.preprocessing import LabelEncoder
    
    print("🔤 Kategorik değişkenler encode ediliyor...")
    
    # Kategorik sütunları bul
    categorical_cols = train_features.select_dtypes(include=['object']).columns
    
    for col in categorical_cols:
        if col in train_features.columns and col in test_features.columns:
            # Label encoder
            le = LabelEncoder()
            
            # Train ve test'i birleştir, fit et, sonra ayır
            combined_values = pd.concat([train_features[col], test_features[col]]).fillna('UNKNOWN')
            le.fit(combined_values)
            
            train_features[col] = le.transform(train_features[col].fillna('UNKNOWN'))
            test_features[col] = le.transform(test_features[col].fillna('UNKNOWN'))
    
    return train_features, test_features

def combine_all_features(train_df, test_df):
    """Tüm feature'ları birleştir"""
    print("🔧 Tüm feature'lar birleştiriliyor...")
    
    def process_dataset(df, is_train=True):
        # Session bazlı event features (en önemli!)
        session_events = create_session_event_features(df)
        
        # Diğer feature grupları
        product_features = create_product_category_features(df)
        temporal_features = create_temporal_features(df)
        user_features = create_user_behavior_features(df)
        statistical_features = create_statistical_features(df, session_events)
        
        # Tüm feature'ları birleştir
        all_features = session_events.join([
            product_features,
            temporal_features, 
            user_features,
            statistical_features
        ], how='left')
        
        # Target değişkeni ekle (sadece train için)
        if is_train:
            target = df.groupby('user_session')['session_value'].first()
            all_features = all_features.join(target, how='left')
        
        return all_features
    
    # Train ve test için feature'ları oluştur
    train_features = process_dataset(train_df, is_train=True)
    test_features = process_dataset(test_df, is_train=False)
    
    # Kategorik değişkenleri encode et
    train_features, test_features = encode_categorical_features(train_features, test_features)
    
    return train_features, test_features

def main():
    """Ana fonksiyon"""
    print("🚀 FEATURE ENGINEERING BAŞLIYOR...")
    print("=" * 50)
    
    # Veri yükleme
    train_df, test_df = load_data()
    
    # Feature engineering
    train_features, test_features = combine_all_features(train_df, test_df)
    
    # Sonuçları kaydet
    print("💾 Feature'lar kaydediliyor...")
    train_features.to_csv('train_features.csv')
    test_features.to_csv('test_features.csv')
    
    print(f"✅ Train features: {train_features.shape}")
    print(f"✅ Test features: {test_features.shape}")
    print(f"📊 Toplam feature sayısı: {train_features.shape[1] - 1}")  # -1 for target
    
    # Feature importance preview
    if 'session_value' in train_features.columns:
        print("\n🎯 En güçlü korelasyonlar:")
        # Sadece numerik sütunları seç
        numeric_features = train_features.select_dtypes(include=[np.number])
        correlations = numeric_features.corr()['session_value'].abs().sort_values(ascending=False)
        for feature, corr in correlations.head(10).items():
            if feature != 'session_value':
                print(f"   {feature}: {corr:.3f}")
    
    print("\n✨ FEATURE ENGINEERING TAMAMLANDI!")
    print("📁 Dosyalar: train_features.csv, test_features.csv")

if __name__ == "__main__":
    main()

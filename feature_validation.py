#!/usr/bin/env python3
"""
Feature Validation ve Model Hazırlığı
Oluşturulan feature'ları analiz eder ve model için hazırlar
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def load_features():
    """Feature'ları yükle"""
    print("📊 Feature'lar yükleniyor...")
    
    train_features = pd.read_csv('train_features.csv', index_col='user_session')
    test_features = pd.read_csv('test_features.csv', index_col='user_session')
    
    print(f"✅ Train features: {train_features.shape}")
    print(f"✅ Test features: {test_features.shape}")
    
    return train_features, test_features

def analyze_feature_quality(train_features):
    """Feature kalitesini analiz et"""
    print("\n🔍 FEATURE KALİTE ANALİZİ")
    print("-" * 40)
    
    # Missing values
    missing_counts = train_features.isnull().sum()
    if missing_counts.sum() > 0:
        print("⚠️ Eksik değerler:")
        for col, count in missing_counts[missing_counts > 0].items():
            print(f"   {col}: {count} ({count/len(train_features)*100:.1f}%)")
    else:
        print("✅ Eksik değer yok")
    
    # Infinite values
    inf_counts = np.isinf(train_features.select_dtypes(include=[np.number])).sum()
    if inf_counts.sum() > 0:
        print("⚠️ Sonsuz değerler:")
        for col, count in inf_counts[inf_counts > 0].items():
            print(f"   {col}: {count}")
    else:
        print("✅ Sonsuz değer yok")
    
    # Constant features
    constant_features = []
    for col in train_features.columns:
        if col != 'session_value' and train_features[col].nunique() <= 1:
            constant_features.append(col)
    
    if constant_features:
        print(f"⚠️ Sabit feature'lar: {constant_features}")
    else:
        print("✅ Sabit feature yok")
    
    # High correlation features
    numeric_features = train_features.select_dtypes(include=[np.number])
    if 'session_value' in numeric_features.columns:
        numeric_features = numeric_features.drop('session_value', axis=1)
    
    corr_matrix = numeric_features.corr().abs()
    high_corr_pairs = []
    
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            if corr_matrix.iloc[i, j] > 0.95:
                high_corr_pairs.append((corr_matrix.columns[i], corr_matrix.columns[j], corr_matrix.iloc[i, j]))
    
    if high_corr_pairs:
        print(f"⚠️ Yüksek korelasyonlu feature çiftleri (>0.95):")
        for feat1, feat2, corr in high_corr_pairs:
            print(f"   {feat1} - {feat2}: {corr:.3f}")
    else:
        print("✅ Yüksek korelasyonlu feature çifti yok")

def feature_importance_analysis(train_features):
    """Feature importance analizi"""
    print("\n🎯 FEATURE IMPORTANCE ANALİZİ")
    print("-" * 40)
    
    # Target ile korelasyonlar
    if 'session_value' in train_features.columns:
        numeric_features = train_features.select_dtypes(include=[np.number])
        correlations = numeric_features.corr()['session_value'].abs().sort_values(ascending=False)
        
        print("📊 Target ile en güçlü korelasyonlar:")
        for i, (feature, corr) in enumerate(correlations.head(15).items(), 1):
            if feature != 'session_value':
                print(f"   {i:2d}. {feature:<25}: {corr:.3f}")
        
        # Random Forest feature importance
        print("\n🌲 Random Forest Feature Importance:")
        
        X = numeric_features.drop('session_value', axis=1)
        y = numeric_features['session_value']
        
        # Eksik değerleri doldur
        X = X.fillna(0)
        
        # Random Forest fit
        rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
        rf.fit(X, y)
        
        # Feature importance
        feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': rf.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("Top 15 önemli feature'lar:")
        for i, row in feature_importance.head(15).iterrows():
            print(f"   {i+1:2d}. {row['feature']:<25}: {row['importance']:.3f}")
        
        return feature_importance
    
    return None

def create_model_ready_data(train_features, test_features):
    """Model için hazır veri seti oluştur"""
    print("\n🔧 MODEL İÇİN VERİ HAZIRLIĞI")
    print("-" * 40)
    
    # Target'ı ayır
    if 'session_value' in train_features.columns:
        X_train = train_features.drop('session_value', axis=1)
        y_train = train_features['session_value']
    else:
        X_train = train_features.copy()
        y_train = None
    
    X_test = test_features.copy()
    
    # Eksik değerleri doldur
    X_train = X_train.fillna(0)
    X_test = X_test.fillna(0)
    
    # Sonsuz değerleri temizle
    X_train = X_train.replace([np.inf, -np.inf], 0)
    X_test = X_test.replace([np.inf, -np.inf], 0)
    
    print(f"✅ X_train: {X_train.shape}")
    print(f"✅ X_test: {X_test.shape}")
    if y_train is not None:
        print(f"✅ y_train: {y_train.shape}")
    
    return X_train, X_test, y_train

def quick_model_test(X_train, y_train):
    """Hızlı model testi"""
    print("\n🚀 HIZLI MODEL TESTİ")
    print("-" * 40)
    
    if y_train is None:
        print("⚠️ Target değişken yok, model testi yapılamıyor")
        return
    
    # Train-validation split
    X_tr, X_val, y_tr, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )
    
    # Random Forest modeli
    print("🌲 Random Forest modeli eğitiliyor...")
    rf = RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        n_jobs=-1
    )
    
    rf.fit(X_tr, y_tr)
    
    # Tahminler
    y_pred_train = rf.predict(X_tr)
    y_pred_val = rf.predict(X_val)
    
    # Metrikler
    train_rmse = np.sqrt(mean_squared_error(y_tr, y_pred_train))
    val_rmse = np.sqrt(mean_squared_error(y_val, y_pred_val))
    train_mae = mean_absolute_error(y_tr, y_pred_train)
    val_mae = mean_absolute_error(y_val, y_pred_val)
    train_r2 = r2_score(y_tr, y_pred_train)
    val_r2 = r2_score(y_val, y_pred_val)
    
    print(f"📊 Model Performansı:")
    print(f"   Train RMSE: {train_rmse:.3f}")
    print(f"   Val RMSE:   {val_rmse:.3f}")
    print(f"   Train MAE:  {train_mae:.3f}")
    print(f"   Val MAE:    {val_mae:.3f}")
    print(f"   Train R²:   {train_r2:.3f}")
    print(f"   Val R²:     {val_r2:.3f}")
    
    # Overfitting kontrolü
    if train_rmse < val_rmse * 0.8:
        print("⚠️ Overfitting riski var!")
    else:
        print("✅ Overfitting riski düşük")
    
    return rf

def save_model_ready_data(X_train, X_test, y_train):
    """Model için hazır veriyi kaydet"""
    print("\n💾 MODEL VERİSİ KAYDEDİLİYOR...")
    print("-" * 40)
    
    # Model için hazır veriyi kaydet
    X_train.to_csv('X_train_model_ready.csv')
    X_test.to_csv('X_test_model_ready.csv')
    
    if y_train is not None:
        y_train.to_csv('y_train_model_ready.csv')
    
    print("✅ Dosyalar kaydedildi:")
    print("   📁 X_train_model_ready.csv")
    print("   📁 X_test_model_ready.csv")
    if y_train is not None:
        print("   📁 y_train_model_ready.csv")

def main():
    """Ana fonksiyon"""
    print("🔍 FEATURE VALIDATION VE MODEL HAZIRLIĞI")
    print("=" * 50)
    
    # Feature'ları yükle
    train_features, test_features = load_features()
    
    # Feature kalite analizi
    analyze_feature_quality(train_features)
    
    # Feature importance analizi
    feature_importance = feature_importance_analysis(train_features)
    
    # Model için veri hazırlığı
    X_train, X_test, y_train = create_model_ready_data(train_features, test_features)
    
    # Hızlı model testi
    model = quick_model_test(X_train, y_train)
    
    # Model için hazır veriyi kaydet
    save_model_ready_data(X_train, X_test, y_train)
    
    print("\n✨ FEATURE VALIDATION TAMAMLANDI!")
    print("🚀 Artık model eğitimine geçebilirsiniz!")

if __name__ == "__main__":
    main()

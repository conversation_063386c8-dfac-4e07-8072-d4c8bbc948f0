# Copyright 2019 The KerasTuner Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from keras_tuner.src.protos.v4 import (
    service_pb2 as keras__tuner_dot_protos_dot_service__pb2,
)


class OracleStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetSpace = channel.unary_unary(
            "/keras_tuner.Oracle/GetSpace",
            request_serializer=keras__tuner_dot_protos_dot_service__pb2.GetSpaceRequest.SerializeToString,
            response_deserializer=keras__tuner_dot_protos_dot_service__pb2.GetSpaceResponse.FromString,
        )
        self.UpdateSpace = channel.unary_unary(
            "/keras_tuner.Oracle/UpdateSpace",
            request_serializer=keras__tuner_dot_protos_dot_service__pb2.UpdateSpaceRequest.SerializeToString,
            response_deserializer=keras__tuner_dot_protos_dot_service__pb2.UpdateSpaceResponse.FromString,
        )
        self.CreateTrial = channel.unary_unary(
            "/keras_tuner.Oracle/CreateTrial",
            request_serializer=keras__tuner_dot_protos_dot_service__pb2.CreateTrialRequest.SerializeToString,
            response_deserializer=keras__tuner_dot_protos_dot_service__pb2.CreateTrialResponse.FromString,
        )
        self.UpdateTrial = channel.unary_unary(
            "/keras_tuner.Oracle/UpdateTrial",
            request_serializer=keras__tuner_dot_protos_dot_service__pb2.UpdateTrialRequest.SerializeToString,
            response_deserializer=keras__tuner_dot_protos_dot_service__pb2.UpdateTrialResponse.FromString,
        )
        self.EndTrial = channel.unary_unary(
            "/keras_tuner.Oracle/EndTrial",
            request_serializer=keras__tuner_dot_protos_dot_service__pb2.EndTrialRequest.SerializeToString,
            response_deserializer=keras__tuner_dot_protos_dot_service__pb2.EndTrialResponse.FromString,
        )
        self.GetBestTrials = channel.unary_unary(
            "/keras_tuner.Oracle/GetBestTrials",
            request_serializer=keras__tuner_dot_protos_dot_service__pb2.GetBestTrialsRequest.SerializeToString,
            response_deserializer=keras__tuner_dot_protos_dot_service__pb2.GetBestTrialsResponse.FromString,
        )
        self.GetTrial = channel.unary_unary(
            "/keras_tuner.Oracle/GetTrial",
            request_serializer=keras__tuner_dot_protos_dot_service__pb2.GetTrialRequest.SerializeToString,
            response_deserializer=keras__tuner_dot_protos_dot_service__pb2.GetTrialResponse.FromString,
        )


class OracleServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetSpace(self, request, context):
        """Return the HyperParameter search space."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UpdateSpace(self, request, context):
        """Updates the HyperParameter search space."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def CreateTrial(self, request, context):
        """Creates a Trial."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UpdateTrial(self, request, context):
        """Updates a Trial with metrics and a step."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def EndTrial(self, request, context):
        """Ends a Trial."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetBestTrials(self, request, context):
        """Gets the best Trials."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetTrial(self, request, context):
        """Gets a Trial by ID."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_OracleServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "GetSpace": grpc.unary_unary_rpc_method_handler(
            servicer.GetSpace,
            request_deserializer=keras__tuner_dot_protos_dot_service__pb2.GetSpaceRequest.FromString,
            response_serializer=keras__tuner_dot_protos_dot_service__pb2.GetSpaceResponse.SerializeToString,
        ),
        "UpdateSpace": grpc.unary_unary_rpc_method_handler(
            servicer.UpdateSpace,
            request_deserializer=keras__tuner_dot_protos_dot_service__pb2.UpdateSpaceRequest.FromString,
            response_serializer=keras__tuner_dot_protos_dot_service__pb2.UpdateSpaceResponse.SerializeToString,
        ),
        "CreateTrial": grpc.unary_unary_rpc_method_handler(
            servicer.CreateTrial,
            request_deserializer=keras__tuner_dot_protos_dot_service__pb2.CreateTrialRequest.FromString,
            response_serializer=keras__tuner_dot_protos_dot_service__pb2.CreateTrialResponse.SerializeToString,
        ),
        "UpdateTrial": grpc.unary_unary_rpc_method_handler(
            servicer.UpdateTrial,
            request_deserializer=keras__tuner_dot_protos_dot_service__pb2.UpdateTrialRequest.FromString,
            response_serializer=keras__tuner_dot_protos_dot_service__pb2.UpdateTrialResponse.SerializeToString,
        ),
        "EndTrial": grpc.unary_unary_rpc_method_handler(
            servicer.EndTrial,
            request_deserializer=keras__tuner_dot_protos_dot_service__pb2.EndTrialRequest.FromString,
            response_serializer=keras__tuner_dot_protos_dot_service__pb2.EndTrialResponse.SerializeToString,
        ),
        "GetBestTrials": grpc.unary_unary_rpc_method_handler(
            servicer.GetBestTrials,
            request_deserializer=keras__tuner_dot_protos_dot_service__pb2.GetBestTrialsRequest.FromString,
            response_serializer=keras__tuner_dot_protos_dot_service__pb2.GetBestTrialsResponse.SerializeToString,
        ),
        "GetTrial": grpc.unary_unary_rpc_method_handler(
            servicer.GetTrial,
            request_deserializer=keras__tuner_dot_protos_dot_service__pb2.GetTrialRequest.FromString,
            response_serializer=keras__tuner_dot_protos_dot_service__pb2.GetTrialResponse.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "keras_tuner.Oracle", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class Oracle(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetSpace(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/keras_tuner.Oracle/GetSpace",
            keras__tuner_dot_protos_dot_service__pb2.GetSpaceRequest.SerializeToString,
            keras__tuner_dot_protos_dot_service__pb2.GetSpaceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UpdateSpace(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/keras_tuner.Oracle/UpdateSpace",
            keras__tuner_dot_protos_dot_service__pb2.UpdateSpaceRequest.SerializeToString,
            keras__tuner_dot_protos_dot_service__pb2.UpdateSpaceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def CreateTrial(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/keras_tuner.Oracle/CreateTrial",
            keras__tuner_dot_protos_dot_service__pb2.CreateTrialRequest.SerializeToString,
            keras__tuner_dot_protos_dot_service__pb2.CreateTrialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UpdateTrial(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/keras_tuner.Oracle/UpdateTrial",
            keras__tuner_dot_protos_dot_service__pb2.UpdateTrialRequest.SerializeToString,
            keras__tuner_dot_protos_dot_service__pb2.UpdateTrialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def EndTrial(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/keras_tuner.Oracle/EndTrial",
            keras__tuner_dot_protos_dot_service__pb2.EndTrialRequest.SerializeToString,
            keras__tuner_dot_protos_dot_service__pb2.EndTrialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetBestTrials(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/keras_tuner.Oracle/GetBestTrials",
            keras__tuner_dot_protos_dot_service__pb2.GetBestTrialsRequest.SerializeToString,
            keras__tuner_dot_protos_dot_service__pb2.GetBestTrialsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetTrial(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/keras_tuner.Oracle/GetTrial",
            keras__tuner_dot_protos_dot_service__pb2.GetTrialRequest.SerializeToString,
            keras__tuner_dot_protos_dot_service__pb2.GetTrialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )


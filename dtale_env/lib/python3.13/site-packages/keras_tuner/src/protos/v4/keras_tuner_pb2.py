# Copyright 2019 The KerasTuner Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: keras_tuner/protos/keras_tuner.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n$keras_tuner/protos/keras_tuner.proto\x12\x0bkeras_tuner"l\n\x05Value\x12\x13\n\tint_value\x18\x01 \x01(\x12H\x00\x12\x15\n\x0b\x66loat_value\x18\x02 \x01(\x01H\x00\x12\x16\n\x0cstring_value\x18\x03 \x01(\tH\x00\x12\x17\n\rboolean_value\x18\x04 \x01(\x08H\x00\x42\x06\n\x04kind"\xaf\x01\n\x05\x46loat\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tmin_value\x18\x02 \x01(\x01\x12\x11\n\tmax_value\x18\x03 \x01(\x01\x12\x0c\n\x04step\x18\x04 \x01(\x01\x12\'\n\x08sampling\x18\x05 \x01(\x0e\x32\x15.keras_tuner.Sampling\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\x06 \x01(\x01\x12*\n\nconditions\x18\x07 \x03(\x0b\x32\x16.keras_tuner.Condition"\xad\x01\n\x03Int\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tmin_value\x18\x02 \x01(\x12\x12\x11\n\tmax_value\x18\x03 \x01(\x12\x12\x0c\n\x04step\x18\x04 \x01(\x12\x12\'\n\x08sampling\x18\x05 \x01(\x0e\x32\x15.keras_tuner.Sampling\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\x06 \x01(\x12\x12*\n\nconditions\x18\x07 \x03(\x0b\x32\x16.keras_tuner.Condition"\x9c\x01\n\x06\x43hoice\x12\x0c\n\x04name\x18\x01 \x01(\t\x12"\n\x06values\x18\x02 \x03(\x0b\x32\x12.keras_tuner.Value\x12#\n\x07\x64\x65\x66\x61ult\x18\x03 \x01(\x0b\x32\x12.keras_tuner.Value\x12\x0f\n\x07ordered\x18\x04 \x01(\x08\x12*\n\nconditions\x18\x05 \x03(\x0b\x32\x16.keras_tuner.Condition"T\n\x07\x42oolean\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\x02 \x01(\x08\x12*\n\nconditions\x18\x03 \x03(\x0b\x32\x16.keras_tuner.Condition"d\n\x05\x46ixed\x12\x0c\n\x04name\x18\x01 \x01(\t\x12!\n\x05value\x18\x02 \x01(\x0b\x32\x12.keras_tuner.Value\x12*\n\nconditions\x18\x03 \x03(\x0b\x32\x16.keras_tuner.Condition"\xe1\x03\n\x0fHyperParameters\x12\x31\n\x05space\x18\x01 \x01(\x0b\x32".keras_tuner.HyperParameters.Space\x12\x33\n\x06values\x18\x02 \x01(\x0b\x32#.keras_tuner.HyperParameters.Values\x1a\xd6\x01\n\x05Space\x12\'\n\x0b\x66loat_space\x18\x01 \x03(\x0b\x32\x12.keras_tuner.Float\x12#\n\tint_space\x18\x02 \x03(\x0b\x32\x10.keras_tuner.Int\x12)\n\x0c\x63hoice_space\x18\x03 \x03(\x0b\x32\x13.keras_tuner.Choice\x12+\n\rboolean_space\x18\x04 \x03(\x0b\x32\x14.keras_tuner.Boolean\x12\'\n\x0b\x66ixed_space\x18\x05 \x03(\x0b\x32\x12.keras_tuner.Fixed\x1a\x8c\x01\n\x06Values\x12?\n\x06values\x18\x01 \x03(\x0b\x32/.keras_tuner.HyperParameters.Values.ValuesEntry\x1a\x41\n\x0bValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12!\n\x05value\x18\x02 \x01(\x0b\x32\x12.keras_tuner.Value:\x02\x38\x01"0\n\x11MetricObservation\x12\r\n\x05value\x18\x01 \x03(\x02\x12\x0c\n\x04step\x18\x02 \x01(\x03"W\n\rMetricHistory\x12\x34\n\x0cobservations\x18\x01 \x03(\x0b\x32\x1e.keras_tuner.MetricObservation\x12\x10\n\x08maximize\x18\x02 \x01(\x08"\x97\x01\n\x0eMetricsTracker\x12\x39\n\x07metrics\x18\x01 \x03(\x0b\x32(.keras_tuner.MetricsTracker.MetricsEntry\x1aJ\n\x0cMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12)\n\x05value\x18\x02 \x01(\x0b\x32\x1a.keras_tuner.MetricHistory:\x02\x38\x01"\xf7\x01\n\x05Trial\x12\x35\n\x0fhyperparameters\x18\x01 \x01(\x0b\x32\x1c.keras_tuner.HyperParameters\x12\x10\n\x08trial_id\x18\x02 \x01(\t\x12(\n\x06status\x18\x03 \x01(\x0e\x32\x18.keras_tuner.TrialStatus\x12,\n\x07metrics\x18\x04 \x01(\x0b\x32\x1b.keras_tuner.MetricsTracker\x12\'\n\x05score\x18\x05 \x01(\x0b\x32\x18.keras_tuner.Trial.Score\x1a$\n\x05Score\x12\r\n\x05value\x18\x01 \x01(\x02\x12\x0c\n\x04step\x18\x02 \x01(\x03"\x80\x01\n\tCondition\x12/\n\x06parent\x18\x01 \x01(\x0b\x32\x1d.keras_tuner.Condition.ParentH\x00\x1a:\n\x06Parent\x12\x0c\n\x04name\x18\x01 \x01(\t\x12"\n\x06values\x18\x02 \x03(\x0b\x32\x12.keras_tuner.ValueB\x06\n\x04kind*:\n\x08Sampling\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06LINEAR\x10\x01\x12\x07\n\x03LOG\x10\x02\x12\x0f\n\x0bREVERSE_LOG\x10\x03*f\n\x0bTrialStatus\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0b\n\x07RUNNING\x10\x01\x12\x08\n\x04IDLE\x10\x02\x12\x0b\n\x07INVALID\x10\x03\x12\x0b\n\x07STOPPED\x10\x04\x12\r\n\tCOMPLETED\x10\x05\x12\n\n\x06\x46\x41ILED\x10\x06\x62\x06proto3'
)

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "keras_tuner.protos.keras_tuner_pb2", globals()
)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    _HYPERPARAMETERS_VALUES_VALUESENTRY._options = None
    _HYPERPARAMETERS_VALUES_VALUESENTRY._serialized_options = b"8\001"
    _METRICSTRACKER_METRICSENTRY._options = None
    _METRICSTRACKER_METRICSENTRY._serialized_options = b"8\001"
    _SAMPLING._serialized_start = 2022
    _SAMPLING._serialized_end = 2080
    _TRIALSTATUS._serialized_start = 2082
    _TRIALSTATUS._serialized_end = 2184
    _VALUE._serialized_start = 53
    _VALUE._serialized_end = 161
    _FLOAT._serialized_start = 164
    _FLOAT._serialized_end = 339
    _INT._serialized_start = 342
    _INT._serialized_end = 515
    _CHOICE._serialized_start = 518
    _CHOICE._serialized_end = 674
    _BOOLEAN._serialized_start = 676
    _BOOLEAN._serialized_end = 760
    _FIXED._serialized_start = 762
    _FIXED._serialized_end = 862
    _HYPERPARAMETERS._serialized_start = 865
    _HYPERPARAMETERS._serialized_end = 1346
    _HYPERPARAMETERS_SPACE._serialized_start = 989
    _HYPERPARAMETERS_SPACE._serialized_end = 1203
    _HYPERPARAMETERS_VALUES._serialized_start = 1206
    _HYPERPARAMETERS_VALUES._serialized_end = 1346
    _HYPERPARAMETERS_VALUES_VALUESENTRY._serialized_start = 1281
    _HYPERPARAMETERS_VALUES_VALUESENTRY._serialized_end = 1346
    _METRICOBSERVATION._serialized_start = 1348
    _METRICOBSERVATION._serialized_end = 1396
    _METRICHISTORY._serialized_start = 1398
    _METRICHISTORY._serialized_end = 1485
    _METRICSTRACKER._serialized_start = 1488
    _METRICSTRACKER._serialized_end = 1639
    _METRICSTRACKER_METRICSENTRY._serialized_start = 1565
    _METRICSTRACKER_METRICSENTRY._serialized_end = 1639
    _TRIAL._serialized_start = 1642
    _TRIAL._serialized_end = 1889
    _TRIAL_SCORE._serialized_start = 1853
    _TRIAL_SCORE._serialized_end = 1889
    _CONDITION._serialized_start = 1892
    _CONDITION._serialized_end = 2020
    _CONDITION_PARENT._serialized_start = 1954
    _CONDITION_PARENT._serialized_end = 2012
# @@protoc_insertion_point(module_scope)


# Copyright 2019 The KerasTuner Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: keras_tuner/protos/service.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from keras_tuner.src.protos.v4 import (
    keras_tuner_pb2 as keras__tuner_dot_protos_dot_keras__tuner__pb2,
)


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n keras_tuner/protos/service.proto\x12\x0bkeras_tuner\x1a$keras_tuner/protos/keras_tuner.proto"\x11\n\x0fGetSpaceRequest"I\n\x10GetSpaceResponse\x12\x35\n\x0fhyperparameters\x18\x01 \x01(\x0b\x32\x1c.keras_tuner.HyperParameters"K\n\x12UpdateSpaceRequest\x12\x35\n\x0fhyperparameters\x18\x01 \x01(\x0b\x32\x1c.keras_tuner.HyperParameters"\x15\n\x13UpdateSpaceResponse"&\n\x12\x43reateTrialRequest\x12\x10\n\x08tuner_id\x18\x01 \x01(\t"8\n\x13\x43reateTrialResponse\x12!\n\x05trial\x18\x01 \x01(\x0b\x32\x12.keras_tuner.Trial"\xa3\x01\n\x12UpdateTrialRequest\x12\x10\n\x08trial_id\x18\x01 \x01(\t\x12=\n\x07metrics\x18\x02 \x03(\x0b\x32,.keras_tuner.UpdateTrialRequest.MetricsEntry\x12\x0c\n\x04step\x18\x03 \x01(\x03\x1a.\n\x0cMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01"8\n\x13UpdateTrialResponse\x12!\n\x05trial\x18\x01 \x01(\x0b\x32\x12.keras_tuner.Trial"4\n\x0f\x45ndTrialRequest\x12!\n\x05trial\x18\x01 \x01(\x0b\x32\x12.keras_tuner.Trial"\x12\n\x10\x45ndTrialResponse"*\n\x14GetBestTrialsRequest\x12\x12\n\nnum_trials\x18\x01 \x01(\x03";\n\x15GetBestTrialsResponse\x12"\n\x06trials\x18\x01 \x03(\x0b\x32\x12.keras_tuner.Trial"#\n\x0fGetTrialRequest\x12\x10\n\x08trial_id\x18\x01 \x01(\t"5\n\x10GetTrialResponse\x12!\n\x05trial\x18\x01 \x01(\x0b\x32\x12.keras_tuner.Trial2\xbf\x04\n\x06Oracle\x12I\n\x08GetSpace\x12\x1c.keras_tuner.GetSpaceRequest\x1a\x1d.keras_tuner.GetSpaceResponse"\x00\x12R\n\x0bUpdateSpace\x12\x1f.keras_tuner.UpdateSpaceRequest\x1a .keras_tuner.UpdateSpaceResponse"\x00\x12R\n\x0b\x43reateTrial\x12\x1f.keras_tuner.CreateTrialRequest\x1a .keras_tuner.CreateTrialResponse"\x00\x12R\n\x0bUpdateTrial\x12\x1f.keras_tuner.UpdateTrialRequest\x1a .keras_tuner.UpdateTrialResponse"\x00\x12I\n\x08\x45ndTrial\x12\x1c.keras_tuner.EndTrialRequest\x1a\x1d.keras_tuner.EndTrialResponse"\x00\x12X\n\rGetBestTrials\x12!.keras_tuner.GetBestTrialsRequest\x1a".keras_tuner.GetBestTrialsResponse"\x00\x12I\n\x08GetTrial\x12\x1c.keras_tuner.GetTrialRequest\x1a\x1d.keras_tuner.GetTrialResponse"\x00\x62\x06proto3'
)

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(
    DESCRIPTOR, "keras_tuner.protos.service_pb2", globals()
)
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    _UPDATETRIALREQUEST_METRICSENTRY._options = None
    _UPDATETRIALREQUEST_METRICSENTRY._serialized_options = b"8\001"
    _GETSPACEREQUEST._serialized_start = 87
    _GETSPACEREQUEST._serialized_end = 104
    _GETSPACERESPONSE._serialized_start = 106
    _GETSPACERESPONSE._serialized_end = 179
    _UPDATESPACEREQUEST._serialized_start = 181
    _UPDATESPACEREQUEST._serialized_end = 256
    _UPDATESPACERESPONSE._serialized_start = 258
    _UPDATESPACERESPONSE._serialized_end = 279
    _CREATETRIALREQUEST._serialized_start = 281
    _CREATETRIALREQUEST._serialized_end = 319
    _CREATETRIALRESPONSE._serialized_start = 321
    _CREATETRIALRESPONSE._serialized_end = 377
    _UPDATETRIALREQUEST._serialized_start = 380
    _UPDATETRIALREQUEST._serialized_end = 543
    _UPDATETRIALREQUEST_METRICSENTRY._serialized_start = 497
    _UPDATETRIALREQUEST_METRICSENTRY._serialized_end = 543
    _UPDATETRIALRESPONSE._serialized_start = 545
    _UPDATETRIALRESPONSE._serialized_end = 601
    _ENDTRIALREQUEST._serialized_start = 603
    _ENDTRIALREQUEST._serialized_end = 655
    _ENDTRIALRESPONSE._serialized_start = 657
    _ENDTRIALRESPONSE._serialized_end = 675
    _GETBESTTRIALSREQUEST._serialized_start = 677
    _GETBESTTRIALSREQUEST._serialized_end = 719
    _GETBESTTRIALSRESPONSE._serialized_start = 721
    _GETBESTTRIALSRESPONSE._serialized_end = 780
    _GETTRIALREQUEST._serialized_start = 782
    _GETTRIALREQUEST._serialized_end = 817
    _GETTRIALRESPONSE._serialized_start = 819
    _GETTRIALRESPONSE._serialized_end = 872
    _ORACLE._serialized_start = 875
    _ORACLE._serialized_end = 1450
# @@protoc_insertion_point(module_scope)


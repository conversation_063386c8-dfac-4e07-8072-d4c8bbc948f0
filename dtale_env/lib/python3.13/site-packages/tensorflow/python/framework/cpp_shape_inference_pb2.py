# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: tensorflow/python/framework/cpp_shape_inference.proto
# Protobuf Python Version: 5.28.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    28,
    3,
    '',
    'tensorflow/python/framework/cpp_shape_inference.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.core.framework import full_type_pb2 as tensorflow_dot_core_dot_framework_dot_full__type__pb2
from tensorflow.core.framework import tensor_shape_pb2 as tensorflow_dot_core_dot_framework_dot_tensor__shape__pb2
from tensorflow.core.framework import types_pb2 as tensorflow_dot_core_dot_framework_dot_types__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n5tensorflow/python/framework/cpp_shape_inference.proto\x12\ntensorflow\x1a)tensorflow/core/framework/full_type.proto\x1a,tensorflow/core/framework/tensor_shape.proto\x1a%tensorflow/core/framework/types.proto\"\x9b\x03\n\x17\x43ppShapeInferenceResult\x12+\n\x05shape\x18\x01 \x01(\x0b\x32\x1c.tensorflow.TensorShapeProto\x12\x43\n\x0bhandle_data\x18\x04 \x01(\x0b\x32..tensorflow.CppShapeInferenceResult.HandleData\x1a\x93\x01\n\x12HandleShapeAndType\x12+\n\x05shape\x18\x01 \x01(\x0b\x32\x1c.tensorflow.TensorShapeProto\x12#\n\x05\x64type\x18\x02 \x01(\x0e\x32\x14.tensorflow.DataType\x12%\n\x04type\x18\x04 \x01(\x0b\x32\x17.tensorflow.FullTypeDefJ\x04\x08\x03\x10\x04\x1al\n\nHandleData\x12\x0e\n\x06is_set\x18\x01 \x01(\x08\x12N\n\x0eshape_and_type\x18\x02 \x03(\x0b\x32\x36.tensorflow.CppShapeInferenceResult.HandleShapeAndTypeJ\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04\"e\n\x1d\x43ppShapeInferenceInputsNeeded\x12\x1c\n\x14input_tensors_needed\x18\x01 \x03(\x05\x12&\n\x1einput_tensors_as_shapes_needed\x18\x02 \x03(\x05\x42\x61Z\\github.com/tensorflow/tensorflow/tensorflow/go/python/framework/cpp_shape_inference_go_proto\xf8\x01\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tensorflow.python.framework.cpp_shape_inference_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z\\github.com/tensorflow/tensorflow/tensorflow/go/python/framework/cpp_shape_inference_go_proto\370\001\001'
  _globals['_CPPSHAPEINFERENCERESULT']._serialized_start=198
  _globals['_CPPSHAPEINFERENCERESULT']._serialized_end=609
  _globals['_CPPSHAPEINFERENCERESULT_HANDLESHAPEANDTYPE']._serialized_start=340
  _globals['_CPPSHAPEINFERENCERESULT_HANDLESHAPEANDTYPE']._serialized_end=487
  _globals['_CPPSHAPEINFERENCERESULT_HANDLEDATA']._serialized_start=489
  _globals['_CPPSHAPEINFERENCERESULT_HANDLEDATA']._serialized_end=597
  _globals['_CPPSHAPEINFERENCEINPUTSNEEDED']._serialized_start=611
  _globals['_CPPSHAPEINFERENCEINPUTSNEEDED']._serialized_end=712
# @@protoc_insertion_point(module_scope)

# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

class DType:
    def __init__(self, arg0: object) -> None: ...
    def __hash__(self) -> int: ...
    def __int__(self) -> int: ...
    @property
    def as_datatype_enum(self) -> int: ...
    @property
    def is_bool(self) -> bool: ...
    @property
    def is_complex(self) -> bool: ...
    @property
    def is_floating(self) -> bool: ...
    @property
    def is_integer(self) -> bool: ...
    @property
    def is_numeric(self) -> bool: ...
    @property
    def is_numpy_compatible(self) -> bool: ...
    @property
    def is_quantized(self) -> bool: ...
    @property
    def is_unsigned(self) -> bool: ...
    @property
    def name(self) -> str: ...
    @property
    def size(self) -> int: ...

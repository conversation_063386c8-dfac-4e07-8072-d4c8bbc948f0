# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

class Cluster:
    def __init__(self, *args, **kwargs) -> None: ...

def TF_DeterminePeakMemoryUsage(arg0, arg1: Cluster) -> dict[str, tuple[int, list[tuple[str, int, int, int, int]]]]: ...
def TF_EstimatePerformance(arg0: bytes) -> float: ...
def TF_GetSupportedDevices(arg0: Cluster, arg1) -> dict[str, list[str]]: ...
def TF_ListAvailableOps() -> list[str]: ...
def TF_ListDevices(arg0: Cluster) -> list[bytes]: ...
def TF_MeasureCosts(arg0, arg1: Cluster, arg2: bool) -> tuple[list[bytes], float, bytes]: ...
def TF_NewCluster(arg0: bool, arg1: bool) -> Cluster: ...
def TF_NewVirtualCluster(arg0: list[bytes]) -> Cluster: ...
def TF_ShutdownCluster(arg0: Cluster) -> None: ...

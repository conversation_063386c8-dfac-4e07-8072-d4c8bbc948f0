{"version": 3, "file": "83022.dtale_bundle.js", "mappings": "8FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,qBACLC,MAAO,gCAETC,SAAU,CACRF,IAAK,YACLC,MAAO,qBAETE,YAAa,kBACbC,iBAAkB,CAChBJ,IAAK,oBACLC,MAAO,+BAETI,SAAU,CACRL,IAAK,WACLC,MAAO,oBAETK,YAAa,CACXN,IAAK,cACLC,MAAO,wBAETM,OAAQ,CACNP,IAAK,QACLC,MAAO,kBAETO,MAAO,CACLR,IAAK,QACLC,MAAO,kBAETQ,YAAa,CACXT,IAAK,iBACLC,MAAO,0BAETS,OAAQ,CACNV,IAAK,WACLC,MAAO,oBAETU,aAAc,CACZX,IAAK,gBACLC,MAAO,0BAETW,QAAS,CACPZ,IAAK,UACLC,MAAO,oBAETY,YAAa,CACXb,IAAK,iBACLC,MAAO,0BAETa,OAAQ,CACNd,IAAK,WACLC,MAAO,oBAETc,WAAY,CACVf,IAAK,qBACLC,MAAO,8BAETe,aAAc,CACZhB,IAAK,oBACLC,MAAO,8BA2BPgB,EAvBiB,SAAwBC,EAAOC,EAAOC,GACzD,IAAIC,EACAC,EAAaxB,EAAqBoB,GAUtC,OAPEG,EADwB,iBAAfC,EACAA,EACU,IAAVH,EACAG,EAAWtB,IAEXsB,EAAWrB,MAAMsB,QAAQ,YAAaC,OAAOL,IAGpDC,SAA0CA,EAAQK,UAChDL,EAAQM,YAAcN,EAAQM,WAAa,EACtC,MAAQL,EAER,QAAUA,EAIdA,CACT,EAGAzB,EAAA,QAAkBqB,EAClBU,EAAO/B,QAAUA,EAAQgC,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/mk/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'помалку од секунда',\n    other: 'помалку од {{count}} секунди'\n  },\n  xSeconds: {\n    one: '1 секунда',\n    other: '{{count}} секунди'\n  },\n  halfAMinute: 'половина минута',\n  lessThanXMinutes: {\n    one: 'помалку од минута',\n    other: 'помалку од {{count}} минути'\n  },\n  xMinutes: {\n    one: '1 минута',\n    other: '{{count}} минути'\n  },\n  aboutXHours: {\n    one: 'околу 1 час',\n    other: 'околу {{count}} часа'\n  },\n  xHours: {\n    one: '1 час',\n    other: '{{count}} часа'\n  },\n  xDays: {\n    one: '1 ден',\n    other: '{{count}} дена'\n  },\n  aboutXWeeks: {\n    one: 'околу 1 недела',\n    other: 'околу {{count}} месеци'\n  },\n  xWeeks: {\n    one: '1 недела',\n    other: '{{count}} недели'\n  },\n  aboutXMonths: {\n    one: 'околу 1 месец',\n    other: 'околу {{count}} недели'\n  },\n  xMonths: {\n    one: '1 месец',\n    other: '{{count}} месеци'\n  },\n  aboutXYears: {\n    one: 'околу 1 година',\n    other: 'околу {{count}} години'\n  },\n  xYears: {\n    one: '1 година',\n    other: '{{count}} години'\n  },\n  overXYears: {\n    one: 'повеќе од 1 година',\n    other: 'повеќе од {{count}} години'\n  },\n  almostXYears: {\n    one: 'безмалку 1 година',\n    other: 'безмалку {{count}} години'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'за ' + result;\n    } else {\n      return 'пред ' + result;\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_default", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison", "module", "default"], "sourceRoot": ""}
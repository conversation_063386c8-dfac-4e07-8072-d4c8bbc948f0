"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[98935],{90694:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d,a=(d=l(19059))&&d.__esModule?d:{default:d};var u={date:(0,a.default)({formats:{full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"d.MM.y"},defaultWidth:"full"}),time:(0,a.default)({formats:{full:"'kl'. HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,a.default)({formats:{full:"{{date}} 'kl.' {{time}}",long:"{{date}} 'kl.' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};t.default=u,e.exports=t.default}}]);
//# sourceMappingURL=98935.dtale_bundle.js.map
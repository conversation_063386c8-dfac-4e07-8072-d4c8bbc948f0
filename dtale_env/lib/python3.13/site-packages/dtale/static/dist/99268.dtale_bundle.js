"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[99268],{35393:(e,t,d)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l,u=(l=d(19059))&&l.__esModule?l:{default:l};var a={date:(0,u.default)({formats:{full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"dd.MM.y"},defaultWidth:"full"}),time:(0,u.default)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,u.default)({formats:{full:"{{date}} 'um' {{time}}",long:"{{date}} 'um' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};t.default=a,e.exports=t.default}}]);
//# sourceMappingURL=99268.dtale_bundle.js.map
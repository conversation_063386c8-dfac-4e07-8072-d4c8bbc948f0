{"version": 3, "file": "96819.dtale_bundle.js", "mappings": "8FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,SAAU,SAAkBC,GAC1B,IAAIC,EAAMD,EAAKE,YACXC,EAAS,WAQb,OANY,IAARF,GAAqB,IAARA,IAEfE,GAAU,KAGZA,GAAU,eAEZ,EACAC,UAAW,kBACXC,MAAO,cACPC,SAAU,eACVC,SAAU,cACVC,MAAO,KAaLC,EAViB,SAAwBC,EAAOV,EAAMW,EAAWC,GACnE,IAAIC,EAASf,EAAqBY,GAElC,MAAsB,mBAAXG,EACFA,EAAOb,GAGTa,CACT,EAGAjB,EAAA,QAAkBa,EAClBK,EAAOlB,QAAUA,EAAQmB,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/lb/_lib/formatRelative/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var day = date.getUTCDay();\n    var result = \"'läschte\";\n\n    if (day === 2 || day === 4) {\n      // Eifeler Regel: Add an n before the consonant d; Here \"Dënschdeg\" \"and Donneschde\".\n      result += 'n';\n    }\n\n    result += \"' eeee 'um' p\";\n    return result;\n  },\n  yesterday: \"'gëschter um' p\",\n  today: \"'haut um' p\",\n  tomorrow: \"'moien um' p\",\n  nextWeek: \"eeee 'um' p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n\n  if (typeof format === 'function') {\n    return format(date);\n  }\n\n  return format;\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatRelativeLocale", "lastWeek", "date", "day", "getUTCDay", "result", "yesterday", "today", "tomorrow", "nextWeek", "other", "_default", "token", "_baseDate", "_options", "format", "module", "default"], "sourceRoot": ""}
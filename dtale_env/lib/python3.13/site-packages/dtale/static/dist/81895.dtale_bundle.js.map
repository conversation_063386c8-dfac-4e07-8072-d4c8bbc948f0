{"version": 3, "file": "81895.dtale_bundle.js", "mappings": "4EAmCAA,EAAOC,QA5BP,SAAoBC,GAClB,MAAO,CACLC,QAAS,CAAE,SACXC,SAAU,CACR,CACEC,UAAW,OACXC,OAAQ,CAGNC,IAAK,MACLD,OAAQ,CACNC,IAAK,IACLC,YAAa,WAGjBC,SAAU,CACR,CACEC,MAAO,iBAET,CACEA,MAAO,uBAMnB,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/python-repl.js"], "sourcesContent": ["/*\nLanguage: Python REPL\nRequires: python.js\nAuthor: <PERSON> <<EMAIL>>\nCategory: common\n*/\n\nfunction pythonRepl(hljs) {\n  return {\n    aliases: [ 'pycon' ],\n    contains: [\n      {\n        className: 'meta',\n        starts: {\n          // a space separates the REPL prefix from the actual code\n          // this is purely for cleaner HTML output\n          end: / |$/,\n          starts: {\n            end: '$',\n            subLanguage: 'python'\n          }\n        },\n        variants: [\n          {\n            begin: /^>>>(?=[ ]|$)/\n          },\n          {\n            begin: /^\\.\\.\\.(?=[ ]|$)/\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = pythonRepl;\n"], "names": ["module", "exports", "hljs", "aliases", "contains", "className", "starts", "end", "subLanguage", "variants", "begin"], "sourceRoot": ""}
{"version": 3, "file": "78138.dtale_bundle.js", "mappings": "6EAgIAA,EAAOC,QAxHP,SAAaC,GACX,MAAMC,EAAU,CACdC,SAAU,CACRF,EAAKC,QAAQ,KAAM,KACnBD,EAAKC,QACH,MACA,MACA,CACEE,SAAU,CAAC,YAMbC,EAAc,CAClBC,UAAW,OACXC,MAAO,kBACPC,UAAW,GAGPC,EAAO,CACXF,MAAO,MACPG,IAAK,MACLC,QAAS,IACTP,SAAU,CACR,CACEE,UAAW,OACXC,MAAO,0CAETL,IAiBJ,MAAO,CACLU,KAAM,MACNC,SACE,8HAEFT,SAAU,CAIR,CACEU,cAAe,qBACfJ,IAAK,WACLG,SAAU,yDACVT,SAAU,CACRK,EACAP,GAEFS,QAAS,YAEX,CACEJ,MAAO,SACPG,IAAK,IACLG,SAAU,qBACVT,SAAU,CACRK,EACAP,GAEFS,QAAS,YAEX,CACEJ,MAAO,OACPG,IAAK,IACLG,SAAU,aACVT,SAAU,CACRC,EACAI,EAhDO,CACbF,MAAO,KACPG,IAAK,KACLN,SAAUK,EAAKL,UA+CTF,IAGJ,CACEY,cAAe,sBACfJ,IAAK,IACLN,SAAU,CACRH,EAAKc,cACLb,IAGJ,CACEK,MAAO,OACPG,IAAK,IACLG,SAAU,OACVT,SAAU,CAACF,IA3DC,CAChBI,UAAW,SACXC,MAAO,UACPG,IAAK,IACLC,QAAS,KA6DPV,EAAKe,kBACLf,EAAKc,cACLV,EACAJ,EAAKgB,QAAQhB,EAAKiB,WAAY,CAC5BX,MAAO,mBAETL,EAEA,CACEK,MAAO,UAGXI,QAAS,IAEb,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/elm.js"], "sourcesContent": ["/*\nLanguage: Elm\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://elm-lang.org\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction elm(hljs) {\n  const COMMENT = {\n    variants: [\n      hljs.COMMENT('--', '$'),\n      hljs.COMMENT(\n        /\\{-/,\n        /-\\}/,\n        {\n          contains: ['self']\n        }\n      )\n    ]\n  };\n\n  const CONSTRUCTOR = {\n    className: 'type',\n    begin: '\\\\b[A-Z][\\\\w\\']*', // TODO: other constructors (built-in, infix).\n    relevance: 0\n  };\n\n  const LIST = {\n    begin: '\\\\(',\n    end: '\\\\)',\n    illegal: '\"',\n    contains: [\n      {\n        className: 'type',\n        begin: '\\\\b[A-Z][\\\\w]*(\\\\((\\\\.\\\\.|,|\\\\w+)\\\\))?'\n      },\n      COMMENT\n    ]\n  };\n\n  const RECORD = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: LIST.contains\n  };\n\n  const CHARACTER = {\n    className: 'string',\n    begin: '\\'\\\\\\\\?.',\n    end: '\\'',\n    illegal: '.'\n  };\n\n  return {\n    name: 'Elm',\n    keywords:\n      'let in if then else case of where module import exposing ' +\n      'type alias as infix infixl infixr port effect command subscription',\n    contains: [\n\n      // Top-level constructions.\n\n      {\n        beginKeywords: 'port effect module',\n        end: 'exposing',\n        keywords: 'port effect module where command subscription exposing',\n        contains: [\n          LIST,\n          COMMENT\n        ],\n        illegal: '\\\\W\\\\.|;'\n      },\n      {\n        begin: 'import',\n        end: '$',\n        keywords: 'import as exposing',\n        contains: [\n          LIST,\n          COMMENT\n        ],\n        illegal: '\\\\W\\\\.|;'\n      },\n      {\n        begin: 'type',\n        end: '$',\n        keywords: 'type alias',\n        contains: [\n          CONSTRUCTOR,\n          LIST,\n          RECORD,\n          COMMENT\n        ]\n      },\n      {\n        beginKeywords: 'infix infixl infixr',\n        end: '$',\n        contains: [\n          hljs.C_NUMBER_MODE,\n          COMMENT\n        ]\n      },\n      {\n        begin: 'port',\n        end: '$',\n        keywords: 'port',\n        contains: [COMMENT]\n      },\n\n      // Literals and names.\n\n      CHARACTER,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      CONSTRUCTOR,\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: '^[_a-z][\\\\w\\']*'\n      }),\n      COMMENT,\n\n      {\n        begin: '->|<-'\n      } // No markup, relevance booster\n    ],\n    illegal: /;/\n  };\n}\n\nmodule.exports = elm;\n"], "names": ["module", "exports", "hljs", "COMMENT", "variants", "contains", "CONSTRUCTOR", "className", "begin", "relevance", "LIST", "end", "illegal", "name", "keywords", "beginKeywords", "C_NUMBER_MODE", "QUOTE_STRING_MODE", "inherit", "TITLE_MODE"], "sourceRoot": ""}
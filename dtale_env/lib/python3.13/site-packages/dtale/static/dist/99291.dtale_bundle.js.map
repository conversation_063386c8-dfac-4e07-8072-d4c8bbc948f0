{"version": 3, "file": "99291.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAqJII,EA9BW,CACbC,cA7BkB,SAAuBC,EAAaC,GACtD,IAAIC,EAAOC,OAAOF,aAAyC,EAASA,EAAQC,MACxEE,EAASC,OAAOL,GAuBpB,OAAOI,GARM,SAATF,EACO,MACS,SAATA,GAA4B,WAATA,GAA8B,WAATA,EACxC,KAECE,EAAS,IAAO,GAAKA,EAAS,IAAO,GAAMA,EAAS,KAAQ,IAAMA,EAAS,KAAQ,GAAY,KAAP,KAItG,EAIEE,KAAK,EAAIX,EAAOE,SAAS,CACvBU,OA1HY,CACdC,OAAQ,CAAC,UAAW,QACpBC,YAAa,CAAC,WAAY,SAC1BC,KAAM,CAAC,eAAgB,cAwHrBC,aAAc,SAEhBC,SAAS,EAAIjB,EAAOE,SAAS,CAC3BU,OAzHgB,CAClBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,UAAW,UAAW,UAAW,WAC/CC,KAAM,CAAC,cAAe,cAAe,cAAe,gBAuHlDC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAInB,EAAOE,SAAS,CACzBU,OA3Hc,CAChBC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,SAAU,OAAQ,OAAQ,QAAS,QAAS,QAAS,OAAQ,MAAO,OAAQ,SAAU,QAAS,UAC7GC,KAAM,CAAC,WAAY,OAAQ,UAAW,WAAY,UAAW,UAAW,SAAU,UAAW,WAAY,aAAc,WAAY,aAyHjIC,aAAc,OACdI,iBAxHwB,CAC1BP,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,SAAU,OAAQ,OAAQ,QAAS,QAAS,QAAS,OAAQ,MAAO,OAAQ,SAAU,QAAS,UAC7GC,KAAM,CAAC,WAAY,SAAU,WAAY,YAAa,SAAU,UAAW,SAAU,SAAU,UAAW,cAAe,YAAa,YAsHpIM,uBAAwB,SAE1BC,KAAK,EAAItB,EAAOE,SAAS,CACvBU,OAvHY,CACdC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCU,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CT,YAAa,CAAC,OAAQ,MAAO,MAAO,MAAO,OAAQ,MAAO,OAC1DC,KAAM,CAAC,UAAW,aAAc,UAAW,SAAU,UAAW,UAAW,WAoHzEC,aAAc,SAEhBQ,WAAW,EAAIxB,EAAOE,SAAS,CAC7BU,OArHkB,CACpBC,OAAQ,CACNY,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,QACNC,QAAS,OACTC,UAAW,QACXC,QAAS,OACTC,MAAO,OAETlB,YAAa,CACXW,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,QACNC,QAAS,OACTC,UAAW,QACXC,QAAS,OACTC,MAAO,OAETjB,KAAM,CACJU,GAAI,KACJC,GAAI,KACJC,SAAU,SACVC,KAAM,WACNC,QAAS,SACTC,UAAW,QACXC,QAAS,QACTC,MAAO,QAyFPhB,aAAc,MACdI,iBAvF4B,CAC9BP,OAAQ,CACNY,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,QACNC,QAAS,OACTC,UAAW,MACXC,QAAS,OACTC,MAAO,QAETlB,YAAa,CACXW,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,QACNC,QAAS,OACTC,UAAW,MACXC,QAAS,OACTC,MAAO,QAETjB,KAAM,CACJU,GAAI,KACJC,GAAI,KACJC,SAAU,SACVC,KAAM,WACNC,QAAS,SACTC,UAAW,MACXC,QAAS,SACTC,MAAO,SA2DPX,uBAAwB,UAI5BxB,EAAA,QAAkBM,EAClB8B,EAAOpC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/be-tarask/_lib/localize/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['да н.э.', 'н.э.'],\n  abbreviated: ['да н. э.', 'н. э.'],\n  wide: ['да нашай эры', 'нашай эры']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-ы кв.', '2-і кв.', '3-і кв.', '4-ы кв.'],\n  wide: ['1-ы квартал', '2-і квартал', '3-і квартал', '4-ы квартал']\n};\nvar monthValues = {\n  narrow: ['С', 'Л', 'С', 'К', 'Т', 'Ч', 'Л', 'Ж', 'В', 'К', 'Л', 'С'],\n  abbreviated: ['студз.', 'лют.', 'сак.', 'крас.', 'трав.', 'чэрв.', 'ліп.', 'жн.', 'вер.', 'кастр.', 'ліст.', 'сьнеж.'],\n  wide: ['студзень', 'люты', 'сакавік', 'красавік', 'травень', 'чэрвень', 'ліпень', 'жнівень', 'верасень', 'кастрычнік', 'лістапад', 'сьнежань']\n};\nvar formattingMonthValues = {\n  narrow: ['С', 'Л', 'С', 'К', 'Т', 'Ч', 'Л', 'Ж', 'В', 'К', 'Л', 'С'],\n  abbreviated: ['студз.', 'лют.', 'сак.', 'крас.', 'трав.', 'чэрв.', 'ліп.', 'жн.', 'вер.', 'кастр.', 'ліст.', 'сьнеж.'],\n  wide: ['студзеня', 'лютага', 'сакавіка', 'красавіка', 'траўня', 'чэрвеня', 'ліпеня', 'жніўня', 'верасня', 'кастрычніка', 'лістапада', 'сьнежня']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'А', 'С', 'Ч', 'П', 'С'],\n  short: ['нд', 'пн', 'аў', 'ср', 'чц', 'пт', 'сб'],\n  abbreviated: ['нядз', 'пан', 'аўт', 'сер', 'чаць', 'пят', 'суб'],\n  wide: ['нядзеля', 'панядзелак', 'аўторак', 'серада', 'чацьвер', 'пятніца', 'субота']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўн.',\n    noon: 'поўд.',\n    morning: 'ран.',\n    afternoon: 'дзень',\n    evening: 'веч.',\n    night: 'ноч'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўн.',\n    noon: 'поўд.',\n    morning: 'ран.',\n    afternoon: 'дзень',\n    evening: 'веч.',\n    night: 'ноч'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўнач',\n    noon: 'поўдзень',\n    morning: 'раніца',\n    afternoon: 'дзень',\n    evening: 'вечар',\n    night: 'ноч'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўн.',\n    noon: 'поўд.',\n    morning: 'ран.',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночы'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўн.',\n    noon: 'поўд.',\n    morning: 'ран.',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночы'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'поўнач',\n    noon: 'поўдзень',\n    morning: 'раніцы',\n    afternoon: 'дня',\n    evening: 'вечара',\n    night: 'ночы'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  var number = Number(dirtyNumber);\n  var suffix;\n  /** Though it's an incorrect ordinal form of a date we use it here for consistency with other similar locales (ru, uk)\n   *  For date-month combinations should be used `d` formatter.\n   *  Correct:   `d MMMM` (4 верасня)\n   *  Incorrect: `do MMMM` (4-га верасня)\n   *\n   *  But following the consistency leads to mistakes for literal uses of `do` formatter (ordinal day of month).\n   *  So for phrase \"5th day of month\" (`do дзень месяца`)\n   *  library will produce:            `5-га дзень месяца`\n   *  but correct spelling should be:  `5-ы дзень месяца`\n   *\n   *  So I guess there should be a stand-alone and a formatting version of \"day of month\" formatters\n   */\n\n  if (unit === 'date') {\n    suffix = '-га';\n  } else if (unit === 'hour' || unit === 'minute' || unit === 'second') {\n    suffix = '-я';\n  } else {\n    suffix = (number % 10 === 2 || number % 10 === 3) && number % 100 !== 12 && number % 100 !== 13 ? '-і' : '-ы';\n  }\n\n  return number + suffix;\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "options", "unit", "String", "number", "Number", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "module"], "sourceRoot": ""}
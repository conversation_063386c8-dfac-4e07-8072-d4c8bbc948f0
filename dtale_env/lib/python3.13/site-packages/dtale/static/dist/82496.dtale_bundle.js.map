{"version": 3, "file": "82496.dtale_bundle.js", "mappings": "6EAoBA,SAASA,KAAUC,GAEjB,OADeA,EAAKC,IAAKC,IAAMC,OAZjBC,EAYwBF,GAVpB,iBAAPE,EAAwBA,EAE5BA,EAAGD,OAHM,KADlB,IAAgBC,IAY4BC,KAAK,GAEjD,CA8DAC,EAAOC,QApDP,SAAoBC,GAClB,MAAO,CACLC,KAAM,cACNC,SAAU,CACRC,SACE,wBACFC,QACE,+IAGJC,SAAU,CACR,CACEC,UAAW,OACXC,MAAO,YACPC,UAAW,IAEbR,EAAKS,QAAQ,IAAK,KAClB,CACEH,UAAW,SACXC,MAAO,gGACPC,UAAW,GAEbR,EAAKU,iBACLV,EAAKW,kBACL,CACEJ,MAAOhB,EACL,UACA,aACA,oBAGJ,CACEgB,MAAO,MAET,CACEA,MAAO,MAET,CACEA,MAAO,KAET,CACEA,MAAO,2EACPC,UAAW,GAEb,CACED,MAAO,sBACPC,UAAW,IAInB,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/erlang-repl.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Erlang REPL\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.erlang.org\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction erlangRepl(hljs) {\n  return {\n    name: 'Erlang REPL',\n    keywords: {\n      built_in:\n        'spawn spawn_link self',\n      keyword:\n        'after and andalso|10 band begin bnot bor bsl bsr bxor case catch cond div end fun if ' +\n        'let not of or orelse|10 query receive rem try when xor'\n    },\n    contains: [\n      {\n        className: 'meta',\n        begin: '^[0-9]+> ',\n        relevance: 10\n      },\n      hljs.COMMENT('%', '$'),\n      {\n        className: 'number',\n        begin: '\\\\b(\\\\d+(_\\\\d+)*#[a-fA-F0-9]+(_[a-fA-F0-9]+)*|\\\\d+(_\\\\d+)*(\\\\.\\\\d+(_\\\\d+)*)?([eE][-+]?\\\\d+)?)',\n        relevance: 0\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        begin: concat(\n          /\\?(::)?/,\n          /([A-Z]\\w*)/, // at least one identifier\n          /((::)[A-Z]\\w*)*/ // perhaps more\n        )\n      },\n      {\n        begin: '->'\n      },\n      {\n        begin: 'ok'\n      },\n      {\n        begin: '!'\n      },\n      {\n        begin: '(\\\\b[a-z\\'][a-zA-Z0-9_\\']*:[a-z\\'][a-zA-Z0-9_\\']*)|(\\\\b[a-z\\'][a-zA-Z0-9_\\']*)',\n        relevance: 0\n      },\n      {\n        begin: '[A-Z][a-zA-Z0-9_\\']*',\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = erlangRepl;\n"], "names": ["concat", "args", "map", "x", "source", "re", "join", "module", "exports", "hljs", "name", "keywords", "built_in", "keyword", "contains", "className", "begin", "relevance", "COMMENT", "APOS_STRING_MODE", "QUOTE_STRING_MODE"], "sourceRoot": ""}
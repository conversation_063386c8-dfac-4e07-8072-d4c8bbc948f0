{"version": 3, "file": "82544.dtale_bundle.js", "mappings": "6HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAgGII,EA1BW,CACbC,cAjBkB,SAAuBC,EAAaC,GACtD,IAAIC,EAASC,OAAOH,GAChBI,EAAOH,aAAyC,EAASA,EAAQG,KACrE,GAAe,IAAXF,EAAc,MAAO,IAUzB,OAAOA,GANQ,IAAXA,EACOE,GAJS,CAAC,OAAQ,OAAQ,OAAQ,SAAU,UAItBC,SAASD,GAAQ,MAAQ,KAE/C,MAIb,EAIEE,KAAK,EAAIX,EAAOE,SAAS,CACvBU,OAzEY,CACdC,OAAQ,CAAC,WAAY,YACrBC,YAAa,CAAC,WAAY,YAC1BC,KAAM,CAAC,qBAAsB,uBAuE3BC,aAAc,SAEhBC,SAAS,EAAIjB,EAAOE,SAAS,CAC3BU,OAxEgB,CAClBC,OAAQ,CAAC,KAAM,KAAM,KAAM,MAC3BC,YAAa,CAAC,YAAa,aAAc,aAAc,cACvDC,KAAM,CAAC,gBAAiB,iBAAkB,iBAAkB,mBAsE1DC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAInB,EAAOE,SAAS,CACzBU,OA1Ec,CAChBC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,QAAS,QAAS,OAAQ,OAAQ,MAAO,OAAQ,QAAS,OAAQ,QAAS,OAAQ,OAAQ,QACzGC,KAAM,CAAC,UAAW,UAAW,OAAQ,QAAS,MAAO,OAAQ,UAAW,OAAQ,YAAa,UAAW,WAAY,aAwElHC,aAAc,SAEhBI,KAAK,EAAIpB,EAAOE,SAAS,CACvBU,OAzEY,CACdC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCQ,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CP,YAAa,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QAC9DC,KAAM,CAAC,WAAY,QAAS,QAAS,WAAY,QAAS,WAAY,WAsEpEC,aAAc,SAEhBM,WAAW,EAAItB,EAAOE,SAAS,CAC7BU,OAvEkB,CACpBC,OAAQ,CACNU,GAAI,KACJC,GAAI,KACJC,SAAU,SACVC,KAAM,OACNC,QAAS,OACTC,UAAW,QACXC,QAAS,OACTC,MAAO,QAEThB,YAAa,CACXS,GAAI,KACJC,GAAI,KACJC,SAAU,SACVC,KAAM,OACNC,QAAS,QACTC,UAAW,aACXC,QAAS,OACTC,MAAO,SAETf,KAAM,CACJQ,GAAI,KACJC,GAAI,KACJC,SAAU,SACVC,KAAM,OACNC,QAAS,WACTC,UAAW,kBACXC,QAAS,UACTC,MAAO,aA2CPd,aAAc,UAIlBnB,EAAA,QAAkBM,EAClB4B,EAAOlC,QAAUA,EAAQK,O,kBC3GzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAgCII,EAda,CACf6B,MAAM,EAAIhC,EAAOE,SAAS,CACxB+B,QApBc,CAChBC,KAAM,gBACNC,KAAM,WACNC,OAAQ,UACRf,MAAO,WAiBLL,aAAc,SAEhBqB,MAAM,EAAIrC,EAAOE,SAAS,CACxB+B,QAlBc,CAChBC,KAAM,gBACNC,KAAM,aACNC,OAAQ,WACRf,MAAO,SAeLL,aAAc,SAEhBsB,UAAU,EAAItC,EAAOE,SAAS,CAC5B+B,QAhBkB,CACpBC,KAAM,wBACNC,KAAM,wBACNC,OAAQ,qBACRf,MAAO,sBAaLL,aAAc,UAIlBnB,EAAA,QAAkBM,EAClB4B,EAAOlC,QAAUA,EAAQK,O,gBC3CzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAI0C,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,sBACLC,MAAO,+BAETC,SAAU,CACRF,IAAK,YACLC,MAAO,sBAETE,YAAa,cACbC,iBAAkB,CAChBJ,IAAK,qBACLC,MAAO,8BAETI,SAAU,CACRL,IAAK,WACLC,MAAO,qBAETK,YAAa,CACXN,IAAK,kBACLC,MAAO,4BAETM,OAAQ,CACNP,IAAK,UACLC,MAAO,oBAETO,MAAO,CACLR,IAAK,SACLC,MAAO,mBAETQ,YAAa,CACXT,IAAK,oBACLC,MAAO,8BAETS,OAAQ,CACNV,IAAK,YACLC,MAAO,sBAETU,aAAc,CACZX,IAAK,iBACLC,MAAO,0BAETW,QAAS,CACPZ,IAAK,SACLC,MAAO,kBAETY,YAAa,CACXb,IAAK,eACLC,MAAO,yBAETa,OAAQ,CACNd,IAAK,OACLC,MAAO,iBAETc,WAAY,CACVf,IAAK,eACLC,MAAO,yBAETe,aAAc,CACZhB,IAAK,eACLC,MAAO,0BA2BPvC,EAvBiB,SAAwBuD,EAAOC,EAAOrD,GACzD,IAAIsD,EACAC,EAAOtB,EAAqBmB,GAUhC,OAPEE,EADkB,iBAATC,EACAA,EACU,IAAVF,EACAE,EAAKpB,IAELoB,EAAKnB,MAAMoB,QAAQ,YAAaC,OAAOJ,IAG9CrD,SAA0CA,EAAQ0D,UAChD1D,EAAQ2D,YAAc3D,EAAQ2D,WAAa,EACtC,QAAUL,EAEV,UAAYA,EAIhBA,CACT,EAGA/D,EAAA,QAAkBM,EAClB4B,EAAOlC,QAAUA,EAAQK,O,kBC7FzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAASkE,EAAuB,EAAQ,QAI5C,SAASA,EAAuBnE,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAE9F,IA+FII,EA1CQ,CACVC,eAAe,EA1DH8D,EAAuB,EAAQ,MA0DhBhE,SAAS,CAClCiE,aAvD4B,8BAwD5BC,aAvD4B,OAwD5BC,cAAe,SAAuBvE,GACpC,OAAOwE,SAASxE,EAClB,IAEFa,KAAK,EAAIX,EAAOE,SAAS,CACvBqE,cA5DmB,CACrB1D,OAAQ,kCACRC,YAAa,oDACbC,KAAM,6CA0DJyD,kBAAmB,OACnBC,cAzDmB,CACrBC,IAAK,CAAC,OAAQ,SAyDZC,kBAAmB,QAErB1D,SAAS,EAAIjB,EAAOE,SAAS,CAC3BqE,cA1DuB,CACzB1D,OAAQ,aACRC,YAAa,8BACbC,KAAM,iCAwDJyD,kBAAmB,OACnBC,cAvDuB,CACzBC,IAAK,CAAC,KAAM,KAAM,KAAM,OAuDtBC,kBAAmB,MACnBN,cAAe,SAAuBO,GACpC,OAAOA,EAAQ,CACjB,IAEFzD,OAAO,EAAInB,EAAOE,SAAS,CACzBqE,cA3DqB,CACvB1D,OAAQ,eACRC,YAAa,sEACbC,KAAM,4FAyDJyD,kBAAmB,OACnBC,cAxDqB,CACvB5D,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACtF6D,IAAK,CAAC,OAAQ,MAAO,QAAS,OAAQ,OAAQ,SAAU,SAAU,OAAQ,MAAO,MAAO,MAAO,QAuD7FC,kBAAmB,QAErBvD,KAAK,EAAIpB,EAAOE,SAAS,CACvBqE,cAxDmB,CACrB1D,OAAQ,aACRQ,MAAO,2BACPP,YAAa,qCACbC,KAAM,2DAqDJyD,kBAAmB,OACnBC,cApDmB,CACrB5D,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACnD6D,IAAK,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,SAmDpDC,kBAAmB,QAErBrD,WAAW,EAAItB,EAAOE,SAAS,CAC7BqE,cApDyB,CAC3B1D,OAAQ,iDACR6D,IAAK,sEAmDHF,kBAAmB,MACnBC,cAlDyB,CAC3BC,IAAK,CACHnD,GAAI,MACJC,GAAI,MACJC,SAAU,QACVC,KAAM,QACNC,QAAS,OACTC,UAAW,MACXC,QAAS,QACTC,MAAO,UA0CP6C,kBAAmB,SAIvB9E,EAAA,QAAkBM,EAClB4B,EAAOlC,QAAUA,EAAQK,O,gBC5GzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIgF,EAAuB,CACzBC,SAAU,qBACVC,UAAW,aACXC,MAAO,oBACPC,SAAU,gBACVC,SAAU,sBACVxC,MAAO,KAOLvC,EAJiB,SAAwBuD,EAAOyB,EAAOC,EAAWC,GACpE,OAAOR,EAAqBnB,EAC9B,EAGA7D,EAAA,QAAkBM,EAClB4B,EAAOlC,QAAUA,EAAQK,O,kBCnBzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAASkE,EAAuB,EAAQ,QAExCoB,EAAUpB,EAAuB,EAAQ,QAEzCqB,EAAUrB,EAAuB,EAAQ,QAEzCsB,EAAUtB,EAAuB,EAAQ,OAEzCuB,EAAUvB,EAAuB,EAAQ,QAE7C,SAASA,EAAuBnE,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAW9F,IAcII,EAdS,CACXuF,KAAM,KACNC,eAAgB3F,EAAOE,QACvB0F,WAAYN,EAAQpF,QACpB2F,eAAgBN,EAAQrF,QACxB4F,SAAUN,EAAQtF,QAClB6F,MAAON,EAAQvF,QACfI,QAAS,CACP0F,aAAc,EAGdC,sBAAuB,IAI3BpG,EAAA,QAAkBM,EAClB4B,EAAOlC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/fr/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/fr/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/fr/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/fr/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/fr/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/locale/fr/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['av. J.-C', 'ap. J.-C'],\n  abbreviated: ['av. J.-C', 'ap. J.-C'],\n  wide: ['avant J<PERSON><PERSON>-Christ', 'apr<PERSON>-Christ']\n};\nvar quarterValues = {\n  narrow: ['T1', 'T2', 'T3', 'T4'],\n  abbreviated: ['1er trim.', '2ème trim.', '3ème trim.', '4ème trim.'],\n  wide: ['1er trimestre', '2ème trimestre', '3ème trimestre', '4ème trimestre']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['janv.', 'févr.', 'mars', 'avr.', 'mai', 'juin', 'juil.', 'août', 'sept.', 'oct.', 'nov.', 'déc.'],\n  wide: ['janvier', 'février', 'mars', 'avril', 'mai', 'juin', 'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre']\n};\nvar dayValues = {\n  narrow: ['D', 'L', 'M', 'M', 'J', 'V', 'S'],\n  short: ['di', 'lu', 'ma', 'me', 'je', 've', 'sa'],\n  abbreviated: ['dim.', 'lun.', 'mar.', 'mer.', 'jeu.', 'ven.', 'sam.'],\n  wide: ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'minuit',\n    noon: 'midi',\n    morning: 'mat.',\n    afternoon: 'ap.m.',\n    evening: 'soir',\n    night: 'mat.'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'minuit',\n    noon: 'midi',\n    morning: 'matin',\n    afternoon: 'après-midi',\n    evening: 'soir',\n    night: 'matin'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'minuit',\n    noon: 'midi',\n    morning: 'du matin',\n    afternoon: 'de l’après-midi',\n    evening: 'du soir',\n    night: 'du matin'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  if (number === 0) return '0';\n  var feminineUnits = ['year', 'week', 'hour', 'minute', 'second'];\n  var suffix;\n\n  if (number === 1) {\n    suffix = unit && feminineUnits.includes(unit) ? 'ère' : 'er';\n  } else {\n    suffix = 'ème';\n  }\n\n  return number + suffix;\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'EEEE d MMMM y',\n  long: 'd MMMM y',\n  medium: 'd MMM y',\n  short: 'dd/MM/y'\n};\nvar timeFormats = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'à' {{time}}\",\n  long: \"{{date}} 'à' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'moins d’une seconde',\n    other: 'moins de {{count}} secondes'\n  },\n  xSeconds: {\n    one: '1 seconde',\n    other: '{{count}} secondes'\n  },\n  halfAMinute: '30 secondes',\n  lessThanXMinutes: {\n    one: 'moins d’une minute',\n    other: 'moins de {{count}} minutes'\n  },\n  xMinutes: {\n    one: '1 minute',\n    other: '{{count}} minutes'\n  },\n  aboutXHours: {\n    one: 'environ 1 heure',\n    other: 'environ {{count}} heures'\n  },\n  xHours: {\n    one: '1 heure',\n    other: '{{count}} heures'\n  },\n  xDays: {\n    one: '1 jour',\n    other: '{{count}} jours'\n  },\n  aboutXWeeks: {\n    one: 'environ 1 semaine',\n    other: 'environ {{count}} semaines'\n  },\n  xWeeks: {\n    one: '1 semaine',\n    other: '{{count}} semaines'\n  },\n  aboutXMonths: {\n    one: 'environ 1 mois',\n    other: 'environ {{count}} mois'\n  },\n  xMonths: {\n    one: '1 mois',\n    other: '{{count}} mois'\n  },\n  aboutXYears: {\n    one: 'environ 1 an',\n    other: 'environ {{count}} ans'\n  },\n  xYears: {\n    one: '1 an',\n    other: '{{count}} ans'\n  },\n  overXYears: {\n    one: 'plus d’un an',\n    other: 'plus de {{count}} ans'\n  },\n  almostXYears: {\n    one: 'presqu’un an',\n    other: 'presque {{count}} ans'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var form = formatDistanceLocale[token];\n\n  if (typeof form === 'string') {\n    result = form;\n  } else if (count === 1) {\n    result = form.one;\n  } else {\n    result = form.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'dans ' + result;\n    } else {\n      return 'il y a ' + result;\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)(ième|ère|ème|er|e)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(av\\.J\\.C|ap\\.J\\.C|ap\\.J\\.-C)/i,\n  abbreviated: /^(av\\.J\\.-C|av\\.J-C|apr\\.J\\.-C|apr\\.J-C|ap\\.J-C)/i,\n  wide: /^(avant J<PERSON><PERSON>-Christ|apr<PERSON>)/i\n};\nvar parseEraPatterns = {\n  any: [/^av/i, /^ap/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^T?[1234]/i,\n  abbreviated: /^[1234](er|ème|e)? trim\\.?/i,\n  wide: /^[1234](er|ème|e)? trimestre/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\\.?/i,\n  wide: /^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^mar/i, /^av/i, /^ma/i, /^juin/i, /^juil/i, /^ao/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[lmjvsd]/i,\n  short: /^(di|lu|ma|me|je|ve|sa)/i,\n  abbreviated: /^(dim|lun|mar|mer|jeu|ven|sam)\\.?/i,\n  wide: /^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^d/i, /^l/i, /^m/i, /^m/i, /^j/i, /^v/i, /^s/i],\n  any: [/^di/i, /^lu/i, /^ma/i, /^me/i, /^je/i, /^ve/i, /^sa/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(a|p|minuit|midi|mat\\.?|ap\\.?m\\.?|soir|nuit)/i,\n  any: /^([ap]\\.?\\s?m\\.?|du matin|de l'après[-\\s]midi|du soir|de la nuit)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^min/i,\n    noon: /^mid/i,\n    morning: /mat/i,\n    afternoon: /ap/i,\n    evening: /soir/i,\n    night: /nuit/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"eeee 'dernier à' p\",\n  yesterday: \"'hier à' p\",\n  today: \"'aujourd’hui à' p\",\n  tomorrow: \"'demain à' p'\",\n  nextWeek: \"eeee 'prochain à' p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary French locale.\n * @language French\n * @iso-639-2 fra\n * <AUTHOR> [@izeau]{@link https://github.com/izeau}\n * <AUTHOR> [@fbonzon]{@link https://github.com/fbonzon}\n */\nvar locale = {\n  code: 'fr',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 1\n    /* Monday */\n    ,\n    firstWeekContainsDate: 4\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "includes", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "module", "date", "formats", "full", "long", "medium", "time", "dateTime", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "token", "count", "result", "form", "replace", "String", "addSuffix", "comparison", "_interopRequireDefault", "matchPattern", "parsePattern", "valueCallback", "parseInt", "matchPatterns", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "index", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "_date", "_baseDate", "_options", "_index2", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "weekStartsOn", "firstWeekContainsDate"], "sourceRoot": ""}
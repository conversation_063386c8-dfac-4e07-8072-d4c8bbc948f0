{"version": 3, "file": "85773.dtale_bundle.js", "mappings": "6EAmCAA,EAAOC,QA3BP,SAAqBC,GACnB,MAAO,CACLC,KAAM,cACNC,YAAa,MACbC,SAAU,CACR,CACEC,UAAW,OACXC,MAAO,oBAGT,CACEA,MAAO,oBACPC,IAAK,IACLJ,YAAa,QAGf,CACEG,MAAO,gBACPC,IAAK,WACLJ,YAAa,OACbK,cAAc,EACdC,YAAY,IAIpB,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/mojolicious.js"], "sourcesContent": ["/*\nLanguage: Mojolicious\nRequires: xml.js, perl.js\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Mojolicious .ep (Embedded Perl) templates\nWebsite: https://mojolicious.org\nCategory: template\n*/\nfunction mojolicious(hljs) {\n  return {\n    name: 'Mojolicious',\n    subLanguage: 'xml',\n    contains: [\n      {\n        className: 'meta',\n        begin: '^__(END|DATA)__$'\n      },\n      // mojolicious line\n      {\n        begin: \"^\\\\s*%{1,2}={0,2}\",\n        end: '$',\n        subLanguage: 'perl'\n      },\n      // mojolicious block\n      {\n        begin: \"<%{1,2}={0,2}\",\n        end: \"={0,1}%>\",\n        subLanguage: 'perl',\n        excludeBegin: true,\n        excludeEnd: true\n      }\n    ]\n  };\n}\n\nmodule.exports = mojolicious;\n"], "names": ["module", "exports", "hljs", "name", "subLanguage", "contains", "className", "begin", "end", "excludeBegin", "excludeEnd"], "sourceRoot": ""}
{"version": 3, "file": "82686.dtale_bundle.js", "mappings": "8FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,SAAU,iBACVC,UAAW,SACXC,MAAO,SACPC,SAAU,SACVC,SAAU,cACVC,MAAO,KAOLC,EAJiB,SAAwBC,EAAOC,EAAOC,EAAWC,GACpE,OAAOX,EAAqBQ,EAC9B,EAGAV,EAAA,QAAkBS,EAClBK,EAAOd,QAAUA,EAAQe,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/hi/_lib/formatRelative/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"'पिछले' eeee p\",\n  yesterday: \"'कल' p\",\n  today: \"'आज' p\",\n  tomorrow: \"'कल' p\",\n  nextWeek: \"eeee 'को' p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "_default", "token", "_date", "_baseDate", "_options", "module", "default"], "sourceRoot": ""}
{"version": 3, "file": "97792.dtale_bundle.js", "mappings": "8FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,SAAU,YACVC,UAAW,OACXC,MAAO,OACPC,SAAU,OACVC,SAAU,YACVC,MAAO,KAOLC,EAJiB,SAAwBC,EAAOC,EAAOC,EAAWC,GACpE,OAAOX,EAAqBQ,EAC9B,EAGAV,EAAA,QAAkBS,EAClBK,EAAOd,QAAUA,EAAQe,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/ja/_lib/formatRelative/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: '先週のeeeeのp',\n  yesterday: '昨日のp',\n  today: '今日のp',\n  tomorrow: '明日のp',\n  nextWeek: '翌週のeeeeのp',\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "_default", "token", "_date", "_baseDate", "_options", "module", "default"], "sourceRoot": ""}
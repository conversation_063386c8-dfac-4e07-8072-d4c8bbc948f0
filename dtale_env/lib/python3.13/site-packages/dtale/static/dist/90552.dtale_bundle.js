"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[20129,34665,79905,83022,90552,93067],{15792:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a(60663));function n(e){return e&&e.__esModule?e:{default:e}}var u={ordinalNumber:(0,n(a(311)).default)({matchPattern:/^(\d+)(-?[врмт][и])?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}}),era:(0,r.default)({matchPatterns:{narrow:/^((пр)?н\.?\s?е\.?)/i,abbreviated:/^((пр)?н\.?\s?е\.?)/i,wide:/^(пред нашата ера|нашата ера)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^п/i,/^н/i]},defaultParseWidth:"any"}),quarter:(0,r.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^[1234](-?[врт]?и?)? кв.?/i,wide:/^[1234](-?[врт]?и?)? квартал/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:(0,r.default)({matchPatterns:{abbreviated:/^(јан|фев|мар|апр|мај|јун|јул|авг|сеп|окт|ноем|дек)/i,wide:/^(јануари|февруари|март|април|мај|јуни|јули|август|септември|октомври|ноември|декември)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^ја/i,/^Ф/i,/^мар/i,/^ап/i,/^мај/i,/^јун/i,/^јул/i,/^ав/i,/^се/i,/^окт/i,/^но/i,/^де/i]},defaultParseWidth:"any"}),day:(0,r.default)({matchPatterns:{narrow:/^[нпвсч]/i,short:/^(не|по|вт|ср|че|пе|са)/i,abbreviated:/^(нед|пон|вто|сре|чет|пет|саб)/i,wide:/^(недела|понеделник|вторник|среда|четврток|петок|сабота)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^н/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н[ед]/i,/^п[он]/i,/^вт/i,/^ср/i,/^ч[ет]/i,/^п[ет]/i,/^с[аб]/i]},defaultParseWidth:"any"}),dayPeriod:(0,r.default)({matchPatterns:{any:/^(претп|попл|полноќ|утро|пладне|вечер|ноќ)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/претпладне/i,pm:/попладне/i,midnight:/полноќ/i,noon:/напладне/i,morning:/наутро/i,afternoon:/попладне/i,evening:/навечер/i,night:/ноќе/i}},defaultParseWidth:"any"})};t.default=u,e.exports=t.default},31458:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,n=(r=a(19059))&&r.__esModule?r:{default:r};var u={date:(0,n.default)({formats:{full:"EEEE, dd MMMM yyyy",long:"dd MMMM yyyy",medium:"dd MMM yyyy",short:"dd/MM/yyyy"},defaultWidth:"full"}),time:(0,n.default)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"H:mm"},defaultWidth:"full"}),dateTime:(0,n.default)({formats:{any:"{{date}} {{time}}"},defaultWidth:"any"})};t.default=u,e.exports=t.default},38536:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,n=(r=a(51545))&&r.__esModule?r:{default:r};var u={ordinalNumber:function(e,t){var a=Number(e),r=a%100;if(r>20||r<10)switch(r%10){case 1:return a+"-ви";case 2:return a+"-ри";case 7:case 8:return a+"-ми"}return a+"-ти"},era:(0,n.default)({values:{narrow:["пр.н.е.","н.е."],abbreviated:["пред н. е.","н. е."],wide:["пред нашата ера","нашата ера"]},defaultWidth:"wide"}),quarter:(0,n.default)({values:{narrow:["1","2","3","4"],abbreviated:["1-ви кв.","2-ри кв.","3-ти кв.","4-ти кв."],wide:["1-ви квартал","2-ри квартал","3-ти квартал","4-ти квартал"]},defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:(0,n.default)({values:{abbreviated:["јан","фев","мар","апр","мај","јун","јул","авг","септ","окт","ноем","дек"],wide:["јануари","февруари","март","април","мај","јуни","јули","август","септември","октомври","ноември","декември"]},defaultWidth:"wide"}),day:(0,n.default)({values:{narrow:["Н","П","В","С","Ч","П","С"],short:["не","по","вт","ср","че","пе","са"],abbreviated:["нед","пон","вто","сре","чет","пет","саб"],wide:["недела","понеделник","вторник","среда","четврток","петок","сабота"]},defaultWidth:"wide"}),dayPeriod:(0,n.default)({values:{wide:{am:"претпладне",pm:"попладне",midnight:"полноќ",noon:"напладне",morning:"наутро",afternoon:"попладне",evening:"навечер",night:"ноќе"}},defaultWidth:"wide"})};t.default=u,e.exports=t.default},52391:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={lessThanXSeconds:{one:"помалку од секунда",other:"помалку од {{count}} секунди"},xSeconds:{one:"1 секунда",other:"{{count}} секунди"},halfAMinute:"половина минута",lessThanXMinutes:{one:"помалку од минута",other:"помалку од {{count}} минути"},xMinutes:{one:"1 минута",other:"{{count}} минути"},aboutXHours:{one:"околу 1 час",other:"околу {{count}} часа"},xHours:{one:"1 час",other:"{{count}} часа"},xDays:{one:"1 ден",other:"{{count}} дена"},aboutXWeeks:{one:"околу 1 недела",other:"околу {{count}} месеци"},xWeeks:{one:"1 недела",other:"{{count}} недели"},aboutXMonths:{one:"околу 1 месец",other:"околу {{count}} недели"},xMonths:{one:"1 месец",other:"{{count}} месеци"},aboutXYears:{one:"околу 1 година",other:"околу {{count}} години"},xYears:{one:"1 година",other:"{{count}} години"},overXYears:{one:"повеќе од 1 година",other:"повеќе од {{count}} години"},almostXYears:{one:"безмалку 1 година",other:"безмалку {{count}} години"}},r=function(e,t,r){var n,u=a[e];return n="string"==typeof u?u:1===t?u.one:u.other.replace("{{count}}",String(t)),null!=r&&r.addSuffix?r.comparison&&r.comparison>0?"за "+n:"пред "+n:n};t.default=r,e.exports=t.default},58648:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,n=(r=a(81713))&&r.__esModule?r:{default:r};var u=["недела","понеделник","вторник","среда","четврток","петок","сабота"];function d(e){var t=u[e];switch(e){case 0:case 3:case 6:return"'ова "+t+" вo' p";case 1:case 2:case 4:case 5:return"'овој "+t+" вo' p"}}var i={lastWeek:function(e,t,a){var r=e.getUTCDay();return(0,n.default)(e,t,a)?d(r):function(e){var t=u[e];switch(e){case 0:case 3:case 6:return"'минатата "+t+" во' p";case 1:case 2:case 4:case 5:return"'минатиот "+t+" во' p"}}(r)},yesterday:"'вчера во' p",today:"'денес во' p",tomorrow:"'утре во' p",nextWeek:function(e,t,a){var r=e.getUTCDay();return(0,n.default)(e,t,a)?d(r):function(e){var t=u[e];switch(e){case 0:case 3:case 6:return"'следната "+t+" вo' p";case 1:case 2:case 4:case 5:return"'следниот "+t+" вo' p"}}(r)},other:"P"},o=function(e,t,a,r){var n=i[e];return"function"==typeof n?n(t,a,r):n};t.default=o,e.exports=t.default},58749:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(a(52391)),n=o(a(31458)),u=o(a(58648)),d=o(a(38536)),i=o(a(15792));function o(e){return e&&e.__esModule?e:{default:e}}var l={code:"mk",formatDistance:r.default,formatLong:n.default,formatRelative:u.default,localize:d.default,match:i.default,options:{weekStartsOn:1,firstWeekContainsDate:4}};t.default=l,e.exports=t.default},81713:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,a){(0,r.default)(2,arguments);var u=(0,n.default)(e,a),d=(0,n.default)(t,a);return u.getTime()===d.getTime()};var r=u(a(10427)),n=u(a(20930));function u(e){return e&&e.__esModule?e:{default:e}}e.exports=t.default}}]);
//# sourceMappingURL=90552.dtale_bundle.js.map
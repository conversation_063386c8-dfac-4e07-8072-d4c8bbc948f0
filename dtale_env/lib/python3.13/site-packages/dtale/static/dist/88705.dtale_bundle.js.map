{"version": 3, "file": "88705.dtale_bundle.js", "mappings": "6EAoCAA,EAAOC,QA5BP,SAAkBC,GAChB,MAAO,CACLC,KAAM,YACNC,SAAU,CACR,CACEC,UAAW,OACXC,OAAQ,CAGNC,IAAK,MACLD,OAAQ,CACNC,IAAK,IACLC,YAAa,eAGjBC,SAAU,CACR,CACEC,MAAO,eAET,CACEA,MAAO,uBAMnB,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/node-repl.js"], "sourcesContent": ["/*\nLanguage: Node REPL\nRequires: javascript.js\nAuthor: <PERSON><PERSON> <<EMAIL>>\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction nodeRepl(hljs) {\n  return {\n    name: 'Node REPL',\n    contains: [\n      {\n        className: 'meta',\n        starts: {\n          // a space separates the REPL prefix from the actual code\n          // this is purely for cleaner HTML output\n          end: / |$/,\n          starts: {\n            end: '$',\n            subLanguage: 'javascript'\n          }\n        },\n        variants: [\n          {\n            begin: /^>(?=[ ]|$)/\n          },\n          {\n            begin: /^\\.\\.\\.(?=[ ]|$)/\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = nodeRepl;\n"], "names": ["module", "exports", "hljs", "name", "contains", "className", "starts", "end", "subLanguage", "variants", "begin"], "sourceRoot": ""}
{"version": 3, "file": "84342.dtale_bundle.js", "mappings": "6EA2MAA,EAAOC,QA/LP,SAAaC,GACX,MAAMC,EAAW,CACfC,UAAW,WACXC,MAAO,0DAKHC,EAAe,CACnBF,UAAW,OACXG,SAAU,CACR,CAAEF,MAAO,SAAUG,UAAW,IAC9B,CAAEH,MAAO,WACT,CAAEA,MAAO,SAGPI,EAAQ,CACZL,UAAW,QACXG,SAAU,CACR,CAAEF,MAAO,SACT,CAAEA,MAAO,OAAQK,IAAK,QAGpBC,EAAgBT,EAAKU,QAAQV,EAAKW,iBAAkB,CACxDC,QAAS,OAELC,EAAgBb,EAAKU,QAAQV,EAAKc,kBAAmB,CACzDF,QAAS,KACTG,SAAUf,EAAKc,kBAAkBC,SAASC,OAAOT,KAE7CU,EAAUjB,EAAKkB,kBAAkB,CACrCf,MAAO,mBACPK,IAAK,gBACLO,SAAUf,EAAKc,kBAAkBC,SAASC,OAAOT,KAE7CY,EAAS,CACbjB,UAAW,SACXa,SAAU,CAACf,EAAKoB,iBAAkBhB,GAClCC,SAAU,CACRL,EAAKU,QAAQD,EAAe,CAC1BN,MAAO,KAAMK,IAAK,MAEpBR,EAAKU,QAAQG,EAAe,CAC1BV,MAAO,KAAMK,IAAK,MAEpBK,EACAJ,EACAQ,IAGEI,EAAS,CACbnB,UAAW,SACXG,SAAU,CACR,CAAEF,MAAO,4BACT,CAAEA,MAAO,8BACT,CAAEA,MAAO,oCAET,CAAEA,MAAO,4EAEXG,UAAW,GAEPgB,EAAW,CACfC,QAGA,umBAcAC,QAAS,kBACTC,SAGA,00CASF,MAAO,CACLC,QAAS,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QAClDC,kBAAkB,EAClBC,SAAUN,EACVP,SAAU,CACRf,EAAK6B,kBACL7B,EAAK8B,QAAQ,KAAM,IAAK,CAACf,SAAU,CAACX,KACpCJ,EAAK8B,QACH,OACA,OACA,CACEf,SAAU,CACR,CACEb,UAAW,SACXC,MAAO,iBAKfH,EAAK8B,QACH,uBACA,EACA,CACEC,gBAAgB,EAChBH,SAAU,oBAGdxB,EACA,CACEF,UAAW,UAAWC,MAAO,YAE/BF,EACA,CAEEE,MAAO,oDAET,CACED,UAAW,WACXI,UAAW,EACX0B,cAAe,cAAexB,IAAK,OAAQyB,YAAY,EACvDrB,QAAS,UACTG,SAAU,CACR,CACEiB,cAAe,OAEjBhC,EAAKkC,sBACL,CACE/B,MAAO,KACPgC,YAAY,GAEd,CACEjC,UAAW,SACXC,MAAO,MAAOK,IAAK,MACnB4B,cAAc,EACdH,YAAY,EACZL,SAAUN,EACVP,SAAU,CACR,OACAd,EACAD,EAAKqC,qBACLlB,EACAE,MAKR,CACEnB,UAAW,QACXG,SAAU,CACR,CAAE2B,cAAe,OAAQpB,QAAS,SAClC,CAAEoB,cAAe,wBAAyBpB,QAAS,WAErDN,UAAW,EACXE,IAAK,KACLyB,YAAY,EACZlB,SAAU,CACR,CAACiB,cAAe,sBAChBhC,EAAKkC,wBAGT,CACEF,cAAe,YACf1B,UAAW,EACXE,IAAK,IACLI,QAAS,OACTG,SAAU,CAACf,EAAKkC,wBAElB,CACEF,cAAe,MACf1B,UAAW,EACXE,IAAK,IACLO,SAAU,CAACf,EAAKkC,wBAElBf,EACAE,GAGN,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/php.js"], "sourcesContent": ["/*\nLanguage: PHP\nAuthor: <PERSON> <<PERSON>.<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://www.php.net\nCategory: common\n*/\n\n/**\n * @param {H<PERSON><PERSON><PERSON><PERSON>} hljs\n * @returns {LanguageDetail}\n * */\nfunction php(hljs) {\n  const VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$+[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*' +\n      // negative look-ahead tries to avoid matching patterns that are not\n      // Perl at all like $ident$, @ident@, etc.\n      `(?![A-Za-z0-9])(?![$])`\n  };\n  const PREPROCESSOR = {\n    className: 'meta',\n    variants: [\n      { begin: /<\\?php/, relevance: 10 }, // boost for obvious PHP\n      { begin: /<\\?[=]?/ },\n      { begin: /\\?>/ } // end php tag\n    ]\n  };\n  const SUBST = {\n    className: 'subst',\n    variants: [\n      { begin: /\\$\\w+/ },\n      { begin: /\\{\\$/, end: /\\}/ }\n    ]\n  };\n  const SINGLE_QUOTED = hljs.inherit(hljs.APOS_STRING_MODE, {\n    illegal: null,\n  });\n  const DOUBLE_QUOTED = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST),\n  });\n  const HEREDOC = hljs.END_SAME_AS_BEGIN({\n    begin: /<<<[ \\t]*(\\w+)\\n/,\n    end: /[ \\t]*(\\w+)\\b/,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST),\n  });\n  const STRING = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE, PREPROCESSOR],\n    variants: [\n      hljs.inherit(SINGLE_QUOTED, {\n        begin: \"b'\", end: \"'\",\n      }),\n      hljs.inherit(DOUBLE_QUOTED, {\n        begin: 'b\"', end: '\"',\n      }),\n      DOUBLE_QUOTED,\n      SINGLE_QUOTED,\n      HEREDOC\n    ]\n  };\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      { begin: `\\\\b0b[01]+(?:_[01]+)*\\\\b` }, // Binary w/ underscore support\n      { begin: `\\\\b0o[0-7]+(?:_[0-7]+)*\\\\b` }, // Octals w/ underscore support\n      { begin: `\\\\b0x[\\\\da-f]+(?:_[\\\\da-f]+)*\\\\b` }, // Hex w/ underscore support\n      // Decimals w/ underscore support, with optional fragments and scientific exponent (e) suffix.\n      { begin: `(?:\\\\b\\\\d+(?:_\\\\d+)*(\\\\.(?:\\\\d+(?:_\\\\d+)*))?|\\\\B\\\\.\\\\d+)(?:e[+-]?\\\\d+)?` }\n    ],\n    relevance: 0\n  };\n  const KEYWORDS = {\n    keyword:\n    // Magic constants:\n    // <https://www.php.net/manual/en/language.constants.predefined.php>\n    '__CLASS__ __DIR__ __FILE__ __FUNCTION__ __LINE__ __METHOD__ __NAMESPACE__ __TRAIT__ ' +\n    // Function that look like language construct or language construct that look like function:\n    // List of keywords that may not require parenthesis\n    'die echo exit include include_once print require require_once ' +\n    // These are not language construct (function) but operate on the currently-executing function and can access the current symbol table\n    // 'compact extract func_get_arg func_get_args func_num_args get_called_class get_parent_class ' +\n    // Other keywords:\n    // <https://www.php.net/manual/en/reserved.php>\n    // <https://www.php.net/manual/en/language.types.type-juggling.php>\n    'array abstract and as binary bool boolean break callable case catch class clone const continue declare ' +\n    'default do double else elseif empty enddeclare endfor endforeach endif endswitch endwhile enum eval extends ' +\n    'final finally float for foreach from global goto if implements instanceof insteadof int integer interface ' +\n    'isset iterable list match|0 mixed new object or private protected public real return string switch throw trait ' +\n    'try unset use var void while xor yield',\n    literal: 'false null true',\n    built_in:\n    // Standard PHP library:\n    // <https://www.php.net/manual/en/book.spl.php>\n    'Error|0 ' + // error is too common a name esp since PHP is case in-sensitive\n    'AppendIterator ArgumentCountError ArithmeticError ArrayIterator ArrayObject AssertionError BadFunctionCallException BadMethodCallException CachingIterator CallbackFilterIterator CompileError Countable DirectoryIterator DivisionByZeroError DomainException EmptyIterator ErrorException Exception FilesystemIterator FilterIterator GlobIterator InfiniteIterator InvalidArgumentException IteratorIterator LengthException LimitIterator LogicException MultipleIterator NoRewindIterator OutOfBoundsException OutOfRangeException OuterIterator OverflowException ParentIterator ParseError RangeException RecursiveArrayIterator RecursiveCachingIterator RecursiveCallbackFilterIterator RecursiveDirectoryIterator RecursiveFilterIterator RecursiveIterator RecursiveIteratorIterator RecursiveRegexIterator RecursiveTreeIterator RegexIterator RuntimeException SeekableIterator SplDoublyLinkedList SplFileInfo SplFileObject SplFixedArray SplHeap SplMaxHeap SplMinHeap SplObjectStorage SplObserver SplObserver SplPriorityQueue SplQueue SplStack SplSubject SplSubject SplTempFileObject TypeError UnderflowException UnexpectedValueException UnhandledMatchError ' +\n    // Reserved interfaces:\n    // <https://www.php.net/manual/en/reserved.interfaces.php>\n    'ArrayAccess Closure Generator Iterator IteratorAggregate Serializable Stringable Throwable Traversable WeakReference WeakMap ' +\n    // Reserved classes:\n    // <https://www.php.net/manual/en/reserved.classes.php>\n    'Directory __PHP_Incomplete_Class parent php_user_filter self static stdClass'\n  };\n  return {\n    aliases: ['php3', 'php4', 'php5', 'php6', 'php7', 'php8'],\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.COMMENT('//', '$', {contains: [PREPROCESSOR]}),\n      hljs.COMMENT(\n        '/\\\\*',\n        '\\\\*/',\n        {\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            }\n          ]\n        }\n      ),\n      hljs.COMMENT(\n        '__halt_compiler.+?;',\n        false,\n        {\n          endsWithParent: true,\n          keywords: '__halt_compiler'\n        }\n      ),\n      PREPROCESSOR,\n      {\n        className: 'keyword', begin: /\\$this\\b/\n      },\n      VARIABLE,\n      {\n        // swallow composed identifiers to avoid parsing them as keywords\n        begin: /(::|->)+[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*/\n      },\n      {\n        className: 'function',\n        relevance: 0,\n        beginKeywords: 'fn function', end: /[;{]/, excludeEnd: true,\n        illegal: '[$%\\\\[]',\n        contains: [\n          {\n            beginKeywords: 'use',\n          },\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            begin: '=>', // No markup, just a relevance booster\n            endsParent: true\n          },\n          {\n            className: 'params',\n            begin: '\\\\(', end: '\\\\)',\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            contains: [\n              'self',\n              VARIABLE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRING,\n              NUMBER\n            ]\n          }\n        ]\n      },\n      {\n        className: 'class',\n        variants: [\n          { beginKeywords: \"enum\", illegal: /[($\"]/ },\n          { beginKeywords: \"class interface trait\", illegal: /[:($\"]/ }\n        ],\n        relevance: 0,\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          {beginKeywords: 'extends implements'},\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: ';',\n        illegal: /[.']/,\n        contains: [hljs.UNDERSCORE_TITLE_MODE]\n      },\n      {\n        beginKeywords: 'use',\n        relevance: 0,\n        end: ';',\n        contains: [hljs.UNDERSCORE_TITLE_MODE]\n      },\n      STRING,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = php;\n"], "names": ["module", "exports", "hljs", "VARIABLE", "className", "begin", "PREPROCESSOR", "variants", "relevance", "SUBST", "end", "SINGLE_QUOTED", "inherit", "APOS_STRING_MODE", "illegal", "DOUBLE_QUOTED", "QUOTE_STRING_MODE", "contains", "concat", "HEREDOC", "END_SAME_AS_BEGIN", "STRING", "BACKSLASH_ESCAPE", "NUMBER", "KEYWORDS", "keyword", "literal", "built_in", "aliases", "case_insensitive", "keywords", "HASH_COMMENT_MODE", "COMMENT", "endsWithParent", "beginKeywords", "excludeEnd", "UNDERSCORE_TITLE_MODE", "endsParent", "excludeBegin", "C_BLOCK_COMMENT_MODE"], "sourceRoot": ""}
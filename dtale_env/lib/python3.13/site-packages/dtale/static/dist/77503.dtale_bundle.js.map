{"version": 3, "file": "77503.dtale_bundle.js", "mappings": "6FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,sBACLC,MAAO,+BAETC,SAAU,CACRF,IAAK,UACLC,MAAO,mBAETE,YAAa,iBACbC,iBAAkB,CAChBJ,IAAK,sBACLC,MAAO,+BAETI,SAAU,CACRL,IAAK,UACLC,MAAO,mBAETK,YAAa,CACXN,IAAK,gBACLC,MAAO,yBAETM,OAAQ,CACNP,IAAK,QACLC,MAAO,iBAETO,MAAO,CACLR,IAAK,SACLC,MAAO,kBAETQ,YAAa,CACXT,IAAK,mBACLC,MAAO,4BAETS,OAAQ,CACNV,IAAK,WACLC,MAAO,oBAETU,aAAc,CACZX,IAAK,kBACLC,MAAO,2BAETW,QAAS,CACPZ,IAAK,UACLC,MAAO,mBAETY,YAAa,CACXb,IAAK,kBACLC,MAAO,2BAETa,OAAQ,CACNd,IAAK,UACLC,MAAO,mBAETc,WAAY,CACVf,IAAK,qBACLC,MAAO,8BAETe,aAAc,CACZhB,IAAK,iBACLC,MAAO,2BA2BPgB,EAvBiB,SAAwBC,EAAOC,EAAOC,GACzD,IAAIC,EACAC,EAAaxB,EAAqBoB,GAUtC,OAPEG,EADwB,iBAAfC,EACAA,EACU,IAAVH,EACAG,EAAWtB,IAEXsB,EAAWrB,MAAMsB,QAAQ,YAAaJ,EAAMK,YAGnDJ,SAA0CA,EAAQK,UAChDL,EAAQM,YAAcN,EAAQM,WAAa,EACtC,eAAiBL,EAEjBA,EAAS,aAIbA,CACT,EAGAzB,EAAA,QAAkBqB,EAClBU,EAAO/B,QAAUA,EAAQgC,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/id/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'kurang dari 1 detik',\n    other: 'kurang dari {{count}} detik'\n  },\n  xSeconds: {\n    one: '1 detik',\n    other: '{{count}} detik'\n  },\n  halfAMinute: 'setengah menit',\n  lessThanXMinutes: {\n    one: 'kurang dari 1 menit',\n    other: 'kurang dari {{count}} menit'\n  },\n  xMinutes: {\n    one: '1 menit',\n    other: '{{count}} menit'\n  },\n  aboutXHours: {\n    one: 'sekitar 1 jam',\n    other: 'sekitar {{count}} jam'\n  },\n  xHours: {\n    one: '1 jam',\n    other: '{{count}} jam'\n  },\n  xDays: {\n    one: '1 hari',\n    other: '{{count}} hari'\n  },\n  aboutXWeeks: {\n    one: 'sekitar 1 minggu',\n    other: 'sekitar {{count}} minggu'\n  },\n  xWeeks: {\n    one: '1 minggu',\n    other: '{{count}} minggu'\n  },\n  aboutXMonths: {\n    one: 'sekitar 1 bulan',\n    other: 'sekitar {{count}} bulan'\n  },\n  xMonths: {\n    one: '1 bulan',\n    other: '{{count}} bulan'\n  },\n  aboutXYears: {\n    one: 'sekitar 1 tahun',\n    other: 'sekitar {{count}} tahun'\n  },\n  xYears: {\n    one: '1 tahun',\n    other: '{{count}} tahun'\n  },\n  overXYears: {\n    one: 'lebih dari 1 tahun',\n    other: 'lebih dari {{count}} tahun'\n  },\n  almostXYears: {\n    one: 'hampir 1 tahun',\n    other: 'hampir {{count}} tahun'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'dalam waktu ' + result;\n    } else {\n      return result + ' yang lalu';\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_default", "token", "count", "options", "result", "tokenValue", "replace", "toString", "addSuffix", "comparison", "module", "default"], "sourceRoot": ""}
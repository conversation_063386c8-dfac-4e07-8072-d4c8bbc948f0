{"version": 3, "file": "87879.dtale_bundle.js", "mappings": "6EAwbAA,EAAOC,QA/aP,SAAgBC,GACd,MA2IMC,EAAW,CACfC,QA/FsB,CACtB,WACA,KACA,OACA,QACA,OACA,QACA,QACA,WACA,KACA,OACA,QACA,WACA,SACA,UACA,QACA,MACA,UACA,OACA,KACA,WACA,KACA,YACA,WACA,KACA,OACA,YACA,MACA,WACA,MACA,WACA,SACA,UACA,YACA,SACA,WACA,SACA,MACA,SACA,SACA,SACA,aACA,SACA,SACA,SACA,OACA,QACA,MACA,SACA,YACA,SACA,QACA,UACA,OACA,WACA,SAwCyBC,OAtCC,CAC1B,MACA,QACA,MACA,YACA,QACA,QACA,KACA,aACA,SACA,OACA,MACA,SACA,QACA,OACA,OACA,OACA,MACA,SACA,MACA,UACA,KACA,KACA,UACA,UACA,SACA,SACA,MACA,YACA,UACA,MACA,OACA,QACA,OACA,UAKAC,SA7IwB,CACxB,OACA,OACA,OACA,UACA,WACA,SACA,UACA,OACA,QACA,MACA,OACA,OACA,QACA,SACA,QACA,QACA,SACA,QACA,OACA,UA0HAC,QAvGuB,CACvB,UACA,QACA,OACA,SAqGIC,EAAaN,EAAKO,QAAQP,EAAKM,WAAY,CAC/CE,MAAO,uBAEHC,EAAU,CACdC,UAAW,SACXC,SAAU,CACR,CACEH,MAAO,iBAET,CACEA,MAAO,mEAET,CACEA,MAAO,wFAGXI,UAAW,GAEPC,EAAkB,CACtBH,UAAW,SACXF,MAAO,KACPM,IAAK,IACLC,SAAU,CACR,CACEP,MAAO,QAIPQ,EAAwBhB,EAAKO,QAAQM,EAAiB,CAC1DI,QAAS,OAELC,EAAQ,CACZR,UAAW,QACXF,MAAO,KACPM,IAAK,KACLK,SAAUlB,GAENmB,EAAcpB,EAAKO,QAAQW,EAAO,CACtCD,QAAS,OAELI,EAAsB,CAC1BX,UAAW,SACXF,MAAO,MACPM,IAAK,IACLG,QAAS,KACTF,SAAU,CACR,CACEP,MAAO,QAET,CACEA,MAAO,QAETR,EAAKsB,iBACLF,IAGEG,EAA+B,CACnCb,UAAW,SACXF,MAAO,OACPM,IAAK,IACLC,SAAU,CACR,CACEP,MAAO,QAET,CACEA,MAAO,QAET,CACEA,MAAO,MAETU,IAGEM,EAAqCxB,EAAKO,QAAQgB,EAA8B,CACpFN,QAAS,KACTF,SAAU,CACR,CACEP,MAAO,QAET,CACEA,MAAO,QAET,CACEA,MAAO,MAETY,KAGJF,EAAMH,SAAW,CACfQ,EACAF,EACAR,EACAb,EAAKyB,iBACLzB,EAAK0B,kBACLjB,EACAT,EAAK2B,sBAEPP,EAAYL,SAAW,CACrBS,EACAH,EACAL,EACAhB,EAAKyB,iBACLzB,EAAK0B,kBACLjB,EACAT,EAAKO,QAAQP,EAAK2B,qBAAsB,CACtCV,QAAS,QAGb,MAAMW,EAAS,CACbjB,SAAU,CACRY,EACAF,EACAR,EACAb,EAAKyB,iBACLzB,EAAK0B,oBAIHG,EAAmB,CACvBrB,MAAO,IACPM,IAAK,IACLC,SAAU,CACR,CACEe,cAAe,UAEjBxB,IAGEyB,EAAgB/B,EAAKgC,SAAW,KAAOhC,EAAKgC,SAAW,aAAehC,EAAKgC,SAAW,iBACtFC,EAAgB,CAGpBzB,MAAO,IAAMR,EAAKgC,SAClBpB,UAAW,GAGb,MAAO,CACLsB,KAAM,KACNC,QAAS,CACP,KACA,MAEFhB,SAAUlB,EACVgB,QAAS,KACTF,SAAU,CACRf,EAAKoC,QACH,MACA,IACA,CACEC,aAAa,EACbtB,SAAU,CACR,CACEL,UAAW,SACXC,SAAU,CACR,CACEH,MAAO,MACPI,UAAW,GAEb,CACEJ,MAAO,kBAET,CACEA,MAAO,MACPM,IAAK,UAOjBd,EAAKsC,oBACLtC,EAAK2B,qBACL,CACEjB,UAAW,OACXF,MAAO,IACPM,IAAK,IACLK,SAAU,CACR,eAAgB,wFAGpBS,EACAnB,EACA,CACEqB,cAAe,kBACflB,UAAW,EACXE,IAAK,QACLG,QAAS,UACTF,SAAU,CACR,CACEe,cAAe,eAEjBxB,EACAuB,EACA7B,EAAKsC,oBACLtC,EAAK2B,uBAGT,CACEG,cAAe,YACflB,UAAW,EACXE,IAAK,QACLG,QAAS,SACTF,SAAU,CACRT,EACAN,EAAKsC,oBACLtC,EAAK2B,uBAGT,CACEG,cAAe,SACflB,UAAW,EACXE,IAAK,QACLG,QAAS,SACTF,SAAU,CACRT,EACAuB,EACA7B,EAAKsC,oBACLtC,EAAK2B,uBAGT,CAEEjB,UAAW,OACXF,MAAO,WACP+B,cAAc,EACdzB,IAAK,MACL0B,YAAY,EACZzB,SAAU,CACR,CACEL,UAAW,cACXF,MAAO,IACPM,IAAK,OAIX,CAGEgB,cAAe,8BACflB,UAAW,GAEb,CACEF,UAAW,WACXF,MAAO,IAAMuB,EAAgB,SAAW/B,EAAKgC,SAAW,qBACxDK,aAAa,EACbvB,IAAK,WACL0B,YAAY,EACZrB,SAAUlB,EACVc,SAAU,CAER,CACEe,cArXiB,CACzB,SACA,UACA,YACA,SACA,WACA,YACA,WACA,QACA,SACA,WACA,SACA,UACA,MACA,SACA,WAsW0CW,KAAK,KACvC7B,UAAW,GAEb,CACEJ,MAAOR,EAAKgC,SAAW,qBACvBK,aAAa,EACbtB,SAAU,CACRf,EAAKM,WACLuB,GAEFjB,UAAW,GAEb,CACEF,UAAW,SACXF,MAAO,KACPM,IAAK,KACLyB,cAAc,EACdC,YAAY,EACZrB,SAAUlB,EACVW,UAAW,EACXG,SAAU,CACRa,EACAnB,EACAT,EAAK2B,uBAGT3B,EAAKsC,oBACLtC,EAAK2B,uBAGTM,GAGN,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/csharp.js"], "sourcesContent": ["/*\nLanguage: C#\nAuthor: <PERSON> <<EMAIL>>\nContributor: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://docs.microsoft.com/en-us/dotnet/csharp/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction csharp(hljs) {\n  const BUILT_IN_KEYWORDS = [\n    'bool',\n    'byte',\n    'char',\n    'decimal',\n    'delegate',\n    'double',\n    'dynamic',\n    'enum',\n    'float',\n    'int',\n    'long',\n    'nint',\n    'nuint',\n    'object',\n    'sbyte',\n    'short',\n    'string',\n    'ulong',\n    'uint',\n    'ushort'\n  ];\n  const FUNCTION_MODIFIERS = [\n    'public',\n    'private',\n    'protected',\n    'static',\n    'internal',\n    'protected',\n    'abstract',\n    'async',\n    'extern',\n    'override',\n    'unsafe',\n    'virtual',\n    'new',\n    'sealed',\n    'partial'\n  ];\n  const LITERAL_KEYWORDS = [\n    'default',\n    'false',\n    'null',\n    'true'\n  ];\n  const NORMAL_KEYWORDS = [\n    'abstract',\n    'as',\n    'base',\n    'break',\n    'case',\n    'class',\n    'const',\n    'continue',\n    'do',\n    'else',\n    'event',\n    'explicit',\n    'extern',\n    'finally',\n    'fixed',\n    'for',\n    'foreach',\n    'goto',\n    'if',\n    'implicit',\n    'in',\n    'interface',\n    'internal',\n    'is',\n    'lock',\n    'namespace',\n    'new',\n    'operator',\n    'out',\n    'override',\n    'params',\n    'private',\n    'protected',\n    'public',\n    'readonly',\n    'record',\n    'ref',\n    'return',\n    'sealed',\n    'sizeof',\n    'stackalloc',\n    'static',\n    'struct',\n    'switch',\n    'this',\n    'throw',\n    'try',\n    'typeof',\n    'unchecked',\n    'unsafe',\n    'using',\n    'virtual',\n    'void',\n    'volatile',\n    'while'\n  ];\n  const CONTEXTUAL_KEYWORDS = [\n    'add',\n    'alias',\n    'and',\n    'ascending',\n    'async',\n    'await',\n    'by',\n    'descending',\n    'equals',\n    'from',\n    'get',\n    'global',\n    'group',\n    'init',\n    'into',\n    'join',\n    'let',\n    'nameof',\n    'not',\n    'notnull',\n    'on',\n    'or',\n    'orderby',\n    'partial',\n    'remove',\n    'select',\n    'set',\n    'unmanaged',\n    'value|0',\n    'var',\n    'when',\n    'where',\n    'with',\n    'yield'\n  ];\n\n  const KEYWORDS = {\n    keyword: NORMAL_KEYWORDS.concat(CONTEXTUAL_KEYWORDS),\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n  const TITLE_MODE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: '[a-zA-Z](\\\\.?\\\\w)*'\n  });\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      {\n        begin: '\\\\b(0b[01\\']+)'\n      },\n      {\n        begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)(u|U|l|L|ul|UL|f|F|b|B)'\n      },\n      {\n        begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)'\n      }\n    ],\n    relevance: 0\n  };\n  const VERBATIM_STRING = {\n    className: 'string',\n    begin: '@\"',\n    end: '\"',\n    contains: [\n      {\n        begin: '\"\"'\n      }\n    ]\n  };\n  const VERBATIM_STRING_NO_LF = hljs.inherit(VERBATIM_STRING, {\n    illegal: /\\n/\n  });\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  const SUBST_NO_LF = hljs.inherit(SUBST, {\n    illegal: /\\n/\n  });\n  const INTERPOLATED_STRING = {\n    className: 'string',\n    begin: /\\$\"/,\n    end: '\"',\n    illegal: /\\n/,\n    contains: [\n      {\n        begin: /\\{\\{/\n      },\n      {\n        begin: /\\}\\}/\n      },\n      hljs.BACKSLASH_ESCAPE,\n      SUBST_NO_LF\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING = {\n    className: 'string',\n    begin: /\\$@\"/,\n    end: '\"',\n    contains: [\n      {\n        begin: /\\{\\{/\n      },\n      {\n        begin: /\\}\\}/\n      },\n      {\n        begin: '\"\"'\n      },\n      SUBST\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING_NO_LF = hljs.inherit(INTERPOLATED_VERBATIM_STRING, {\n    illegal: /\\n/,\n    contains: [\n      {\n        begin: /\\{\\{/\n      },\n      {\n        begin: /\\}\\}/\n      },\n      {\n        begin: '\"\"'\n      },\n      SUBST_NO_LF\n    ]\n  });\n  SUBST.contains = [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.C_BLOCK_COMMENT_MODE\n  ];\n  SUBST_NO_LF.contains = [\n    INTERPOLATED_VERBATIM_STRING_NO_LF,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING_NO_LF,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.inherit(hljs.C_BLOCK_COMMENT_MODE, {\n      illegal: /\\n/\n    })\n  ];\n  const STRING = {\n    variants: [\n      INTERPOLATED_VERBATIM_STRING,\n      INTERPOLATED_STRING,\n      VERBATIM_STRING,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n\n  const GENERIC_MODIFIER = {\n    begin: \"<\",\n    end: \">\",\n    contains: [\n      {\n        beginKeywords: \"in out\"\n      },\n      TITLE_MODE\n    ]\n  };\n  const TYPE_IDENT_RE = hljs.IDENT_RE + '(<' + hljs.IDENT_RE + '(\\\\s*,\\\\s*' + hljs.IDENT_RE + ')*>)?(\\\\[\\\\])?';\n  const AT_IDENTIFIER = {\n    // prevents expressions like `@class` from incorrect flagging\n    // `class` as a keyword\n    begin: \"@\" + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  return {\n    name: 'C#',\n    aliases: [\n      'cs',\n      'c#'\n    ],\n    keywords: KEYWORDS,\n    illegal: /::/,\n    contains: [\n      hljs.COMMENT(\n        '///',\n        '$',\n        {\n          returnBegin: true,\n          contains: [\n            {\n              className: 'doctag',\n              variants: [\n                {\n                  begin: '///',\n                  relevance: 0\n                },\n                {\n                  begin: '<!--|-->'\n                },\n                {\n                  begin: '</?',\n                  end: '>'\n                }\n              ]\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'if else elif endif define undef warning error line region endregion pragma checksum'\n        }\n      },\n      STRING,\n      NUMBERS,\n      {\n        beginKeywords: 'class interface',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:,]/,\n        contains: [\n          {\n            beginKeywords: \"where class\"\n          },\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'record',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        // [Attributes(\"\")]\n        className: 'meta',\n        begin: '^\\\\s*\\\\[',\n        excludeBegin: true,\n        end: '\\\\]',\n        excludeEnd: true,\n        contains: [\n          {\n            className: 'meta-string',\n            begin: /\"/,\n            end: /\"/\n          }\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new return throw await else',\n        relevance: 0\n      },\n      {\n        className: 'function',\n        begin: '(' + TYPE_IDENT_RE + '\\\\s+)+' + hljs.IDENT_RE + '\\\\s*(<.+>\\\\s*)?\\\\(',\n        returnBegin: true,\n        end: /\\s*[{;=]/,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          // prevents these from being highlighted `title`\n          {\n            beginKeywords: FUNCTION_MODIFIERS.join(\" \"),\n            relevance: 0\n          },\n          {\n            begin: hljs.IDENT_RE + '\\\\s*(<.+>\\\\s*)?\\\\(',\n            returnBegin: true,\n            contains: [\n              hljs.TITLE_MODE,\n              GENERIC_MODIFIER\n            ],\n            relevance: 0\n          },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              STRING,\n              NUMBERS,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      AT_IDENTIFIER\n    ]\n  };\n}\n\nmodule.exports = csharp;\n"], "names": ["module", "exports", "hljs", "KEYWORDS", "keyword", "concat", "built_in", "literal", "TITLE_MODE", "inherit", "begin", "NUMBERS", "className", "variants", "relevance", "VERBATIM_STRING", "end", "contains", "VERBATIM_STRING_NO_LF", "illegal", "SUBST", "keywords", "SUBST_NO_LF", "INTERPOLATED_STRING", "BACKSLASH_ESCAPE", "INTERPOLATED_VERBATIM_STRING", "INTERPOLATED_VERBATIM_STRING_NO_LF", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "C_BLOCK_COMMENT_MODE", "STRING", "GENERIC_MODIFIER", "beginKeywords", "TYPE_IDENT_RE", "IDENT_RE", "AT_IDENTIFIER", "name", "aliases", "COMMENT", "returnBegin", "C_LINE_COMMENT_MODE", "excludeBegin", "excludeEnd", "join"], "sourceRoot": ""}
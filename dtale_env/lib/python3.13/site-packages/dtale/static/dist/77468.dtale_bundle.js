"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[77468],{53657:(e,t,d)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l,a=(l=d(19059))&&l.__esModule?l:{default:l};var u={date:(0,a.default)({formats:{full:"EEEE, do MMMM, y",long:"do MMMM, y",medium:"d MMM, y",short:"dd/MM/yyyy"},defaultWidth:"full"}),time:(0,a.default)({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:(0,a.default)({formats:{full:"{{date}} 'को' {{time}}",long:"{{date}} 'को' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})};t.default=u,e.exports=t.default}}]);
//# sourceMappingURL=77468.dtale_bundle.js.map
"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[90350],{98851:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d,a=(d=l(19059))&&d.__esModule?d:{default:d};var u={date:(0,a.default)({formats:{full:"eeee d. MMMM y",long:"d. MMMM y",medium:"d. MMM y",short:"d.M.y"},defaultWidth:"full"}),time:(0,a.default)({formats:{full:"HH.mm.ss zzzz",long:"HH.mm.ss z",medium:"HH.mm.ss",short:"HH.mm"},defaultWidth:"full"}),dateTime:(0,a.default)({formats:{full:"{{date}} 'klo' {{time}}",long:"{{date}} 'klo' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};t.default=u,e.exports=t.default}}]);
//# sourceMappingURL=90350.dtale_bundle.js.map
{"version": 3, "file": "80347.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAsHII,EA5BW,CACbC,cALkB,SAAuBC,GACzC,OAAOC,OAAOD,EAChB,EAIEE,KAAK,EAAIP,EAAOE,SAAS,CACvBM,OA7FY,CACdC,OAAQ,CAAC,IAAK,KACdC,YAAa,CAAC,OAAQ,QACtBC,KAAM,CAAC,cAAe,gBA2FpBC,aAAc,SAEhBC,SAAS,EAAIb,EAAOE,SAAS,CAC3BM,OA5FgB,CAClBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,KAAM,KAAM,KAAM,MAChCC,KAAM,CAAC,cAAe,eAAgB,eAAgB,iBA0FpDC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOE,OAAOF,GAAW,CAC3B,IAEFG,OAAO,EAAIhB,EAAOE,SAAS,CACzBM,OA9Fc,CAChBC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,MAAO,MAAO,OAAQ,QAAS,MAAO,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,QACnGC,KAAM,CAAC,QAAS,SAAU,OAAQ,QAAS,MAAO,QAAS,SAAU,MAAO,QAAS,SAAU,QAAS,UA4FtGC,aAAc,SAEhBK,KAAK,EAAIjB,EAAOE,SAAS,CACvBM,OA7FY,CACdC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCS,MAAO,CAAC,MAAO,QAAS,SAAU,SAAU,OAAQ,OAAQ,OAC5DR,YAAa,CAAC,MAAO,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,OAC5DC,KAAM,CAAC,QAAS,UAAW,WAAY,WAAY,SAAU,SAAU,UA0FrEC,aAAc,SAEhBO,WAAW,EAAInB,EAAOE,SAAS,CAC7BM,OA3FkB,CACpBC,OAAQ,CACNW,GAAI,IACJC,GAAI,IACJC,SAAU,IACVC,KAAM,IACNC,QAAS,SACTC,UAAW,YACXC,QAAS,SACTC,MAAO,SAETjB,YAAa,CACXU,GAAI,IACJC,GAAI,IACJC,SAAU,YACVC,KAAM,MACNC,QAAS,SACTC,UAAW,YACXC,QAAS,SACTC,MAAO,SAEThB,KAAM,CACJS,GAAI,IACJC,GAAI,IACJC,SAAU,YACVC,KAAM,MACNC,QAAS,SACTC,UAAW,YACXC,QAAS,SACTC,MAAO,UA+DPf,aAAc,OACdgB,iBA7D4B,CAC9BnB,OAAQ,CACNW,GAAI,IACJC,GAAI,IACJC,SAAU,IACVC,KAAM,IACNC,QAAS,YACTC,UAAW,aACXC,QAAS,YACTC,MAAO,YAETjB,YAAa,CACXU,GAAI,IACJC,GAAI,IACJC,SAAU,YACVC,KAAM,MACNC,QAAS,YACTC,UAAW,YACXC,QAAS,YACTC,MAAO,YAEThB,KAAM,CACJS,GAAI,IACJC,GAAI,IACJC,SAAU,YACVC,KAAM,MACNC,QAAS,SACTC,UAAW,aACXC,QAAS,YACTC,MAAO,aAiCPE,uBAAwB,UAI5BhC,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/ar-MA/_lib/localize/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['ق', 'ب'],\n  abbreviated: ['ق.م.', 'ب.م.'],\n  wide: ['قبل الميلاد', 'بعد الميلاد']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['ر1', 'ر2', 'ر3', 'ر4'],\n  wide: ['الربع الأول', 'الربع الثاني', 'الربع الثالث', 'الربع الرابع']\n};\nvar monthValues = {\n  narrow: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'غ', 'ش', 'أ', 'ن', 'د'],\n  abbreviated: ['ينا', 'فبر', 'مارس', 'أبريل', 'ماي', 'يونـ', 'يولـ', 'غشت', 'شتنـ', 'أكتـ', 'نونـ', 'دجنـ'],\n  wide: ['يناير', 'فبراير', 'مارس', 'أبريل', 'ماي', 'يونيو', 'يوليوز', 'غشت', 'شتنبر', 'أكتوبر', 'نونبر', 'دجنبر']\n};\nvar dayValues = {\n  narrow: ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],\n  short: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],\n  abbreviated: ['أحد', 'اثنـ', 'ثلا', 'أربـ', 'خميـ', 'جمعة', 'سبت'],\n  wide: ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'ن',\n    noon: 'ظ',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءاً',\n    night: 'ليلاً'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهر',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءاً',\n    night: 'ليلاً'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهر',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءاً',\n    night: 'ليلاً'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'ن',\n    noon: 'ظ',\n    morning: 'في الصباح',\n    afternoon: 'بعد الظـهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهر',\n    morning: 'في الصباح',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهر',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظـهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber) {\n  return String(dirtyNumber);\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return Number(quarter) - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "String", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "Number", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingValues", "defaultFormattingWidth", "module"], "sourceRoot": ""}
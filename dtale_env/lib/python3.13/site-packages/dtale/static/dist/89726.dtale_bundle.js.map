{"version": 3, "file": "89726.dtale_bundle.js", "mappings": "6EA2FAA,EAAOC,QApFP,SAAYC,GACV,MAIMC,EAAc,CAClBC,SAAU,yBACVC,QACE,2JAEFC,QACE,iBACFC,SACE,8v<PERSON><PERSON><PERSON>,EAAoB,CACxBC,UAAW,SACXC,MAAO,IACPC,IAAK,IACLC,QAAS,OAiBLC,EAAS,CACbC,cAAe,SACfH,IAAK,IACLI,SAAUZ,EACVa,SAAU,CAAER,IAERS,EAAsB,CAC1BR,UAAW,WACXC,MAAO,gBACPQ,aAAa,EACbP,IAAK,KACLK,SAAU,CACRd,EAAKiB,QAAQjB,EAAKkB,WAAY,CAC5BC,OAAQ,CACNC,gBAAgB,EAChBP,SAAUZ,OAKlB,MAAO,CACLoB,KAAM,KACNC,QAAS,CAAE,OACXT,SAAUZ,EACVa,SAAU,CACRd,EAAKuB,oBACLvB,EAAKwB,qBACLlB,EA1CsB,CACxBC,UAAW,SACXC,MAAO,IACPC,IAAK,IACLC,QAAS,OAEO,CAChBH,UAAW,SACXC,MAAO,KACPC,IAAK,MAoCHM,EACAJ,EAnCiB,CACnBJ,UAAW,SACXC,MAAO,0DAmCLR,EAAKyB,aAGX,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/xl.js"], "sourcesContent": ["/*\nLanguage: XL\nAuthor: <PERSON> <<EMAIL>>\nDescription: An extensible programming language, based on parse tree rewriting\nWebsite: http://xlr.sf.net\n*/\n\nfunction xl(hljs) {\n  const BUILTIN_MODULES =\n    'ObjectLoader Animate MovieCredits Slides Filters Shading Materials LensFlare Mapping VLCAudioVideo ' +\n    'StereoDecoder PointCloud NetworkAccess RemoteControl RegExp ChromaKey Snowfall NodeJS Speech Charts';\n\n  const XL_KEYWORDS = {\n    $pattern: /[a-zA-Z][a-zA-Z0-9_?]*/,\n    keyword:\n      'if then else do while until for loop import with is as where when by data constant ' +\n      'integer real text name boolean symbol infix prefix postfix block tree',\n    literal:\n      'true false nil',\n    built_in:\n      'in mod rem and or xor not abs sign floor ceil sqrt sin cos tan asin ' +\n      'acos atan exp expm1 log log2 log10 log1p pi at text_length text_range ' +\n      'text_find text_replace contains page slide basic_slide title_slide ' +\n      'title subtitle fade_in fade_out fade_at clear_color color line_color ' +\n      'line_width texture_wrap texture_transform texture scale_?x scale_?y ' +\n      'scale_?z? translate_?x translate_?y translate_?z? rotate_?x rotate_?y ' +\n      'rotate_?z? rectangle circle ellipse sphere path line_to move_to ' +\n      'quad_to curve_to theme background contents locally time mouse_?x ' +\n      'mouse_?y mouse_buttons ' +\n      BUILTIN_MODULES\n  };\n\n  const DOUBLE_QUOTE_TEXT = {\n    className: 'string',\n    begin: '\"',\n    end: '\"',\n    illegal: '\\\\n'\n  };\n  const SINGLE_QUOTE_TEXT = {\n    className: 'string',\n    begin: '\\'',\n    end: '\\'',\n    illegal: '\\\\n'\n  };\n  const LONG_TEXT = {\n    className: 'string',\n    begin: '<<',\n    end: '>>'\n  };\n  const BASED_NUMBER = {\n    className: 'number',\n    begin: '[0-9]+#[0-9A-Z_]+(\\\\.[0-9-A-Z_]+)?#?([Ee][+-]?[0-9]+)?'\n  };\n  const IMPORT = {\n    beginKeywords: 'import',\n    end: '$',\n    keywords: XL_KEYWORDS,\n    contains: [ DOUBLE_QUOTE_TEXT ]\n  };\n  const FUNCTION_DEFINITION = {\n    className: 'function',\n    begin: /[a-z][^\\n]*->/,\n    returnBegin: true,\n    end: /->/,\n    contains: [\n      hljs.inherit(hljs.TITLE_MODE, {\n        starts: {\n          endsWithParent: true,\n          keywords: XL_KEYWORDS\n        }\n      })\n    ]\n  };\n  return {\n    name: 'XL',\n    aliases: [ 'tao' ],\n    keywords: XL_KEYWORDS,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      DOUBLE_QUOTE_TEXT,\n      SINGLE_QUOTE_TEXT,\n      LONG_TEXT,\n      FUNCTION_DEFINITION,\n      IMPORT,\n      BASED_NUMBER,\n      hljs.NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = xl;\n"], "names": ["module", "exports", "hljs", "XL_KEYWORDS", "$pattern", "keyword", "literal", "built_in", "DOUBLE_QUOTE_TEXT", "className", "begin", "end", "illegal", "IMPORT", "beginKeywords", "keywords", "contains", "FUNCTION_DEFINITION", "returnBegin", "inherit", "TITLE_MODE", "starts", "endsWithParent", "name", "aliases", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "NUMBER_MODE"], "sourceRoot": ""}
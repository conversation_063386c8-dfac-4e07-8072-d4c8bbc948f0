{"version": 3, "file": "99902.dtale_bundle.js", "mappings": "8HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAuIII,EA5BW,CACbC,cAtBkB,SAAuBC,EAAaC,GACtD,IAAIC,EAASC,OAAOH,GAEpB,OAAQC,aAAyC,EAASA,EAAQG,MAChE,IAAK,OACH,OAAOF,EAAS,IAElB,IAAK,OACH,OAAOA,EAAS,IAElB,IAAK,SACH,OAAOA,EAAS,IAElB,IAAK,SACH,OAAOA,EAAS,IAElB,QACE,MAAO,KAAOA,EAEpB,EAIEG,KAAK,EAAIV,EAAOE,SAAS,CACvBS,OA9GY,CACdC,OAAQ,CAAC,IAAK,MACdC,YAAa,CAAC,IAAK,MACnBC,KAAM,CAAC,MAAO,OA4GZC,aAAc,SAEhBC,SAAS,EAAIhB,EAAOE,SAAS,CAC3BS,OA7GgB,CAClBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,MAAO,MAAO,MAAO,OACnCC,KAAM,CAAC,OAAQ,OAAQ,OAAQ,SA2G7BC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAIlB,EAAOE,SAAS,CACzBS,OA/Gc,CAChBC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,MACjEC,YAAa,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAAO,MAAO,OAClFC,KAAM,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAAO,QA6GxEC,aAAc,SAEhBI,KAAK,EAAInB,EAAOE,SAAS,CACvBS,OA9GY,CACdC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCQ,MAAO,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACtCP,YAAa,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAClDC,KAAM,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,QA2G/CC,aAAc,SAEhBM,WAAW,EAAIrB,EAAOE,SAAS,CAC7BS,OA5GkB,CACpBC,OAAQ,CACNU,GAAI,IACJC,GAAI,IACJC,SAAU,KACVC,KAAM,IACNC,QAAS,IACTC,UAAW,KACXC,QAAS,IACTC,MAAO,KAEThB,YAAa,CACXS,GAAI,KACJC,GAAI,KACJC,SAAU,KACVC,KAAM,KACNC,QAAS,KACTC,UAAW,KACXC,QAAS,KACTC,MAAO,MAETf,KAAM,CACJQ,GAAI,KACJC,GAAI,KACJC,SAAU,KACVC,KAAM,KACNC,QAAS,KACTC,UAAW,KACXC,QAAS,KACTC,MAAO,OAgFPd,aAAc,OACde,iBA9E4B,CAC9BlB,OAAQ,CACNU,GAAI,IACJC,GAAI,IACJC,SAAU,KACVC,KAAM,IACNC,QAAS,IACTC,UAAW,KACXC,QAAS,IACTC,MAAO,KAEThB,YAAa,CACXS,GAAI,KACJC,GAAI,KACJC,SAAU,KACVC,KAAM,KACNC,QAAS,KACTC,UAAW,KACXC,QAAS,KACTC,MAAO,MAETf,KAAM,CACJQ,GAAI,KACJC,GAAI,KACJC,SAAU,KACVC,KAAM,KACNC,QAAS,KACTC,UAAW,KACXC,QAAS,KACTC,MAAO,OAkDPE,uBAAwB,UAI5BlC,EAAA,QAAkBM,EAClB6B,EAAOnC,QAAUA,EAAQK,O,kBClJzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAASiC,EAAuB,EAAQ,QAI5C,SAASA,EAAuBlC,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAE9F,IA6FII,EA1CQ,CACVC,eAAe,EAxDH6B,EAAuB,EAAQ,MAwDhB/B,SAAS,CAClCgC,aArD4B,yBAsD5BC,aArD4B,OAsD5BC,cAAe,SAAuBtC,GACpC,OAAOuC,SAASvC,EAAO,GACzB,IAEFY,KAAK,EAAIV,EAAOE,SAAS,CACvBoC,cA1DmB,CACrB1B,OAAQ,QACRC,YAAa,QACbC,KAAM,cAwDJyB,kBAAmB,OACnBC,cAvDmB,CACrBC,IAAK,CAAC,QAAS,WAuDbC,kBAAmB,QAErB1B,SAAS,EAAIhB,EAAOE,SAAS,CAC3BoC,cAxDuB,CACzB1B,OAAQ,WACRC,YAAa,aACbC,KAAM,eAsDJyB,kBAAmB,OACnBC,cArDuB,CACzBC,IAAK,CAAC,SAAU,SAAU,SAAU,WAqDlCC,kBAAmB,MACnBN,cAAe,SAAuBO,GACpC,OAAOA,EAAQ,CACjB,IAEFzB,OAAO,EAAIlB,EAAOE,SAAS,CACzBoC,cAzDqB,CACvB1B,OAAQ,8BACRC,YAAa,wCACbC,KAAM,gCAuDJyB,kBAAmB,OACnBC,cAtDqB,CACvB5B,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,eAAgB,OAAQ,QAChG6B,IAAK,CAAC,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,kBAAmB,UAAW,YAqDnHC,kBAAmB,QAErBvB,KAAK,EAAInB,EAAOE,SAAS,CACvBoC,cAtDmB,CACrB1B,OAAQ,cACRQ,MAAO,cACPP,YAAa,eACbC,KAAM,iBAmDJyB,kBAAmB,OACnBC,cAlDmB,CACrBC,IAAK,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,OAkDxCC,kBAAmB,QAErBrB,WAAW,EAAIrB,EAAOE,SAAS,CAC7BoC,cAnDyB,CAC3BG,IAAK,sCAmDHF,kBAAmB,MACnBC,cAlDyB,CAC3BC,IAAK,CACHnB,GAAI,QACJC,GAAI,QACJC,SAAU,OACVC,KAAM,UACNC,QAAS,OACTC,UAAW,OACXC,QAAS,QACTC,MAAO,SA0CPa,kBAAmB,SAIvB7C,EAAA,QAAkBM,EAClB6B,EAAOnC,QAAUA,EAAQK,O,gBC1GzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAI+C,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,SACLC,MAAO,kBAETC,SAAU,CACRF,IAAK,MACLC,MAAO,eAETE,YAAa,MACbC,iBAAkB,CAChBJ,IAAK,UACLC,MAAO,mBAETI,SAAU,CACRL,IAAK,OACLC,MAAO,gBAETK,OAAQ,CACNN,IAAK,OACLC,MAAO,gBAETM,YAAa,CACXP,IAAK,UACLC,MAAO,mBAETO,MAAO,CACLR,IAAK,MACLC,MAAO,eAETQ,YAAa,CACXT,IAAK,WACLC,MAAO,oBAETS,OAAQ,CACNV,IAAK,QACLC,MAAO,iBAETU,aAAc,CACZX,IAAK,UACLC,MAAO,mBAETW,QAAS,CACPZ,IAAK,OACLC,MAAO,gBAETY,YAAa,CACXb,IAAK,SACLC,MAAO,kBAETa,OAAQ,CACNd,IAAK,MACLC,MAAO,eAETc,WAAY,CACVf,IAAK,SACLC,MAAO,kBAETe,aAAc,CACZhB,IAAK,SACLC,MAAO,mBA2BP5C,EAvBiB,SAAwB4D,EAAOC,EAAO1D,GACzD,IAAI2D,EACAC,EAAatB,EAAqBmB,GAUtC,OAPEE,EADwB,iBAAfC,EACAA,EACU,IAAVF,EACAE,EAAWpB,IAEXoB,EAAWnB,MAAMoB,QAAQ,YAAaC,OAAOJ,IAGpD1D,SAA0CA,EAAQ+D,UAChD/D,EAAQgE,YAAchE,EAAQgE,WAAa,EACtCL,EAAS,IAETA,EAAS,IAIbA,CACT,EAGApE,EAAA,QAAkBM,EAClB6B,EAAOnC,QAAUA,EAAQK,O,kBC7FzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAgCII,EAda,CACfoE,MAAM,EAAIvE,EAAOE,SAAS,CACxBsE,QApBc,CAChBC,KAAM,oBACNC,KAAM,eACNC,OAAQ,aACRvD,MAAO,YAiBLL,aAAc,SAEhB6D,MAAM,EAAI5E,EAAOE,SAAS,CACxBsE,QAlBc,CAChBC,KAAM,iBACNC,KAAM,cACNC,OAAQ,YACRvD,MAAO,UAeLL,aAAc,SAEhB8D,UAAU,EAAI7E,EAAOE,SAAS,CAC5BsE,QAhBkB,CACpBC,KAAM,oBACNC,KAAM,oBACNC,OAAQ,oBACRvD,MAAO,qBAaLL,aAAc,UAIlBlB,EAAA,QAAkBM,EAClB6B,EAAOnC,QAAUA,EAAQK,O,kBC3CzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAASiC,EAAuB,EAAQ,QAExC6C,EAAU7C,EAAuB,EAAQ,QAEzC8C,EAAU9C,EAAuB,EAAQ,QAEzC+C,EAAU/C,EAAuB,EAAQ,QAEzCgD,EAAUhD,EAAuB,EAAQ,QAE7C,SAASA,EAAuBlC,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAY9F,IAcII,EAdS,CACX+E,KAAM,QACNC,eAAgBnF,EAAOE,QACvBkF,WAAYN,EAAQ5E,QACpBmF,eAAgBN,EAAQ7E,QACxBoF,SAAUN,EAAQ9E,QAClBqF,MAAON,EAAQ/E,QACfI,QAAS,CACPkF,aAAc,EAGdC,sBAAuB,IAI3B5F,EAAA,QAAkBM,EAClB6B,EAAOnC,QAAUA,EAAQK,O,gBC3CzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAI6F,EAAuB,CACzBC,SAAU,aACVC,UAAW,SACXC,MAAO,SACPC,SAAU,SACVC,SAAU,aACVhD,MAAO,KAOL5C,EAJiB,SAAwB4D,EAAOiC,EAAOC,EAAWC,GACpE,OAAOR,EAAqB3B,EAC9B,EAGAlE,EAAA,QAAkBM,EAClB6B,EAAOnC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/zh-TW/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/zh-TW/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/zh-TW/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/zh-TW/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/zh-TW/index.js", "webpack://dtale/./node_modules/date-fns/locale/zh-TW/_lib/formatRelative/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['前', '公元'],\n  abbreviated: ['前', '公元'],\n  wide: ['公元前', '公元']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['第一刻', '第二刻', '第三刻', '第四刻'],\n  wide: ['第一刻鐘', '第二刻鐘', '第三刻鐘', '第四刻鐘']\n};\nvar monthValues = {\n  narrow: ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二'],\n  abbreviated: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],\n  wide: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']\n};\nvar dayValues = {\n  narrow: ['日', '一', '二', '三', '四', '五', '六'],\n  short: ['日', '一', '二', '三', '四', '五', '六'],\n  abbreviated: ['週日', '週一', '週二', '週三', '週四', '週五', '週六'],\n  wide: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: '上',\n    pm: '下',\n    midnight: '凌晨',\n    noon: '午',\n    morning: '早',\n    afternoon: '下午',\n    evening: '晚',\n    night: '夜'\n  },\n  abbreviated: {\n    am: '上午',\n    pm: '下午',\n    midnight: '凌晨',\n    noon: '中午',\n    morning: '早晨',\n    afternoon: '中午',\n    evening: '晚上',\n    night: '夜間'\n  },\n  wide: {\n    am: '上午',\n    pm: '下午',\n    midnight: '凌晨',\n    noon: '中午',\n    morning: '早晨',\n    afternoon: '中午',\n    evening: '晚上',\n    night: '夜間'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: '上',\n    pm: '下',\n    midnight: '凌晨',\n    noon: '午',\n    morning: '早',\n    afternoon: '下午',\n    evening: '晚',\n    night: '夜'\n  },\n  abbreviated: {\n    am: '上午',\n    pm: '下午',\n    midnight: '凌晨',\n    noon: '中午',\n    morning: '早晨',\n    afternoon: '中午',\n    evening: '晚上',\n    night: '夜間'\n  },\n  wide: {\n    am: '上午',\n    pm: '下午',\n    midnight: '凌晨',\n    noon: '中午',\n    morning: '早晨',\n    afternoon: '中午',\n    evening: '晚上',\n    night: '夜間'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n\n  switch (options === null || options === void 0 ? void 0 : options.unit) {\n    case 'date':\n      return number + '日';\n\n    case 'hour':\n      return number + '時';\n\n    case 'minute':\n      return number + '分';\n\n    case 'second':\n      return number + '秒';\n\n    default:\n      return '第 ' + number;\n  }\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(第\\s*)?\\d+(日|時|分|秒)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(前)/i,\n  abbreviated: /^(前)/i,\n  wide: /^(公元前|公元)/i\n};\nvar parseEraPatterns = {\n  any: [/^(前)/i, /^(公元)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^第[一二三四]刻/i,\n  wide: /^第[一二三四]刻鐘/i\n};\nvar parseQuarterPatterns = {\n  any: [/(1|一)/i, /(2|二)/i, /(3|三)/i, /(4|四)/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^(一|二|三|四|五|六|七|八|九|十[二一])/i,\n  abbreviated: /^(一|二|三|四|五|六|七|八|九|十[二一]|\\d|1[12])月/i,\n  wide: /^(一|二|三|四|五|六|七|八|九|十[二一])月/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^一/i, /^二/i, /^三/i, /^四/i, /^五/i, /^六/i, /^七/i, /^八/i, /^九/i, /^十(?!(一|二))/i, /^十一/i, /^十二/i],\n  any: [/^一|1/i, /^二|2/i, /^三|3/i, /^四|4/i, /^五|5/i, /^六|6/i, /^七|7/i, /^八|8/i, /^九|9/i, /^十(?!(一|二))|10/i, /^十一|11/i, /^十二|12/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[一二三四五六日]/i,\n  short: /^[一二三四五六日]/i,\n  abbreviated: /^週[一二三四五六日]/i,\n  wide: /^星期[一二三四五六日]/i\n};\nvar parseDayPatterns = {\n  any: [/日/i, /一/i, /二/i, /三/i, /四/i, /五/i, /六/i]\n};\nvar matchDayPeriodPatterns = {\n  any: /^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^上午?/i,\n    pm: /^下午?/i,\n    midnight: /^午夜/i,\n    noon: /^[中正]午/i,\n    morning: /^早上/i,\n    afternoon: /^下午/i,\n    evening: /^晚上?/i,\n    night: /^凌晨/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: '少於 1 秒',\n    other: '少於 {{count}} 秒'\n  },\n  xSeconds: {\n    one: '1 秒',\n    other: '{{count}} 秒'\n  },\n  halfAMinute: '半分鐘',\n  lessThanXMinutes: {\n    one: '少於 1 分鐘',\n    other: '少於 {{count}} 分鐘'\n  },\n  xMinutes: {\n    one: '1 分鐘',\n    other: '{{count}} 分鐘'\n  },\n  xHours: {\n    one: '1 小時',\n    other: '{{count}} 小時'\n  },\n  aboutXHours: {\n    one: '大約 1 小時',\n    other: '大約 {{count}} 小時'\n  },\n  xDays: {\n    one: '1 天',\n    other: '{{count}} 天'\n  },\n  aboutXWeeks: {\n    one: '大約 1 個星期',\n    other: '大約 {{count}} 個星期'\n  },\n  xWeeks: {\n    one: '1 個星期',\n    other: '{{count}} 個星期'\n  },\n  aboutXMonths: {\n    one: '大約 1 個月',\n    other: '大約 {{count}} 個月'\n  },\n  xMonths: {\n    one: '1 個月',\n    other: '{{count}} 個月'\n  },\n  aboutXYears: {\n    one: '大約 1 年',\n    other: '大約 {{count}} 年'\n  },\n  xYears: {\n    one: '1 年',\n    other: '{{count}} 年'\n  },\n  overXYears: {\n    one: '超過 1 年',\n    other: '超過 {{count}} 年'\n  },\n  almostXYears: {\n    one: '將近 1 年',\n    other: '將近 {{count}} 年'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + '內';\n    } else {\n      return result + '前';\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: \"y'年'M'月'd'日' EEEE\",\n  long: \"y'年'M'月'd'日'\",\n  medium: 'yyyy-MM-dd',\n  short: 'yy-MM-dd'\n};\nvar timeFormats = {\n  full: 'zzzz a h:mm:ss',\n  long: 'z a h:mm:ss',\n  medium: 'a h:mm:ss',\n  short: 'a h:mm'\n};\nvar dateTimeFormats = {\n  full: '{{date}} {{time}}',\n  long: '{{date}} {{time}}',\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Chinese Traditional locale.\n * @language Chinese Traditional\n * @iso-639-2 zho\n * <AUTHOR> [@tpai]{@link https://github.com/tpai}\n * <AUTHOR> [@jackhsu978]{@link https://github.com/jackhsu978}\n * <AUTHOR> [@skyuplam]{@link https://github.com/skyuplam}\n */\nvar locale = {\n  code: 'zh-TW',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 1\n    /* Monday */\n    ,\n    firstWeekContainsDate: 4\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"'上個'eeee p\",\n  yesterday: \"'昨天' p\",\n  today: \"'今天' p\",\n  tomorrow: \"'明天' p\",\n  nextWeek: \"'下個'eeee p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingValues", "defaultFormattingWidth", "module", "_interopRequireDefault", "matchPattern", "parsePattern", "valueCallback", "parseInt", "matchPatterns", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "index", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "xHours", "aboutXHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "token", "count", "result", "tokenValue", "replace", "String", "addSuffix", "comparison", "date", "formats", "full", "long", "medium", "time", "dateTime", "_index2", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "weekStartsOn", "firstWeekContainsDate", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "_date", "_baseDate", "_options"], "sourceRoot": ""}
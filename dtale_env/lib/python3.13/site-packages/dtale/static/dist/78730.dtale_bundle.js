"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[78730],{48047:(e,d,n)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.default=void 0;var t,a=(t=n(51545))&&t.__esModule?t:{default:t};var r={ordinalNumber:function(e,d){return Number(e)+"."},era:(0,a.default)({values:{narrow:["ie.","isz."],abbreviated:["i. e.","i. sz."],wide:["<PERSON><PERSON><PERSON> el<PERSON>tt","időszámításunk szerint"]},defaultWidth:"wide"}),quarter:(0,a.default)({values:{narrow:["1.","2.","3.","4."],abbreviated:["1. n.év","2. n.év","3. n.év","4. n.év"],wide:["1. negyedév","2. negyedév","3. negyedév","4. negyedév"]},defaultWidth:"wide",argumentCallback:function(e){return e-1},formattingValues:{narrow:["I.","II.","III.","IV."],abbreviated:["I. n.év","II. n.év","III. n.év","IV. n.év"],wide:["I. negyedév","II. negyedév","III. negyedév","IV. negyedév"]},defaultFormattingWidth:"wide"}),month:(0,a.default)({values:{narrow:["J","F","M","Á","M","J","J","A","Sz","O","N","D"],abbreviated:["jan.","febr.","márc.","ápr.","máj.","jún.","júl.","aug.","szept.","okt.","nov.","dec."],wide:["január","február","március","április","május","június","július","augusztus","szeptember","október","november","december"]},defaultWidth:"wide"}),day:(0,a.default)({values:{narrow:["V","H","K","Sz","Cs","P","Sz"],short:["V","H","K","Sze","Cs","P","Szo"],abbreviated:["V","H","K","Sze","Cs","P","Szo"],wide:["vasárnap","hétfő","kedd","szerda","csütörtök","péntek","szombat"]},defaultWidth:"wide"}),dayPeriod:(0,a.default)({values:{narrow:{am:"de.",pm:"du.",midnight:"éjfél",noon:"dél",morning:"reggel",afternoon:"du.",evening:"este",night:"éjjel"},abbreviated:{am:"de.",pm:"du.",midnight:"éjfél",noon:"dél",morning:"reggel",afternoon:"du.",evening:"este",night:"éjjel"},wide:{am:"de.",pm:"du.",midnight:"éjfél",noon:"dél",morning:"reggel",afternoon:"délután",evening:"este",night:"éjjel"}},defaultWidth:"wide"})};d.default=r,e.exports=d.default}}]);
//# sourceMappingURL=78730.dtale_bundle.js.map
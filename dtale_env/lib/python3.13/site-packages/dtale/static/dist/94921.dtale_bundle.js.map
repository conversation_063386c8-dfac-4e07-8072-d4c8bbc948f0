{"version": 3, "file": "94921.dtale_bundle.js", "mappings": "8FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,SAAU,SAAkBC,GAC1B,IAAIC,EAAUD,EAAKE,YAEnB,MAAO,KADgB,IAAZD,GAA6B,IAAZA,EAAgB,SAAW,UACnC,eACtB,EACAE,UAAW,eACXC,MAAO,cACPC,SAAU,gBACVC,SAAU,cACVC,MAAO,KAaLC,EAViB,SAAwBC,EAAOT,EAAMU,EAAWC,GACnE,IAAIC,EAASd,EAAqBW,GAElC,MAAsB,mBAAXG,EACFA,EAAOZ,GAGTY,CACT,EAGAhB,EAAA,QAAkBY,EAClBK,EAAOjB,QAAUA,EAAQkB,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/pt/_lib/formatRelative/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var weekday = date.getUTCDay();\n    var last = weekday === 0 || weekday === 6 ? 'último' : 'última';\n    return \"'\" + last + \"' eeee 'às' p\";\n  },\n  yesterday: \"'ontem às' p\",\n  today: \"'hoje às' p\",\n  tomorrow: \"'amanhã às' p\",\n  nextWeek: \"eeee 'às' p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n\n  if (typeof format === 'function') {\n    return format(date);\n  }\n\n  return format;\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatRelativeLocale", "lastWeek", "date", "weekday", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "_default", "token", "_baseDate", "_options", "format", "module", "default"], "sourceRoot": ""}
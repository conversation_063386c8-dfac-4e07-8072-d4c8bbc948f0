{"version": 3, "file": "93070.dtale_bundle.js", "mappings": "8HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIE,EAASC,EAAuB,EAAQ,QAI5C,SAASA,EAAuBC,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAE9F,IAiGIG,EA1CQ,CACVC,eAAe,EA5DHL,EAAuB,EAAQ,MA4DhBG,SAAS,CAClCG,aAzD4B,sBA0D5BC,aAzD4B,OA0D5BC,cAAe,SAAuBV,GACpC,OAAOW,SAASX,EAAO,GACzB,IAEFY,KAAK,EAAIX,EAAOI,SAAS,CACvBQ,cA9DmB,CACrBC,OAAQ,uBACRC,YAAa,uBACbC,KAAM,qEA4DJC,kBAAmB,OACnBC,cA3DmB,CACrBC,IAAK,CAAC,MAAO,QA2DXC,kBAAmB,QAErBC,SAAS,EAAIpB,EAAOI,SAAS,CAC3BQ,cA5DuB,CACzBC,OAAQ,WACRC,YAAa,wBACbC,KAAM,0BA0DJC,kBAAmB,OACnBC,cAzDuB,CACzBC,IAAK,CAAC,KAAM,KAAM,KAAM,OAyDtBC,kBAAmB,MACnBV,cAAe,SAAuBY,GACpC,OAAOA,EAAQ,CACjB,IAEFC,OAAO,EAAItB,EAAOI,SAAS,CACzBQ,cA7DqB,CACvBC,OAAQ,sCACRC,YAAa,sDACbC,KAAM,0FA2DJC,kBAAmB,OACnBC,cA1DqB,CACvBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACtFC,YAAa,CAAC,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,SACjHI,IAAK,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,QAwDjFC,kBAAmB,QAErBI,KAAK,EAAIvB,EAAOI,SAAS,CACvBQ,cAzDmB,CACrBC,OAAQ,oBACRW,MAAO,2BACPT,KAAM,+DAuDJC,kBAAmB,OACnBC,cAtDmB,CACrBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACnDW,MAAO,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QACxDN,IAAK,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,YAoDtEC,kBAAmB,QAErBM,WAAW,EAAIzB,EAAOI,SAAS,CAC7BQ,cArDyB,CAC3BC,OAAQ,gHACRE,KAAM,gHACNG,IAAK,iHAmDHF,kBAAmB,OACnBC,cAlDyB,CAC3BC,IAAK,CACHQ,GAAI,OACJC,GAAI,OACJC,SAAU,aACVC,KAAM,WACNC,QAAS,OACTC,UAAW,OACXC,QAAS,OACTC,MAAO,SA0CPd,kBAAmB,SAIvBrB,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,kBC9GzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCI,EAF5BF,GAE4BE,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAAIiC,EAAqB,CAAC,aAAc,aAAc,aAAc,aAAc,aAAc,SAAU,WAO1G,SAASC,EAASb,GAEhB,MAAO,IADOY,EAAmBZ,GACV,gBACzB,CAOA,IAAIc,EAAuB,CACzBC,SAAU,SAAkBC,EAAMC,EAAUC,GAC1C,IAAIlB,EAAMgB,EAAKG,YAEf,OAAI,EAAI1C,EAAOI,SAASmC,EAAMC,EAAUC,GAC/BL,EAASb,GApBtB,SAAmBA,GAEjB,MAAO,UADOY,EAAmBZ,GACJ,gBAC/B,CAmBaoB,CAAUpB,EAErB,EACAqB,UAAW,sBACXC,MAAO,uBACPC,SAAU,uBACVC,SAAU,SAAkBR,EAAMC,EAAUC,GAC1C,IAAIlB,EAAMgB,EAAKG,YAEf,OAAI,EAAI1C,EAAOI,SAASmC,EAAMC,EAAUC,GAC/BL,EAASb,GAtBtB,SAAmBA,GAEjB,MAAO,WADOY,EAAmBZ,GACH,gBAChC,CAqBayB,CAAUzB,EAErB,EACA0B,MAAO,KAaL5C,EAViB,SAAwB6C,EAAOX,EAAMC,EAAUC,GAClE,IAAIU,EAASd,EAAqBa,GAElC,MAAsB,mBAAXC,EACFA,EAAOZ,EAAMC,EAAUC,GAGzBU,CACT,EAGArD,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,gBC/DzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIsD,EAAuB,CACzBC,iBAAkB,CAChBC,QAAS,CACPC,IAAK,iBACLC,mBAAoB,yBACpBC,iBAAkB,yBAClBC,eAAgB,0BAElBC,OAAQ,CACNJ,IAAK,sBACLC,mBAAoB,4BACpBC,iBAAkB,4BAClBC,eAAgB,8BAGpBE,SAAU,CACRN,QAAS,CACPE,mBAAoB,mBACpBC,iBAAkB,mBAClBC,eAAgB,oBAElBG,KAAM,CACJL,mBAAoB,yBACpBC,iBAAkB,yBAClBC,eAAgB,0BAElBC,OAAQ,CACNH,mBAAoB,4BACpBC,iBAAkB,4BAClBC,eAAgB,8BAGpBI,YAAa,SAAqBrB,GAChC,OAAIA,SAA0CA,EAAQsB,UAChDtB,EAAQuB,YAAcvB,EAAQuB,WAAa,EACtC,qBAEA,oBAIJ,aACT,EACAC,iBAAkB,CAChBX,QAAS,CACPC,IAAK,gBACLC,mBAAoB,wBACpBC,iBAAkB,wBAClBC,eAAgB,yBAElBC,OAAQ,CACNJ,IAAK,gBACLC,mBAAoB,yBACpBC,iBAAkB,yBAClBC,eAAgB,2BAGpBQ,SAAU,CACRZ,QAAS,CACPE,mBAAoB,kBACpBC,iBAAkB,kBAClBC,eAAgB,mBAElBG,KAAM,CACJL,mBAAoB,wBACpBC,iBAAkB,wBAClBC,eAAgB,yBAElBC,OAAQ,CACNH,mBAAoB,2BACpBC,iBAAkB,2BAClBC,eAAgB,6BAGpBS,YAAa,CACXb,QAAS,CACPE,mBAAoB,0BACpBC,iBAAkB,0BAClBC,eAAgB,2BAElBC,OAAQ,CACNH,mBAAoB,mCACpBC,iBAAkB,mCAClBC,eAAgB,qCAGpBU,OAAQ,CACNd,QAAS,CACPE,mBAAoB,kBACpBC,iBAAkB,kBAClBC,eAAgB,oBAGpBW,MAAO,CACLf,QAAS,CACPE,mBAAoB,gBACpBC,iBAAkB,gBAClBC,eAAgB,iBAElBC,OAAQ,CACNH,mBAAoB,yBACpBC,iBAAkB,yBAClBC,eAAgB,2BAGpBY,YAAa,CACXC,KAAM,QACNhB,IAAK,iBACLN,MAAO,0BAETuB,OAAQ,CACND,KAAM,QACNhB,IAAK,SACLN,MAAO,kBAETwB,aAAc,CACZnB,QAAS,CACPE,mBAAoB,uBACpBC,iBAAkB,uBAClBC,eAAgB,wBAElBC,OAAQ,CACNH,mBAAoB,gCACpBC,iBAAkB,gCAClBC,eAAgB,kCAGpBgB,QAAS,CACPpB,QAAS,CACPE,mBAAoB,eACpBC,iBAAkB,eAClBC,eAAgB,iBAGpBiB,YAAa,CACXrB,QAAS,CACPE,mBAAoB,wBACpBC,iBAAkB,wBAClBC,eAAgB,yBAElBC,OAAQ,CACNH,mBAAoB,iCACpBC,iBAAkB,iCAClBC,eAAgB,mCAGpBkB,OAAQ,CACNtB,QAAS,CACPE,mBAAoB,gBACpBC,iBAAkB,gBAClBC,eAAgB,iBAElBC,OAAQ,CACNH,mBAAoB,yBACpBC,iBAAkB,yBAClBC,eAAgB,2BAGpBmB,WAAY,CACVvB,QAAS,CACPE,mBAAoB,yBACpBC,iBAAkB,yBAClBC,eAAgB,0BAElBC,OAAQ,CACNH,mBAAoB,yBACpBC,iBAAkB,yBAClBC,eAAgB,2BAGpBoB,aAAc,CACZxB,QAAS,CACPE,mBAAoB,wBACpBC,iBAAkB,wBAClBC,eAAgB,yBAElBC,OAAQ,CACNH,mBAAoB,yBACpBC,iBAAkB,yBAClBC,eAAgB,4BAKtB,SAASqB,EAAWC,EAAQC,GAE1B,GAAID,EAAOzB,KAAiB,IAAV0B,EAAa,OAAOD,EAAOzB,IAC7C,IAAI2B,EAAQD,EAAQ,GAChBE,EAASF,EAAQ,IAErB,OAAc,IAAVC,GAA0B,KAAXC,EACVH,EAAOxB,mBAAmB4B,QAAQ,YAAaC,OAAOJ,IACpDC,GAAS,GAAKA,GAAS,IAAMC,EAAS,IAAMA,EAAS,IACvDH,EAAOvB,iBAAiB2B,QAAQ,YAAaC,OAAOJ,IAEpDD,EAAOtB,eAAe0B,QAAQ,YAAaC,OAAOJ,GAE7D,CAEA,IA2BI5E,EA3BiB,SAAwB6C,EAAO+B,EAAOxC,GACzD,IAAI6C,EAAalC,EAAqBF,GACtC,MAA0B,mBAAfoC,EAAkCA,EAAW7C,GAEhC,UAApB6C,EAAWf,KACI,IAAVU,EAAcK,EAAW/B,IAAM+B,EAAWrC,MAAMmC,QAAQ,YAAaC,OAAOJ,IAGjFxC,SAA0CA,EAAQsB,UAChDtB,EAAQuB,YAAcvB,EAAQuB,WAAa,EACzCsB,EAAW3B,OACNoB,EAAWO,EAAW3B,OAAQsB,GAE9BF,EAAWO,EAAWhC,QAAS2B,GAAS,SAG7CK,EAAWzB,KACNkB,EAAWO,EAAWzB,KAAMoB,GAE5BF,EAAWO,EAAWhC,QAAS2B,GAAS,SAI5CF,EAAWO,EAAWhC,QAAS2B,EAE1C,EAGAnF,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,kBCxOzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIE,EAASC,EAAuB,EAAQ,QAExCsF,EAAUtF,EAAuB,EAAQ,QAEzCuF,EAAUvF,EAAuB,EAAQ,QAEzCwF,EAAUxF,EAAuB,EAAQ,QAEzCyF,EAAUzF,EAAuB,EAAQ,QAE7C,SAASA,EAAuBC,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAU9F,IAcIG,EAdS,CACXsF,KAAM,KACNC,eAAgB5F,EAAOI,QACvByF,WAAYN,EAAQnF,QACpB0F,eAAgBN,EAAQpF,QACxB2F,SAAUN,EAAQrF,QAClB4F,MAAON,EAAQtF,QACfqC,QAAS,CACPwD,aAAc,EAGdC,sBAAuB,IAI3BpG,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,kBCzCzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCI,EAF5BF,GAE4BE,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAsEIiG,EAAW,CACb,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,EAAG,MACH,GAAI,MACJ,GAAI,MACJ,GAAI,MACJ,GAAI,MACJ,GAAI,MACJ,GAAI,MACJ,GAAI,MACJ,GAAI,MACJ,GAAI,MACJ,IAAK,OAyCH9F,EA9BW,CACbC,cATkB,SAAuB8F,EAAaC,GACtD,IAAIC,EAASC,OAAOH,GAEhBI,EAAIF,GAAU,IAAM,IAAM,KAE9B,OAAOA,GADMH,EAASG,IAAWH,EAFrBG,EAAS,KAE+BE,GAAKL,EAASK,IAAM,GAE1E,EAIE7F,KAAK,EAAIX,EAAOI,SAAS,CACvBqG,OAxGY,CACd5F,OAAQ,CAAC,SAAU,QACnBC,YAAa,CAAC,SAAU,QACxBC,KAAM,CAAC,2BAA4B,qBAsGjC2F,aAAc,SAEhBtF,SAAS,EAAIpB,EAAOI,SAAS,CAC3BqG,OAvGgB,CAClB5F,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,YAAa,YAAa,YAAa,aACrDC,KAAM,CAAC,cAAe,cAAe,cAAe,gBAqGlD2F,aAAc,OACdC,iBAAkB,SAA0BvF,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAItB,EAAOI,SAAS,CACzBqG,OAzGc,CAChB5F,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAC3FC,KAAM,CAAC,SAAU,QAAS,SAAU,QAAS,QAAS,SAAU,QAAS,QAAS,WAAY,QAAS,SAAU,cAuG/G2F,aAAc,OACdE,iBAtGwB,CAC1B/F,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAC3FC,KAAM,CAAC,SAAU,QAAS,SAAU,QAAS,QAAS,SAAU,QAAS,QAAS,WAAY,QAAS,SAAU,cAoG/G8F,uBAAwB,SAE1BtF,KAAK,EAAIvB,EAAOI,SAAS,CACvBqG,OArGY,CACd5F,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCW,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CV,YAAa,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAClDC,KAAM,CAAC,WAAY,WAAY,WAAY,WAAY,WAAY,OAAQ,UAkGzE2F,aAAc,SAEhBjF,WAAW,EAAIzB,EAAOI,SAAS,CAC7BqG,OAnGkB,CACpB5F,OAAQ,CACNa,GAAI,KACJC,GAAI,KACJC,SAAU,aACVC,KAAM,MACNC,QAAS,MACTC,UAAW,SACXC,QAAS,MACTC,MAAO,OAETlB,KAAM,CACJW,GAAI,KACJC,GAAI,KACJC,SAAU,aACVC,KAAM,MACNC,QAAS,MACTC,UAAW,SACXC,QAAS,MACTC,MAAO,QAiFPyE,aAAc,MACdE,iBA/E4B,CAC9B/F,OAAQ,CACNa,GAAI,KACJC,GAAI,KACJC,SAAU,gBACVC,KAAM,MACNC,QAAS,MACTC,UAAW,MACXC,QAAS,MACTC,MAAO,OAETlB,KAAM,CACJW,GAAI,KACJC,GAAI,KACJC,SAAU,gBACVC,KAAM,QACNC,QAAS,WACTC,UAAW,SACXC,QAAS,QACTC,MAAO,UA6DP4E,uBAAwB,UAI5B/G,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,kBC9IzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,QAQA,SAAuBgH,EAAeC,EAAgBtE,IACpD,EAAIzC,EAAOI,SAAS,EAAG4G,WACvB,IAAIC,GAAsB,EAAI1B,EAAQnF,SAAS0G,EAAerE,GAC1DyE,GAAuB,EAAI3B,EAAQnF,SAAS2G,EAAgBtE,GAChE,OAAOwE,EAAoBE,YAAcD,EAAqBC,SAChE,EAXA,IAAInH,EAASC,EAAuB,EAAQ,QAExCsF,EAAUtF,EAAuB,EAAQ,QAE7C,SAASA,EAAuBC,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAS9FgC,EAAOpC,QAAUA,EAAQM,O,kBClBzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCI,EAF5BF,GAE4BE,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IA6BIG,EAda,CACfkC,MAAM,EAAIvC,EAAOI,SAAS,CACxBgH,QAjBc,CAChBC,KAAM,uBACNC,KAAM,iBACNC,OAAQ,eACR/F,MAAO,cAcLkF,aAAc,SAEhBc,MAAM,EAAIxH,EAAOI,SAAS,CACxBgH,QAfc,CAChBC,KAAM,eACNC,KAAM,YACNC,OAAQ,UACR/F,MAAO,QAYLkF,aAAc,SAEhBe,UAAU,EAAIzH,EAAOI,SAAS,CAC5BgH,QAbkB,CACpBlG,IAAK,sBAaHwF,aAAc,SAIlB5G,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/kk/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/kk/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/locale/kk/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/kk/index.js", "webpack://dtale/./node_modules/date-fns/locale/kk/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/_lib/isSameUTCWeek/index.js", "webpack://dtale/./node_modules/date-fns/locale/kk/_lib/formatLong/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)(-?(ші|шы))?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^((б )?з\\.?\\s?д\\.?)/i,\n  abbreviated: /^((б )?з\\.?\\s?д\\.?)/i,\n  wide: /^(біздің заманымызға дейін|біздің заманымыз|біздің заманымыздан)/i\n};\nvar parseEraPatterns = {\n  any: [/^б/i, /^з/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234](-?ші)? тоқ.?/i,\n  wide: /^[1234](-?ші)? тоқсан/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^(қ|а|н|с|м|мау|ш|т|қыр|қаз|қар|ж)/i,\n  abbreviated: /^(қаң|ақп|нау|сәу|мам|мау|шіл|там|қыр|қаз|қар|жел)/i,\n  wide: /^(қаңтар|ақпан|наурыз|сәуір|мамыр|маусым|шілде|тамыз|қыркүйек|қазан|қараша|желтоқсан)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^қ/i, /^а/i, /^н/i, /^с/i, /^м/i, /^м/i, /^ш/i, /^т/i, /^қ/i, /^қ/i, /^қ/i, /^ж/i],\n  abbreviated: [/^қаң/i, /^ақп/i, /^нау/i, /^сәу/i, /^мам/i, /^мау/i, /^шіл/i, /^там/i, /^қыр/i, /^қаз/i, /^қар/i, /^жел/i],\n  any: [/^қ/i, /^а/i, /^н/i, /^с/i, /^м/i, /^м/i, /^ш/i, /^т/i, /^қ/i, /^қ/i, /^қ/i, /^ж/i]\n};\nvar matchDayPatterns = {\n  narrow: /^(ж|д|с|с|б|ж|с)/i,\n  short: /^(жс|дс|сс|ср|бс|жм|сб)/i,\n  wide: /^(жексенбі|дүйсенбі|сейсенбі|сәрсенбі|бейсенбі|жұма|сенбі)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^ж/i, /^д/i, /^с/i, /^с/i, /^б/i, /^ж/i, /^с/i],\n  short: [/^жс/i, /^дс/i, /^сс/i, /^ср/i, /^бс/i, /^жм/i, /^сб/i],\n  any: [/^ж[ек]/i, /^д[үй]/i, /^сe[й]/i, /^сә[р]/i, /^б[ей]/i, /^ж[ұм]/i, /^се[н]/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^Т\\.?\\s?[ДК]\\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\\.?)/i,\n  wide: /^Т\\.?\\s?[ДК]\\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\\.?)/i,\n  any: /^Т\\.?\\s?[ДК]\\.?|түн ортасында|((түсте|таңертең|таңда|таңертең|таңмен|таң|күндіз|күн|кеште|кеш|түнде|түн)\\.?)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^ТД/i,\n    pm: /^ТК/i,\n    midnight: /^түн орта/i,\n    noon: /^күндіз/i,\n    morning: /таң/i,\n    afternoon: /түс/i,\n    evening: /кеш/i,\n    night: /түн/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../../_lib/isSameUTCWeek/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar accusativeWeekdays = ['жексенбіде', 'дүйсенбіде', 'сейсенбіде', 'сәрсенбіде', 'бейсенбіде', 'жұмада', 'сенбіде'];\n\nfunction _lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'өткен \" + weekday + \" сағат' p'-де'\";\n}\n\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'\" + weekday + \" сағат' p'-де'\";\n}\n\nfunction _nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'келесі \" + weekday + \" сағат' p'-де'\";\n}\n\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n\n    if ((0, _index.default)(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'кеше сағат' p'-де'\",\n  today: \"'бүгін сағат' p'-де'\",\n  tomorrow: \"'ертең сағат' p'-де'\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n\n    if ((0, _index.default)(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    regular: {\n      one: '1 секундтан аз',\n      singularNominative: '{{count}} секундтан аз',\n      singularGenitive: '{{count}} секундтан аз',\n      pluralGenitive: '{{count}} секундтан аз'\n    },\n    future: {\n      one: 'бір секундтан кейін',\n      singularNominative: '{{count}} секундтан кейін',\n      singularGenitive: '{{count}} секундтан кейін',\n      pluralGenitive: '{{count}} секундтан кейін'\n    }\n  },\n  xSeconds: {\n    regular: {\n      singularNominative: '{{count}} секунд',\n      singularGenitive: '{{count}} секунд',\n      pluralGenitive: '{{count}} секунд'\n    },\n    past: {\n      singularNominative: '{{count}} секунд бұрын',\n      singularGenitive: '{{count}} секунд бұрын',\n      pluralGenitive: '{{count}} секунд бұрын'\n    },\n    future: {\n      singularNominative: '{{count}} секундтан кейін',\n      singularGenitive: '{{count}} секундтан кейін',\n      pluralGenitive: '{{count}} секундтан кейін'\n    }\n  },\n  halfAMinute: function halfAMinute(options) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        return 'жарты минут ішінде';\n      } else {\n        return 'жарты минут бұрын';\n      }\n    }\n\n    return 'жарты минут';\n  },\n  lessThanXMinutes: {\n    regular: {\n      one: '1 минуттан аз',\n      singularNominative: '{{count}} минуттан аз',\n      singularGenitive: '{{count}} минуттан аз',\n      pluralGenitive: '{{count}} минуттан аз'\n    },\n    future: {\n      one: 'минуттан кем ',\n      singularNominative: '{{count}} минуттан кем',\n      singularGenitive: '{{count}} минуттан кем',\n      pluralGenitive: '{{count}} минуттан кем'\n    }\n  },\n  xMinutes: {\n    regular: {\n      singularNominative: '{{count}} минут',\n      singularGenitive: '{{count}} минут',\n      pluralGenitive: '{{count}} минут'\n    },\n    past: {\n      singularNominative: '{{count}} минут бұрын',\n      singularGenitive: '{{count}} минут бұрын',\n      pluralGenitive: '{{count}} минут бұрын'\n    },\n    future: {\n      singularNominative: '{{count}} минуттан кейін',\n      singularGenitive: '{{count}} минуттан кейін',\n      pluralGenitive: '{{count}} минуттан кейін'\n    }\n  },\n  aboutXHours: {\n    regular: {\n      singularNominative: 'шамамен {{count}} сағат',\n      singularGenitive: 'шамамен {{count}} сағат',\n      pluralGenitive: 'шамамен {{count}} сағат'\n    },\n    future: {\n      singularNominative: 'шамамен {{count}} сағаттан кейін',\n      singularGenitive: 'шамамен {{count}} сағаттан кейін',\n      pluralGenitive: 'шамамен {{count}} сағаттан кейін'\n    }\n  },\n  xHours: {\n    regular: {\n      singularNominative: '{{count}} сағат',\n      singularGenitive: '{{count}} сағат',\n      pluralGenitive: '{{count}} сағат'\n    }\n  },\n  xDays: {\n    regular: {\n      singularNominative: '{{count}} күн',\n      singularGenitive: '{{count}} күн',\n      pluralGenitive: '{{count}} күн'\n    },\n    future: {\n      singularNominative: '{{count}} күннен кейін',\n      singularGenitive: '{{count}} күннен кейін',\n      pluralGenitive: '{{count}} күннен кейін'\n    }\n  },\n  aboutXWeeks: {\n    type: 'weeks',\n    one: 'шамамен 1 апта',\n    other: 'шамамен {{count}} апта'\n  },\n  xWeeks: {\n    type: 'weeks',\n    one: '1 апта',\n    other: '{{count}} апта'\n  },\n  aboutXMonths: {\n    regular: {\n      singularNominative: 'шамамен {{count}} ай',\n      singularGenitive: 'шамамен {{count}} ай',\n      pluralGenitive: 'шамамен {{count}} ай'\n    },\n    future: {\n      singularNominative: 'шамамен {{count}} айдан кейін',\n      singularGenitive: 'шамамен {{count}} айдан кейін',\n      pluralGenitive: 'шамамен {{count}} айдан кейін'\n    }\n  },\n  xMonths: {\n    regular: {\n      singularNominative: '{{count}} ай',\n      singularGenitive: '{{count}} ай',\n      pluralGenitive: '{{count}} ай'\n    }\n  },\n  aboutXYears: {\n    regular: {\n      singularNominative: 'шамамен {{count}} жыл',\n      singularGenitive: 'шамамен {{count}} жыл',\n      pluralGenitive: 'шамамен {{count}} жыл'\n    },\n    future: {\n      singularNominative: 'шамамен {{count}} жылдан кейін',\n      singularGenitive: 'шамамен {{count}} жылдан кейін',\n      pluralGenitive: 'шамамен {{count}} жылдан кейін'\n    }\n  },\n  xYears: {\n    regular: {\n      singularNominative: '{{count}} жыл',\n      singularGenitive: '{{count}} жыл',\n      pluralGenitive: '{{count}} жыл'\n    },\n    future: {\n      singularNominative: '{{count}} жылдан кейін',\n      singularGenitive: '{{count}} жылдан кейін',\n      pluralGenitive: '{{count}} жылдан кейін'\n    }\n  },\n  overXYears: {\n    regular: {\n      singularNominative: '{{count}} жылдан астам',\n      singularGenitive: '{{count}} жылдан астам',\n      pluralGenitive: '{{count}} жылдан астам'\n    },\n    future: {\n      singularNominative: '{{count}} жылдан астам',\n      singularGenitive: '{{count}} жылдан астам',\n      pluralGenitive: '{{count}} жылдан астам'\n    }\n  },\n  almostXYears: {\n    regular: {\n      singularNominative: '{{count}} жылға жақын',\n      singularGenitive: '{{count}} жылға жақын',\n      pluralGenitive: '{{count}} жылға жақын'\n    },\n    future: {\n      singularNominative: '{{count}} жылдан кейін',\n      singularGenitive: '{{count}} жылдан кейін',\n      pluralGenitive: '{{count}} жылдан кейін'\n    }\n  }\n};\n\nfunction declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one && count === 1) return scheme.one;\n  var rem10 = count % 10;\n  var rem100 = count % 100; // 1, 21, 31, ...\n\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace('{{count}}', String(count)); // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace('{{count}}', String(count)); // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace('{{count}}', String(count));\n  }\n}\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'function') return tokenValue(options);\n\n  if (tokenValue.type === 'weeks') {\n    return count === 1 ? tokenValue.one : tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      if (tokenValue.future) {\n        return declension(tokenValue.future, count);\n      } else {\n        return declension(tokenValue.regular, count) + ' кейін';\n      }\n    } else {\n      if (tokenValue.past) {\n        return declension(tokenValue.past, count);\n      } else {\n        return declension(tokenValue.regular, count) + ' бұрын';\n      }\n    }\n  } else {\n    return declension(tokenValue.regular, count);\n  }\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Kazakh locale.\n * @language Kazakh\n * @iso-639-2 kaz\n * <AUTHOR> [@drugoi]{@link https://github.com/drugoi}\n */\nvar locale = {\n  code: 'kk',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 1\n    /* Monday */\n    ,\n    firstWeekContainsDate: 1\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['б.з.д.', 'б.з.'],\n  abbreviated: ['б.з.д.', 'б.з.'],\n  wide: ['біздің заманымызға дейін', 'біздің заманымыз']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-ші тоқ.', '2-ші тоқ.', '3-ші тоқ.', '4-ші тоқ.'],\n  wide: ['1-ші тоқсан', '2-ші тоқсан', '3-ші тоқсан', '4-ші тоқсан']\n};\nvar monthValues = {\n  narrow: ['Қ', 'А', 'Н', 'С', 'М', 'М', 'Ш', 'Т', 'Қ', 'Қ', 'Қ', 'Ж'],\n  abbreviated: ['қаң', 'ақп', 'нау', 'сәу', 'мам', 'мау', 'шіл', 'там', 'қыр', 'қаз', 'қар', 'жел'],\n  wide: ['қаңтар', 'ақпан', 'наурыз', 'сәуір', 'мамыр', 'маусым', 'шілде', 'тамыз', 'қыркүйек', 'қазан', 'қараша', 'желтоқсан']\n};\nvar formattingMonthValues = {\n  narrow: ['Қ', 'А', 'Н', 'С', 'М', 'М', 'Ш', 'Т', 'Қ', 'Қ', 'Қ', 'Ж'],\n  abbreviated: ['қаң', 'ақп', 'нау', 'сәу', 'мам', 'мау', 'шіл', 'там', 'қыр', 'қаз', 'қар', 'жел'],\n  wide: ['қаңтар', 'ақпан', 'наурыз', 'сәуір', 'мамыр', 'маусым', 'шілде', 'тамыз', 'қыркүйек', 'қазан', 'қараша', 'желтоқсан']\n};\nvar dayValues = {\n  narrow: ['Ж', 'Д', 'С', 'С', 'Б', 'Ж', 'С'],\n  short: ['жс', 'дс', 'сс', 'ср', 'бс', 'жм', 'сб'],\n  abbreviated: ['жс', 'дс', 'сс', 'ср', 'бс', 'жм', 'сб'],\n  wide: ['жексенбі', 'дүйсенбі', 'сейсенбі', 'сәрсенбі', 'бейсенбі', 'жұма', 'сенбі']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ТД',\n    pm: 'ТК',\n    midnight: 'түн ортасы',\n    noon: 'түс',\n    morning: 'таң',\n    afternoon: 'күндіз',\n    evening: 'кеш',\n    night: 'түн'\n  },\n  wide: {\n    am: 'ТД',\n    pm: 'ТК',\n    midnight: 'түн ортасы',\n    noon: 'түс',\n    morning: 'таң',\n    afternoon: 'күндіз',\n    evening: 'кеш',\n    night: 'түн'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ТД',\n    pm: 'ТК',\n    midnight: 'түн ортасында',\n    noon: 'түс',\n    morning: 'таң',\n    afternoon: 'күн',\n    evening: 'кеш',\n    night: 'түн'\n  },\n  wide: {\n    am: 'ТД',\n    pm: 'ТК',\n    midnight: 'түн ортасында',\n    noon: 'түсте',\n    morning: 'таңертең',\n    afternoon: 'күндіз',\n    evening: 'кеште',\n    night: 'түнде'\n  }\n};\nvar suffixes = {\n  0: '-ші',\n  1: '-ші',\n  2: '-ші',\n  3: '-ші',\n  4: '-ші',\n  5: '-ші',\n  6: '-шы',\n  7: '-ші',\n  8: '-ші',\n  9: '-шы',\n  10: '-шы',\n  20: '-шы',\n  30: '-шы',\n  40: '-шы',\n  50: '-ші',\n  60: '-шы',\n  70: '-ші',\n  80: '-ші',\n  90: '-шы',\n  100: '-ші'\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var mod10 = number % 10;\n  var b = number >= 100 ? 100 : null;\n  var suffix = suffixes[number] || suffixes[mod10] || b && suffixes[b] || '';\n  return number + suffix;\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = isSameUTCWeek;\n\nvar _index = _interopRequireDefault(require(\"../requiredArgs/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../startOfUTCWeek/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction isSameUTCWeek(dirtyDateLeft, dirtyDateRight, options) {\n  (0, _index.default)(2, arguments);\n  var dateLeftStartOfWeek = (0, _index2.default)(dirtyDateLeft, options);\n  var dateRightStartOfWeek = (0, _index2.default)(dirtyDateRight, options);\n  return dateLeftStartOfWeek.getTime() === dateRightStartOfWeek.getTime();\n}\n\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: \"EEEE, do MMMM y 'ж.'\",\n  long: \"do MMMM y 'ж.'\",\n  medium: \"d MMM y 'ж.'\",\n  short: 'dd.MM.yyyy'\n};\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  any: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'any'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "_index", "_interopRequireDefault", "obj", "__esModule", "default", "_default", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "parseInt", "era", "matchPatterns", "narrow", "abbreviated", "wide", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "quarter", "index", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "module", "accusativeWeekdays", "thisWeek", "formatRelativeLocale", "lastWeek", "date", "baseDate", "options", "getUTCDay", "_lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "_nextWeek", "other", "token", "format", "formatDistanceLocale", "lessThanXSeconds", "regular", "one", "singularNominative", "singularGenitive", "pluralGenitive", "future", "xSeconds", "past", "halfAMinute", "addSuffix", "comparison", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "type", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "declension", "scheme", "count", "rem10", "rem100", "replace", "String", "tokenValue", "_index2", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "weekStartsOn", "firstWeekContainsDate", "suffixes", "dirtyNumber", "_options", "number", "Number", "b", "values", "defaultWidth", "argument<PERSON>allback", "formattingValues", "defaultFormattingWidth", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeftStartOfWeek", "dateRightStartOfWeek", "getTime", "formats", "full", "long", "medium", "time", "dateTime"], "sourceRoot": ""}
{"version": 3, "file": "82012.dtale_bundle.js", "mappings": "8FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,yBACLC,MAAO,iCAETC,SAAU,CACRF,IAAK,YACLC,MAAO,sBAETE,YAAa,mBACbC,iBAAkB,CAChBJ,IAAK,wBACLC,MAAO,gCAETI,SAAU,CACRL,IAAK,aACLC,MAAO,qBAETK,YAAa,CACXN,IAAK,iBACLC,MAAO,0BAETM,OAAQ,CACNP,IAAK,QACLC,MAAO,iBAETO,MAAO,CACLR,IAAK,QACLC,MAAO,mBAETQ,YAAa,CACXT,IAAK,kBACLC,MAAO,4BAETS,OAAQ,CACNV,IAAK,SACLC,MAAO,mBAETU,aAAc,CACZX,IAAK,mBACLC,MAAO,8BAETW,QAAS,CACPZ,IAAK,UACLC,MAAO,qBAETY,YAAa,CACXb,IAAK,kBACLC,MAAO,2BAETa,OAAQ,CACNd,IAAK,SACLC,MAAO,kBAETc,WAAY,CACVf,IAAK,kBACLC,MAAO,2BAETe,aAAc,CACZhB,IAAK,eACLC,MAAO,yBA2BPgB,EAvBiB,SAAwBC,EAAOC,EAAOC,GACzD,IAAIC,EACAC,EAAaxB,EAAqBoB,GAUtC,OAPEG,EADwB,iBAAfC,EACAA,EACU,IAAVH,EACAG,EAAWtB,IAEXsB,EAAWrB,MAAMsB,QAAQ,YAAaC,OAAOL,IAGpDC,SAA0CA,EAAQK,UAChDL,EAAQM,YAAcN,EAAQM,WAAa,EACtC,QAAUL,EAEVA,EAAS,WAIbA,CACT,EAGAzB,EAAA,QAAkBqB,EAClBU,EAAO/B,QAAUA,EAAQgC,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/nl-BE/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'minder dan een seconde',\n    other: 'minder dan {{count}} seconden'\n  },\n  xSeconds: {\n    one: '1 seconde',\n    other: '{{count}} seconden'\n  },\n  halfAMinute: 'een halve minuut',\n  lessThanXMinutes: {\n    one: 'minder dan een minuut',\n    other: 'minder dan {{count}} minuten'\n  },\n  xMinutes: {\n    one: 'een minuut',\n    other: '{{count}} minuten'\n  },\n  aboutXHours: {\n    one: 'ongeveer 1 uur',\n    other: 'ongeveer {{count}} uur'\n  },\n  xHours: {\n    one: '1 uur',\n    other: '{{count}} uur'\n  },\n  xDays: {\n    one: '1 dag',\n    other: '{{count}} dagen'\n  },\n  aboutXWeeks: {\n    one: 'ongeveer 1 week',\n    other: 'ongeveer {{count}} weken'\n  },\n  xWeeks: {\n    one: '1 week',\n    other: '{{count}} weken'\n  },\n  aboutXMonths: {\n    one: 'ongeveer 1 maand',\n    other: 'ongeveer {{count}} maanden'\n  },\n  xMonths: {\n    one: '1 maand',\n    other: '{{count}} maanden'\n  },\n  aboutXYears: {\n    one: 'ongeveer 1 jaar',\n    other: 'ongeveer {{count}} jaar'\n  },\n  xYears: {\n    one: '1 jaar',\n    other: '{{count}} jaar'\n  },\n  overXYears: {\n    one: 'meer dan 1 jaar',\n    other: 'meer dan {{count}} jaar'\n  },\n  almostXYears: {\n    one: 'bijna 1 jaar',\n    other: 'bijna {{count}} jaar'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'over ' + result;\n    } else {\n      return result + ' geleden';\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_default", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison", "module", "default"], "sourceRoot": ""}
{"version": 3, "file": "8078.dtale_bundle.js", "mappings": "4EA6HAA,EAAOC,QAvHP,SAAgBC,GACd,MAAMC,EACJ,26BAWIC,EAAgB,CACpBF,EAAKG,oBACLH,EAAKI,QAAQ,KAAM,KAAM,CACvBC,UAAW,IAEbL,EAAKI,QAAQ,OAAQ,OAAQ,CAC3BC,UAAW,MAGTC,EAAY,CAChBC,UAAW,OACXC,SAAU,CACR,CACEC,MAAO,OACPC,IAAK,MAEP,CACED,MAAO,SACPC,IAAK,UAILC,EAAS,CACbJ,UAAW,SACXE,MAAO,IACPC,IAAK,IACLE,SAAU,CAAC,CACTH,MAAO,QAsBLI,EAAc,CAClBN,UAAW,SACXE,MAAO,WAEHK,EAAQ,CACZL,MAAOT,EAAKe,SAAW,wBACvBC,aAAa,EACbJ,SAAU,CAACZ,EAAKiB,aAEZC,EAAW,CACfX,UAAW,WACXY,cAAe,4CACfT,IAAK,OACLU,SAAU,qDACVR,SAAU,CACRZ,EAAKiB,WACL,CACEV,UAAW,SACXE,MAAO,KACPC,IAAK,KACLU,SAAUnB,EACVW,SAAU,CACRD,EACAE,EACAP,GACAe,OAAOnB,IAEXI,GACAe,OAAOnB,IAEX,MAAO,CACLoB,KAAM,SACNC,QAAS,CACP,MACA,MACA,MACA,SACA,aACA,UACA,MACA,OAEFC,kBAAkB,EAClBJ,SAAUnB,EACVwB,QAAS,2BACTb,SAAU,CACRD,EACAE,EACAb,EAAK0B,YAnEM,CACbnB,UAAW,SACXF,UAAW,EAEXG,SAAU,CACR,CAEEC,MAAO,mBAET,CAEEA,MAAO,WAET,CAEEA,MAAO,YAsDTK,EACAI,EACAZ,GACAe,OAAOnB,GAEb,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/delphi.js"], "sourcesContent": ["/*\nLanguage: Delphi\nWebsite: https://www.embarcadero.com/products/delphi\n*/\n\n/** @type LanguageFn */\nfunction delphi(hljs) {\n  const KEYWORDS =\n    'exports register file shl array record property for mod while set ally label uses raise not ' +\n    'stored class safecall var interface or private static exit index inherited to else stdcall ' +\n    'override shr asm far resourcestring finalization packed virtual out and protected library do ' +\n    'xorwrite goto near function end div overload object unit begin string on inline repeat until ' +\n    'destructor write message program with read initialization except default nil if case cdecl in ' +\n    'downto threadvar of try pascal const external constructor type public then implementation ' +\n    'finally published procedure absolute reintroduce operator as is abstract alias assembler ' +\n    'bitpacked break continue cppdecl cvar enumerator experimental platform deprecated ' +\n    'unimplemented dynamic export far16 forward generic helper implements interrupt iochecks ' +\n    'local name nodefault noreturn nostackframe oldfpccall otherwise saveregisters softfloat ' +\n    'specialize strict unaligned varargs ';\n  const COMMENT_MODES = [\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.COMMENT(/\\{/, /\\}/, {\n      relevance: 0\n    }),\n    hljs.COMMENT(/\\(\\*/, /\\*\\)/, {\n      relevance: 10\n    })\n  ];\n  const DIRECTIVE = {\n    className: 'meta',\n    variants: [\n      {\n        begin: /\\{\\$/,\n        end: /\\}/\n      },\n      {\n        begin: /\\(\\*\\$/,\n        end: /\\*\\)/\n      }\n    ]\n  };\n  const STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/,\n    contains: [{\n      begin: /''/\n    }]\n  };\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    // Source: https://www.freepascal.org/docs-html/ref/refse6.html\n    variants: [\n      {\n        // Hexadecimal notation, e.g., $7F.\n        begin: '\\\\$[0-9A-Fa-f]+'\n      },\n      {\n        // Octal notation, e.g., &42.\n        begin: '&[0-7]+'\n      },\n      {\n        // Binary notation, e.g., %1010.\n        begin: '%[01]+'\n      }\n    ]\n  };\n  const CHAR_STRING = {\n    className: 'string',\n    begin: /(#\\d+)+/\n  };\n  const CLASS = {\n    begin: hljs.IDENT_RE + '\\\\s*=\\\\s*class\\\\s*\\\\(',\n    returnBegin: true,\n    contains: [hljs.TITLE_MODE]\n  };\n  const FUNCTION = {\n    className: 'function',\n    beginKeywords: 'function constructor destructor procedure',\n    end: /[:;]/,\n    keywords: 'function constructor|10 destructor|10 procedure|10',\n    contains: [\n      hljs.TITLE_MODE,\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        contains: [\n          STRING,\n          CHAR_STRING,\n          DIRECTIVE\n        ].concat(COMMENT_MODES)\n      },\n      DIRECTIVE\n    ].concat(COMMENT_MODES)\n  };\n  return {\n    name: 'Delphi',\n    aliases: [\n      'dpr',\n      'dfm',\n      'pas',\n      'pascal',\n      'freepascal',\n      'lazarus',\n      'lpr',\n      'lfm'\n    ],\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    illegal: /\"|\\$[G-Zg-z]|\\/\\*|<\\/|\\|/,\n    contains: [\n      STRING,\n      CHAR_STRING,\n      hljs.NUMBER_MODE,\n      NUMBER,\n      CLASS,\n      FUNCTION,\n      DIRECTIVE\n    ].concat(COMMENT_MODES)\n  };\n}\n\nmodule.exports = delphi;\n"], "names": ["module", "exports", "hljs", "KEYWORDS", "COMMENT_MODES", "C_LINE_COMMENT_MODE", "COMMENT", "relevance", "DIRECTIVE", "className", "variants", "begin", "end", "STRING", "contains", "CHAR_STRING", "CLASS", "IDENT_RE", "returnBegin", "TITLE_MODE", "FUNCTION", "beginKeywords", "keywords", "concat", "name", "aliases", "case_insensitive", "illegal", "NUMBER_MODE"], "sourceRoot": ""}
{"version": 3, "file": "76986.dtale_bundle.js", "mappings": "6EA+DAA,EAAOC,QAtDP,SAAmBC,GACjB,MAAO,CACLC,KAAM,cACNC,QAAS,CAAC,SACVC,SAAU,CACRC,QACE,sGACFC,SACE,6HAEFC,QACE,cAEJC,SAAU,CACRP,EAAKQ,kBACLR,EAAKS,YACLT,EAAKU,kBACL,CACEC,UAAW,OACXC,MAAO,iBACPC,QAAS,MAEX,CACEF,UAAW,SACXC,MAAO,UAET,CACED,UAAW,QACXG,cAAe,cACfC,IAAK,KACLF,QAAS,KACTN,SAAU,CAACP,EAAKgB,QAAQhB,EAAKiB,WAAY,CACvCC,OAAQ,CACNC,gBAAgB,EAChBC,YAAY,OAIlB,CACET,UAAW,QACXG,cAAe,YACfC,IAAK,KACLF,QAAS,KACTN,SAAU,CAACP,EAAKgB,QAAQhB,EAAKiB,WAAY,CACvCC,OAAQ,CACNC,gBAAgB,EAChBC,YAAY,QAMxB,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/capnproto.js"], "sourcesContent": ["/*\nLanguage: Cap’n Proto\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Cap’n Proto message definition format\nWebsite: https://capnproto.org/capnp-tool.html\nCategory: protocols\n*/\n\n/** @type LanguageFn */\nfunction capnproto(hljs) {\n  return {\n    name: 'Cap’n Proto',\n    aliases: ['capnp'],\n    keywords: {\n      keyword:\n        'struct enum interface union group import using const annotation extends in of on as with from fixed',\n      built_in:\n        'Void Bool Int8 Int16 Int32 Int64 UInt8 UInt16 UInt32 UInt64 Float32 Float64 ' +\n        'Text Data AnyPointer AnyStruct Capability List',\n      literal:\n        'true false'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      hljs.HASH_COMMENT_MODE,\n      {\n        className: 'meta',\n        begin: /@0x[\\w\\d]{16};/,\n        illegal: /\\n/\n      },\n      {\n        className: 'symbol',\n        begin: /@\\d+\\b/\n      },\n      {\n        className: 'class',\n        beginKeywords: 'struct enum',\n        end: /\\{/,\n        illegal: /\\n/,\n        contains: [hljs.inherit(hljs.TITLE_MODE, {\n          starts: {\n            endsWithParent: true,\n            excludeEnd: true\n          } // hack: eating everything after the first title\n        })]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'interface',\n        end: /\\{/,\n        illegal: /\\n/,\n        contains: [hljs.inherit(hljs.TITLE_MODE, {\n          starts: {\n            endsWithParent: true,\n            excludeEnd: true\n          } // hack: eating everything after the first title\n        })]\n      }\n    ]\n  };\n}\n\nmodule.exports = capnproto;\n"], "names": ["module", "exports", "hljs", "name", "aliases", "keywords", "keyword", "built_in", "literal", "contains", "QUOTE_STRING_MODE", "NUMBER_MODE", "HASH_COMMENT_MODE", "className", "begin", "illegal", "beginKeywords", "end", "inherit", "TITLE_MODE", "starts", "endsWithParent", "excludeEnd"], "sourceRoot": ""}
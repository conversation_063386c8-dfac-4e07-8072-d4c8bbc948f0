{"version": 3, "file": "9612.dtale_bundle.js", "mappings": "4EAoBA,SAASA,KAAUC,GAEjB,OADeA,EAAKC,IAAKC,IAAMC,OAZjBC,EAYwBF,GAVpB,iBAAPE,EAAwBA,EAE5BA,EAAGD,OAHM,KADlB,IAAgBC,IAY4BC,KAAK,GAEjD,CAiJAC,EAAOC,QAtIP,SAAcC,GACZ,MAAMC,EAAM,CAAC,EACPC,EAAa,CACjBC,MAAO,OACPC,IAAI,KACJC,SAAU,CACR,OACA,CACEF,MAAO,KACPE,SAAU,CAAEJ,MAIlBK,OAAOC,OAAON,EAAI,CAChBO,UAAW,WACXC,SAAU,CACR,CAACN,MAAOZ,EAAO,qBAGb,wBACFW,KAIJ,MAAMQ,EAAQ,CACZF,UAAW,QACXL,MAAO,OAAQC,IAAK,KACpBC,SAAU,CAACL,EAAKW,mBAEZC,EAAW,CACfT,MAAO,iBACPU,OAAQ,CACNR,SAAU,CACRL,EAAKc,kBAAkB,CACrBX,MAAO,QACPC,IAAK,QACLI,UAAW,cAKbO,EAAe,CACnBP,UAAW,SACXL,MAAO,IAAKC,IAAK,IACjBC,SAAU,CACRL,EAAKW,iBACLV,EACAS,IAGJA,EAAML,SAASW,KAAKD,GACpB,MASME,EAAa,CACjBd,MAAO,SACPC,IAAK,OACLC,SAAU,CACR,CAAEF,MAAO,gBAAiBK,UAAW,UACrCR,EAAKkB,YACLjB,IAcEkB,EAAgBnB,EAAKoB,QAAQ,CACjCC,OAAQ,IAZa,CACrB,OACA,OACA,MACA,KACA,MACA,MACA,OACA,OACA,QAG2BxB,KAAK,QAChCyB,UAAW,KAEPC,EAAW,CACff,UAAW,WACXL,MAAO,4BACPqB,aAAa,EACbnB,SAAU,CAACL,EAAKyB,QAAQzB,EAAK0B,WAAY,CAACvB,MAAO,gBACjDmB,UAAW,GAGb,MAAO,CACLK,KAAM,OACNC,QAAS,CAAC,KAAM,OAChBC,SAAU,CACRC,SAAU,gBACVC,QACE,+DACFC,QACE,aACFC,SAGE,6uBAeJ5B,SAAU,CACRc,EACAnB,EAAKoB,UACLG,EACAN,EACAjB,EAAKkC,kBACLtB,EACAG,EA3EkB,CACpBP,UAAW,GACXL,MAAO,OAGW,CAClBK,UAAW,SACXL,MAAO,IAAKC,IAAK,KAuEfH,GAGN,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/bash.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Bash\nAuthor: vah <<EMAIL>>\nContributrors: <PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/bash/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction bash(hljs) {\n  const VAR = {};\n  const BRACED_VAR = {\n    begin: /\\$\\{/,\n    end:/\\}/,\n    contains: [\n      \"self\",\n      {\n        begin: /:-/,\n        contains: [ VAR ]\n      } // default values\n    ]\n  };\n  Object.assign(VAR,{\n    className: 'variable',\n    variants: [\n      {begin: concat(/\\$[\\w\\d#@][\\w\\d_]*/,\n        // negative look-ahead tries to avoid matching patterns that are not\n        // Perl at all like $ident$, @ident@, etc.\n        `(?![\\\\w\\\\d])(?![$])`) },\n      BRACED_VAR\n    ]\n  });\n\n  const SUBST = {\n    className: 'subst',\n    begin: /\\$\\(/, end: /\\)/,\n    contains: [hljs.BACKSLASH_ESCAPE]\n  };\n  const HERE_DOC = {\n    begin: /<<-?\\s*(?=\\w+)/,\n    starts: {\n      contains: [\n        hljs.END_SAME_AS_BEGIN({\n          begin: /(\\w+)/,\n          end: /(\\w+)/,\n          className: 'string'\n        })\n      ]\n    }\n  };\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/, end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VAR,\n      SUBST\n    ]\n  };\n  SUBST.contains.push(QUOTE_STRING);\n  const ESCAPED_QUOTE = {\n    className: '',\n    begin: /\\\\\"/\n\n  };\n  const APOS_STRING = {\n    className: 'string',\n    begin: /'/, end: /'/\n  };\n  const ARITHMETIC = {\n    begin: /\\$\\(\\(/,\n    end: /\\)\\)/,\n    contains: [\n      { begin: /\\d+#[0-9a-f]+/, className: \"number\" },\n      hljs.NUMBER_MODE,\n      VAR\n    ]\n  };\n  const SH_LIKE_SHELLS = [\n    \"fish\",\n    \"bash\",\n    \"zsh\",\n    \"sh\",\n    \"csh\",\n    \"ksh\",\n    \"tcsh\",\n    \"dash\",\n    \"scsh\",\n  ];\n  const KNOWN_SHEBANG = hljs.SHEBANG({\n    binary: `(${SH_LIKE_SHELLS.join(\"|\")})`,\n    relevance: 10\n  });\n  const FUNCTION = {\n    className: 'function',\n    begin: /\\w[\\w\\d_]*\\s*\\(\\s*\\)\\s*\\{/,\n    returnBegin: true,\n    contains: [hljs.inherit(hljs.TITLE_MODE, {begin: /\\w[\\w\\d_]*/})],\n    relevance: 0\n  };\n\n  return {\n    name: 'Bash',\n    aliases: ['sh', 'zsh'],\n    keywords: {\n      $pattern: /\\b[a-z._-]+\\b/,\n      keyword:\n        'if then else elif fi for while in do done case esac function',\n      literal:\n        'true false',\n      built_in:\n        // Shell built-ins\n        // http://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n        'break cd continue eval exec exit export getopts hash pwd readonly return shift test times ' +\n        'trap umask unset ' +\n        // Bash built-ins\n        'alias bind builtin caller command declare echo enable help let local logout mapfile printf ' +\n        'read readarray source type typeset ulimit unalias ' +\n        // Shell modifiers\n        'set shopt ' +\n        // Zsh built-ins\n        'autoload bg bindkey bye cap chdir clone comparguments compcall compctl compdescribe compfiles ' +\n        'compgroups compquote comptags comptry compvalues dirs disable disown echotc echoti emulate ' +\n        'fc fg float functions getcap getln history integer jobs kill limit log noglob popd print ' +\n        'pushd pushln rehash sched setcap setopt stat suspend ttyctl unfunction unhash unlimit ' +\n        'unsetopt vared wait whence where which zcompile zformat zftp zle zmodload zparseopts zprof ' +\n        'zpty zregexparse zsocket zstyle ztcp'\n    },\n    contains: [\n      KNOWN_SHEBANG, // to catch known shells and boost relevancy\n      hljs.SHEBANG(), // to catch unknown shells but still highlight the shebang\n      FUNCTION,\n      ARITHMETIC,\n      hljs.HASH_COMMENT_MODE,\n      HERE_DOC,\n      QUOTE_STRING,\n      ESCAPED_QUOTE,\n      APOS_STRING,\n      VAR\n    ]\n  };\n}\n\nmodule.exports = bash;\n"], "names": ["concat", "args", "map", "x", "source", "re", "join", "module", "exports", "hljs", "VAR", "BRACED_VAR", "begin", "end", "contains", "Object", "assign", "className", "variants", "SUBST", "BACKSLASH_ESCAPE", "HERE_DOC", "starts", "END_SAME_AS_BEGIN", "QUOTE_STRING", "push", "ARITHMETIC", "NUMBER_MODE", "KNOWN_SHEBANG", "SHEBANG", "binary", "relevance", "FUNCTION", "returnBegin", "inherit", "TITLE_MODE", "name", "aliases", "keywords", "$pattern", "keyword", "literal", "built_in", "HASH_COMMENT_MODE"], "sourceRoot": ""}
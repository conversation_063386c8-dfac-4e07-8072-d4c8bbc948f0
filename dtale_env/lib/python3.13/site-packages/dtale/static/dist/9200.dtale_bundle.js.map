{"version": 3, "file": "9200.dtale_bundle.js", "mappings": "0HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,SAAU,qBACVC,UAAW,cACXC,MAAO,gBACPC,SAAU,eACVC,SAAU,eACVC,MAAO,KAOLC,EAJiB,SAAwBC,EAAOC,EAAOC,EAAWC,GACpE,OAAOX,EAAqBQ,EAC9B,EAGAV,EAAA,QAAkBS,EAClBK,EAAOd,QAAUA,EAAQe,O,iBCnBzBjB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIgB,EAASC,EAAuB,EAAQ,QAExCC,EAAUD,EAAuB,EAAQ,QAEzCE,EAAUF,EAAuB,EAAQ,OAEzCG,EAAUH,EAAuB,EAAQ,QAEzCI,EAAUJ,EAAuB,EAAQ,QAE7C,SAASA,EAAuBK,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEP,QAASO,EAAO,CAU9F,IAcIb,EAdS,CACXe,KAAM,QACNC,eAAgBT,EAAOD,QACvBW,WAAYR,EAAQH,QACpBY,eAAgBR,EAAQJ,QACxBa,SAAUR,EAAQL,QAClBc,MAAOR,EAAQN,QACfe,QAAS,CACPC,aAAc,EAGdC,sBAAuB,IAI3BhC,EAAA,QAAkBS,EAClBK,EAAOd,QAAUA,EAAQe,O,kBCzCzBjB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIgB,EAASC,EAAuB,EAAQ,QAI5C,SAASA,EAAuBK,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEP,QAASO,EAAO,CAE9F,IAgGIb,EA1CQ,CACVwB,eAAe,EA3DHhB,EAAuB,EAAQ,MA2DhBF,SAAS,CAClCmB,aAxD4B,wBAyD5BC,aAxD4B,OAyD5BC,cAAe,SAAuBnC,GACpC,OAAOoC,SAASpC,EAAO,GACzB,IAEFqC,KAAK,EAAItB,EAAOD,SAAS,CACvBwB,cA7DmB,CACrBC,OAAQ,UACRC,YAAa,qDACbC,KAAM,uDA2DJC,kBAAmB,OACnBC,cA1DmB,CACrBC,IAAK,CAAC,QAAS,UA0DbC,kBAAmB,QAErBC,SAAS,EAAI/B,EAAOD,SAAS,CAC3BwB,cA3DuB,CACzBC,OAAQ,WACRC,YAAa,YACbC,KAAM,kBAyDJC,kBAAmB,OACnBC,cAxDuB,CACzBC,IAAK,CAAC,KAAM,KAAM,KAAM,OAwDtBC,kBAAmB,MACnBV,cAAe,SAAuBY,GACpC,OAAOA,EAAQ,CACjB,IAEFC,OAAO,EAAIjC,EAAOD,SAAS,CACzBwB,cA5DqB,CACvBC,OAAQ,eACRC,YAAa,0CACbC,KAAM,2CA0DJC,kBAAmB,OACnBC,cAzDqB,CACvBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACtFK,IAAK,CAAC,OAAQ,MAAO,QAAS,OAAQ,QAAS,QAAS,QAAS,OAAQ,MAAO,OAAQ,MAAO,QAwD7FC,kBAAmB,QAErBI,KAAK,EAAIlC,EAAOD,SAAS,CACvBwB,cAzDmB,CACrBC,OAAQ,cACRW,MAAO,4CACPV,YAAa,mCACbC,KAAM,2DAsDJC,kBAAmB,OACnBC,cArDmB,CACrBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACnDE,KAAM,CAAC,UAAW,YAAa,aAAc,aAAc,WAAY,WAAY,WACnFG,IAAK,CAAC,OAAQ,OAAQ,MAAO,OAAQ,MAAO,MAAO,QAmDjDC,kBAAmB,QAErBM,WAAW,EAAIpC,EAAOD,SAAS,CAC7BwB,cApDyB,CAC3BC,OAAQ,6DACRK,IAAK,kFAmDHF,kBAAmB,MACnBC,cAlDyB,CAC3BC,IAAK,CACHQ,GAAI,MACJC,GAAI,MACJC,SAAU,OACVC,KAAM,OACNC,QAAS,WACTC,UAAW,aACXC,QAAS,WACTC,MAAO,WA0CPd,kBAAmB,SAIvB9C,EAAA,QAAkBS,EAClBK,EAAOd,QAAUA,EAAQe,O,kBC7GzBjB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCsB,EAF5BN,GAE4BM,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEP,QAASO,GAEvF,IAgCIb,EAda,CACfoD,MAAM,EAAI7C,EAAOD,SAAS,CACxB+C,QApBc,CAChBC,KAAM,mBACNC,KAAM,aACNC,OAAQ,WACRd,MAAO,cAiBLe,aAAc,SAEhBC,MAAM,EAAInD,EAAOD,SAAS,CACxB+C,QAlBc,CAChBC,KAAM,iBACNC,KAAM,cACNC,OAAQ,YACRd,MAAO,UAeLe,aAAc,SAEhBE,UAAU,EAAIpD,EAAOD,SAAS,CAC5B+C,QAhBkB,CACpBC,KAAM,0BACNC,KAAM,0BACNC,OAAQ,qBACRd,MAAO,sBAaLe,aAAc,UAIlBlE,EAAA,QAAkBS,EAClBK,EAAOd,QAAUA,EAAQe,O,kBC3CzBjB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCsB,EAF5BN,GAE4BM,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEP,QAASO,GAEvF,IAsHIb,EA5BW,CACbwB,cALkB,SAAuBoC,GACzC,OAAOC,OAAOD,EAChB,EAIE/B,KAAK,EAAItB,EAAOD,SAAS,CACvBwD,OA7FY,CACd/B,OAAQ,CAAC,IAAK,KACdC,YAAa,CAAC,OAAQ,QACtBC,KAAM,CAAC,cAAe,gBA2FpBwB,aAAc,SAEhBnB,SAAS,EAAI/B,EAAOD,SAAS,CAC3BwD,OA5FgB,CAClB/B,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,KAAM,KAAM,KAAM,MAChCC,KAAM,CAAC,cAAe,eAAgB,eAAgB,iBA0FpDwB,aAAc,OACdM,iBAAkB,SAA0BzB,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAIjC,EAAOD,SAAS,CACzBwD,OA9Fc,CAChB/B,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,MAAO,MAAO,OAAQ,QAAS,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QACrGC,KAAM,CAAC,QAAS,SAAU,OAAQ,QAAS,OAAQ,QAAS,QAAS,QAAS,SAAU,SAAU,SAAU,WA4F1GwB,aAAc,SAEhBhB,KAAK,EAAIlC,EAAOD,SAAS,CACvBwD,OA7FY,CACd/B,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCW,MAAO,CAAC,MAAO,QAAS,SAAU,SAAU,OAAQ,OAAQ,OAC5DV,YAAa,CAAC,MAAO,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,OAC5DC,KAAM,CAAC,QAAS,UAAW,WAAY,WAAY,SAAU,SAAU,UA0FrEwB,aAAc,SAEhBd,WAAW,EAAIpC,EAAOD,SAAS,CAC7BwD,OA3FkB,CACpB/B,OAAQ,CACNa,GAAI,IACJC,GAAI,IACJC,SAAU,IACVC,KAAM,IACNC,QAAS,SACTC,UAAW,YACXC,QAAS,SACTC,MAAO,SAETnB,YAAa,CACXY,GAAI,IACJC,GAAI,IACJC,SAAU,YACVC,KAAM,MACNC,QAAS,SACTC,UAAW,YACXC,QAAS,SACTC,MAAO,SAETlB,KAAM,CACJW,GAAI,IACJC,GAAI,IACJC,SAAU,YACVC,KAAM,MACNC,QAAS,SACTC,UAAW,YACXC,QAAS,SACTC,MAAO,UA+DPM,aAAc,OACdO,iBA7D4B,CAC9BjC,OAAQ,CACNa,GAAI,IACJC,GAAI,IACJC,SAAU,IACVC,KAAM,IACNC,QAAS,YACTC,UAAW,aACXC,QAAS,YACTC,MAAO,YAETnB,YAAa,CACXY,GAAI,IACJC,GAAI,IACJC,SAAU,YACVC,KAAM,MACNC,QAAS,YACTC,UAAW,YACXC,QAAS,YACTC,MAAO,YAETlB,KAAM,CACJW,GAAI,IACJC,GAAI,IACJC,SAAU,YACVC,KAAM,MACNC,QAAS,SACTC,UAAW,aACXC,QAAS,YACTC,MAAO,aAiCPc,uBAAwB,UAI5B1E,EAAA,QAAkBS,EAClBK,EAAOd,QAAUA,EAAQe,O,gBCjIzBjB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAI2E,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,qBACLC,IAAK,gBACLC,WAAY,yBACZvE,MAAO,0BAETwE,SAAU,CACRH,IAAK,cACLC,IAAK,SACLC,WAAY,kBACZvE,MAAO,mBAETyE,YAAa,YACbC,iBAAkB,CAChBL,IAAK,eACLC,IAAK,iBACLC,WAAY,yBACZvE,MAAO,0BAET2E,SAAU,CACRN,IAAK,cACLC,IAAK,UACLC,WAAY,kBACZvE,MAAO,mBAET4E,YAAa,CACXP,IAAK,qBACLC,IAAK,iBACLC,WAAY,0BACZvE,MAAO,0BAET6E,OAAQ,CACNR,IAAK,aACLC,IAAK,SACLC,WAAY,kBACZvE,MAAO,kBAET8E,MAAO,CACLT,IAAK,WACLC,IAAK,QACLC,WAAY,iBACZvE,MAAO,iBAET+E,YAAa,CACXV,IAAK,qBACLC,IAAK,kBACLC,WAAY,2BACZvE,MAAO,2BAETgF,OAAQ,CACNX,IAAK,aACLC,IAAK,UACLC,WAAY,mBACZvE,MAAO,mBAETiF,aAAc,CACZZ,IAAK,mBACLC,IAAK,gBACLC,WAAY,yBACZvE,MAAO,yBAETkF,QAAS,CACPb,IAAK,WACLC,IAAK,QACLC,WAAY,iBACZvE,MAAO,iBAETmF,YAAa,CACXd,IAAK,mBACLC,IAAK,gBACLC,WAAY,0BACZvE,MAAO,yBAEToF,OAAQ,CACNf,IAAK,WACLC,IAAK,QACLC,WAAY,kBACZvE,MAAO,iBAETqF,WAAY,CACVhB,IAAK,cACLC,IAAK,gBACLC,WAAY,0BACZvE,MAAO,yBAETsF,aAAc,CACZjB,IAAK,mBACLC,IAAK,gBACLC,WAAY,0BACZvE,MAAO,0BA+BPC,EA3BiB,SAAwBC,EAAOqF,EAAOjE,GACzD,IAAIkE,EACAC,EAAatB,EAAqBjE,GActC,OAXEsF,EADwB,iBAAfC,EACAA,EACU,IAAVF,EACAE,EAAWpB,IACD,IAAVkB,EACAE,EAAWnB,IACXiB,GAAS,GACTE,EAAWlB,WAAWmB,QAAQ,YAAa5B,OAAOyB,IAElDE,EAAWzF,MAAM0F,QAAQ,YAAa5B,OAAOyB,IAGpDjE,SAA0CA,EAAQqE,UAChDrE,EAAQsE,YAActE,EAAQsE,WAAa,EACtC,WAAaJ,EAEb,OAASA,EAIbA,CACT,EAGAhG,EAAA,QAAkBS,EAClBK,EAAOd,QAAUA,EAAQe,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/ar-SA/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/locale/ar-SA/index.js", "webpack://dtale/./node_modules/date-fns/locale/ar-SA/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/ar-SA/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/ar-SA/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/ar-SA/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"'أخر' eeee 'عند' p\",\n  yesterday: \"'أمس عند' p\",\n  today: \"'اليوم عند' p\",\n  tomorrow: \"'غداً عند' p\",\n  nextWeek: \"eeee 'عند' p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Arabic locale (Sauid Arabic).\n * @language Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@dalwadani]{@link https://github.com/dalwadani}\n */\nvar locale = {\n  code: 'ar-SA',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 0\n    /* Sunday */\n    ,\n    firstWeekContainsDate: 1\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)(th|st|nd|rd)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(ق|ب)/i,\n  abbreviated: /^(ق\\.?\\s?م\\.?|ق\\.?\\s?م\\.?\\s?|a\\.?\\s?d\\.?|c\\.?\\s?)/i,\n  wide: /^(قبل الميلاد|قبل الميلاد|بعد الميلاد|بعد الميلاد)/i\n};\nvar parseEraPatterns = {\n  any: [/^قبل/i, /^بعد/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^ر[1234]/i,\n  wide: /^الربع [1234]/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[يفمأمسند]/i,\n  abbreviated: /^(ين|ف|مار|أب|ماي|يون|يول|أغ|س|أك|ن|د)/i,\n  wide: /^(ين|ف|مار|أب|ماي|يون|يول|أغ|س|أك|ن|د)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^ي/i, /^ف/i, /^م/i, /^أ/i, /^م/i, /^ي/i, /^ي/i, /^أ/i, /^س/i, /^أ/i, /^ن/i, /^د/i],\n  any: [/^ين/i, /^ف/i, /^مار/i, /^أب/i, /^ماي/i, /^يون/i, /^يول/i, /^أغ/i, /^س/i, /^أك/i, /^ن/i, /^د/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[حنثرخجس]/i,\n  short: /^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,\n  abbreviated: /^(أحد|اثن|ثلا|أرب|خمي|جمعة|سبت)/i,\n  wide: /^(الأحد|الاثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^ح/i, /^ن/i, /^ث/i, /^ر/i, /^خ/i, /^ج/i, /^س/i],\n  wide: [/^الأحد/i, /^الاثنين/i, /^الثلاثاء/i, /^الأربعاء/i, /^الخميس/i, /^الجمعة/i, /^السبت/i],\n  any: [/^أح/i, /^اث/i, /^ث/i, /^أر/i, /^خ/i, /^ج/i, /^س/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,\n  any: /^([ap]\\.?\\s?m\\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mi/i,\n    noon: /^no/i,\n    morning: /morning/i,\n    afternoon: /afternoon/i,\n    evening: /evening/i,\n    night: /night/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'EEEE, MMMM do, y',\n  long: 'MMMM do, y',\n  medium: 'MMM d, y',\n  short: 'MM/dd/yyyy'\n};\nvar timeFormats = {\n  full: 'h:mm:ss a zzzz',\n  long: 'h:mm:ss a z',\n  medium: 'h:mm:ss a',\n  short: 'h:mm a'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'عند' {{time}}\",\n  long: \"{{date}} 'عند' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['ق', 'ب'],\n  abbreviated: ['ق.م.', 'ب.م.'],\n  wide: ['قبل الميلاد', 'بعد الميلاد']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['ر1', 'ر2', 'ر3', 'ر4'],\n  wide: ['الربع الأول', 'الربع الثاني', 'الربع الثالث', 'الربع الرابع']\n};\nvar monthValues = {\n  narrow: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'],\n  abbreviated: ['ينا', 'فبر', 'مارس', 'أبريل', 'مايو', 'يونـ', 'يولـ', 'أغسـ', 'سبتـ', 'أكتـ', 'نوفـ', 'ديسـ'],\n  wide: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']\n};\nvar dayValues = {\n  narrow: ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],\n  short: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],\n  abbreviated: ['أحد', 'اثنـ', 'ثلا', 'أربـ', 'خميـ', 'جمعة', 'سبت'],\n  wide: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'ن',\n    noon: 'ظ',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءاً',\n    night: 'ليلاً'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهر',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءاً',\n    night: 'ليلاً'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهر',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءاً',\n    night: 'ليلاً'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'ن',\n    noon: 'ظ',\n    morning: 'في الصباح',\n    afternoon: 'بعد الظـهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهر',\n    morning: 'في الصباح',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهر',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظـهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber) {\n  return String(dirtyNumber);\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'أقل من ثانية واحدة',\n    two: 'أقل من ثانتين',\n    threeToTen: 'أقل من {{count}} ثواني',\n    other: 'أقل من {{count}} ثانية'\n  },\n  xSeconds: {\n    one: 'ثانية واحدة',\n    two: 'ثانتين',\n    threeToTen: '{{count}} ثواني',\n    other: '{{count}} ثانية'\n  },\n  halfAMinute: 'نصف دقيقة',\n  lessThanXMinutes: {\n    one: 'أقل من دقيقة',\n    two: 'أقل من دقيقتين',\n    threeToTen: 'أقل من {{count}} دقائق',\n    other: 'أقل من {{count}} دقيقة'\n  },\n  xMinutes: {\n    one: 'دقيقة واحدة',\n    two: 'دقيقتين',\n    threeToTen: '{{count}} دقائق',\n    other: '{{count}} دقيقة'\n  },\n  aboutXHours: {\n    one: 'ساعة واحدة تقريباً',\n    two: 'ساعتين تقريباً',\n    threeToTen: '{{count}} ساعات تقريباً',\n    other: '{{count}} ساعة تقريباً'\n  },\n  xHours: {\n    one: 'ساعة واحدة',\n    two: 'ساعتين',\n    threeToTen: '{{count}} ساعات',\n    other: '{{count}} ساعة'\n  },\n  xDays: {\n    one: 'يوم واحد',\n    two: 'يومين',\n    threeToTen: '{{count}} أيام',\n    other: '{{count}} يوم'\n  },\n  aboutXWeeks: {\n    one: 'أسبوع واحد تقريباً',\n    two: 'أسبوعين تقريباً',\n    threeToTen: '{{count}} أسابيع تقريباً',\n    other: '{{count}} أسبوع تقريباً'\n  },\n  xWeeks: {\n    one: 'أسبوع واحد',\n    two: 'أسبوعين',\n    threeToTen: '{{count}} أسابيع',\n    other: '{{count}} أسبوع'\n  },\n  aboutXMonths: {\n    one: 'شهر واحد تقريباً',\n    two: 'شهرين تقريباً',\n    threeToTen: '{{count}} أشهر تقريباً',\n    other: '{{count}} شهر تقريباً'\n  },\n  xMonths: {\n    one: 'شهر واحد',\n    two: 'شهرين',\n    threeToTen: '{{count}} أشهر',\n    other: '{{count}} شهر'\n  },\n  aboutXYears: {\n    one: 'عام واحد تقريباً',\n    two: 'عامين تقريباً',\n    threeToTen: '{{count}} أعوام تقريباً',\n    other: '{{count}} عام تقريباً'\n  },\n  xYears: {\n    one: 'عام واحد',\n    two: 'عامين',\n    threeToTen: '{{count}} أعوام',\n    other: '{{count}} عام'\n  },\n  overXYears: {\n    one: 'أكثر من عام',\n    two: 'أكثر من عامين',\n    threeToTen: 'أكثر من {{count}} أعوام',\n    other: 'أكثر من {{count}} عام'\n  },\n  almostXYears: {\n    one: 'عام واحد تقريباً',\n    two: 'عامين تقريباً',\n    threeToTen: '{{count}} أعوام تقريباً',\n    other: '{{count}} عام تقريباً'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2) {\n    result = tokenValue.two;\n  } else if (count <= 10) {\n    result = tokenValue.threeToTen.replace('{{count}}', String(count));\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'في خلال ' + result;\n    } else {\n      return 'منذ ' + result;\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "_default", "token", "_date", "_baseDate", "_options", "module", "default", "_index", "_interopRequireDefault", "_index2", "_index3", "_index4", "_index5", "obj", "__esModule", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "options", "weekStartsOn", "firstWeekContainsDate", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "parseInt", "era", "matchPatterns", "narrow", "abbreviated", "wide", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "quarter", "index", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "date", "formats", "full", "long", "medium", "defaultWidth", "time", "dateTime", "dirtyNumber", "String", "values", "argument<PERSON>allback", "formattingValues", "defaultFormattingWidth", "formatDistanceLocale", "lessThanXSeconds", "one", "two", "threeToTen", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "count", "result", "tokenValue", "replace", "addSuffix", "comparison"], "sourceRoot": ""}
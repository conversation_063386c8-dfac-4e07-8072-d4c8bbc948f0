{"version": 3, "file": "94006.dtale_bundle.js", "mappings": "8FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,SAAU,SAAkBC,GAG1B,OAFUA,EAAKC,aAGb,KAAK,EACH,MAAO,0BAET,KAAK,EACH,MAAO,wBAET,KAAK,EACH,MAAO,yBAET,QACE,MAAO,yBAEb,EACAC,UAAW,gBACXC,MAAO,eACPC,SAAU,eACVC,SAAU,SAAkBL,GAG1B,OAFUA,EAAKC,aAGb,KAAK,EACH,MAAO,2BAET,KAAK,EACH,MAAO,yBAET,KAAK,EACH,MAAO,0BAET,QACE,MAAO,0BAEb,EACAK,MAAO,KAaLC,EAViB,SAAwBC,EAAOR,EAAMS,EAAWC,GACnE,IAAIC,EAASb,EAAqBU,GAElC,MAAsB,mBAAXG,EACFA,EAAOX,GAGTW,CACT,EAGAf,EAAA,QAAkBW,EAClBK,EAAOhB,QAAUA,EAAQiB,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/sl/_lib/formatRelative/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var day = date.getUTCDay();\n\n    switch (day) {\n      case 0:\n        return \"'prej<PERSON><PERSON><PERSON> nedeljo ob' p\";\n\n      case 3:\n        return \"'prejšnjo sredo ob' p\";\n\n      case 6:\n        return \"'prejšnjo soboto ob' p\";\n\n      default:\n        return \"'prejšnji' EEEE 'ob' p\";\n    }\n  },\n  yesterday: \"'včeraj ob' p\",\n  today: \"'danes ob' p\",\n  tomorrow: \"'jutri ob' p\",\n  nextWeek: function nextWeek(date) {\n    var day = date.getUTCDay();\n\n    switch (day) {\n      case 0:\n        return \"'naslednjo nedeljo ob' p\";\n\n      case 3:\n        return \"'naslednjo sredo ob' p\";\n\n      case 6:\n        return \"'naslednjo soboto ob' p\";\n\n      default:\n        return \"'naslednji' EEEE 'ob' p\";\n    }\n  },\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n\n  if (typeof format === 'function') {\n    return format(date);\n  }\n\n  return format;\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatRelativeLocale", "lastWeek", "date", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "_default", "token", "_baseDate", "_options", "format", "module", "default"], "sourceRoot": ""}
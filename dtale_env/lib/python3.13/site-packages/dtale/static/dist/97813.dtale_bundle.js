"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[97813,99458],{12728:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d=s(a(91287)),l=s(a(26376)),u=s(a(12568)),f=s(a(85024)),o=s(a(83447));function s(e){return e&&e.__esModule?e:{default:e}}var m={code:"en-GB",formatDistance:d.default,formatLong:o.default,formatRelative:l.default,localize:u.default,match:f.default,options:{weekStartsOn:1,firstWeekContainsDate:4}};t.default=m,e.exports=t.default},83447:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d,l=(d=a(19059))&&d.__esModule?d:{default:d};var u={date:(0,l.default)({formats:{full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"dd/MM/yyyy"},defaultWidth:"full"}),time:(0,l.default)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,l.default)({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})};t.default=u,e.exports=t.default}}]);
//# sourceMappingURL=97813.dtale_bundle.js.map
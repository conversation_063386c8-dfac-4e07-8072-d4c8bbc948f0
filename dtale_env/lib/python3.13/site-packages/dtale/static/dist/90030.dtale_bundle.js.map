{"version": 3, "file": "90030.dtale_bundle.js", "mappings": "2HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,iBAAkB,CAChBC,WAAY,CACVC,IAAK,wBACLC,MAAO,iCAETC,gBAAiB,CACfF,IAAK,0BACLC,MAAO,kCAGXE,SAAU,CACRJ,WAAY,CACVC,IAAK,aACLC,MAAO,sBAETC,gBAAiB,CACfF,IAAK,eACLC,MAAO,uBAGXG,YAAa,CACXL,WAAY,oBACZG,gBAAiB,wBAEnBG,iBAAkB,CAChBN,WAAY,CACVC,IAAK,wBACLC,MAAO,iCAETC,gBAAiB,CACfF,IAAK,0BACLC,MAAO,kCAGXK,SAAU,CACRP,WAAY,CACVC,IAAK,aACLC,MAAO,sBAETC,gBAAiB,CACfF,IAAK,eACLC,MAAO,uBAGXM,YAAa,CACXR,WAAY,CACVC,IAAK,sBACLC,MAAO,+BAETC,gBAAiB,CACfF,IAAK,wBACLC,MAAO,gCAGXO,OAAQ,CACNT,WAAY,CACVC,IAAK,YACLC,MAAO,qBAETC,gBAAiB,CACfF,IAAK,cACLC,MAAO,sBAGXQ,MAAO,CACLV,WAAY,CACVC,IAAK,UACLC,MAAO,kBAETC,gBAAiB,CACfF,IAAK,YACLC,MAAO,mBAGXS,YAAa,CACXX,WAAY,CACVC,IAAK,qBACLC,MAAO,8BAETC,gBAAiB,CACfF,IAAK,wBACLC,MAAO,+BAGXU,OAAQ,CACNZ,WAAY,CACVC,IAAK,WACLC,MAAO,oBAETC,gBAAiB,CACfF,IAAK,aACLC,MAAO,qBAGXW,aAAc,CACZb,WAAY,CACVC,IAAK,qBACLC,MAAO,6BAETC,gBAAiB,CACfF,IAAK,wBACLC,MAAO,8BAGXY,QAAS,CACPd,WAAY,CACVC,IAAK,WACLC,MAAO,mBAETC,gBAAiB,CACfF,IAAK,cACLC,MAAO,oBAGXa,YAAa,CACXf,WAAY,CACVC,IAAK,oBACLC,MAAO,4BAETC,gBAAiB,CACfF,IAAK,uBACLC,MAAO,6BAGXc,OAAQ,CACNhB,WAAY,CACVC,IAAK,UACLC,MAAO,kBAETC,gBAAiB,CACfF,IAAK,aACLC,MAAO,mBAGXe,WAAY,CACVjB,WAAY,CACVC,IAAK,kBACLC,MAAO,0BAETC,gBAAiB,CACfF,IAAK,qBACLC,MAAO,2BAGXgB,aAAc,CACZlB,WAAY,CACVC,IAAK,cACLC,MAAO,sBAETC,gBAAiB,CACfF,IAAK,iBACLC,MAAO,wBAITiB,EAAuB,CAAC,IAAK,IAAK,IAAK,IAAK,KAC5CC,EAAS,CAAC,KAAM,IAAK,IAAK,IAAK,KAC/BC,EAAyB,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,GACzCC,EAAsC,CAAC,GAAI,GAAI,GAAI,IAEvD,SAASC,EAAeC,GACtB,IAAIC,EAAcD,EAAUE,OAAO,GAAGC,cAEtC,IAAoC,GAAhCP,EAAOQ,QAAQH,KAAoE,GAA9CN,EAAqBS,QAAQH,GACpE,OAAO,EAKT,IAAII,EAAYL,EAAUM,MAAM,KAAK,GACjCC,EAASC,SAASH,GAEtB,OAAKI,MAAMF,KAA2D,GAAhDV,EAAuBO,QAAQG,EAAS,MAAkG,GAArFT,EAAoCM,QAAQI,SAASH,EAAUK,UAAU,EAAG,IAMzJ,CAEA,IAwBIC,EAxBiB,SAAwBC,EAAOC,EAAOC,GACzD,IAAIC,EACAC,EAAa1C,EAAqBsC,GAClCK,EAAaH,SAA0CA,EAAQI,UAAYF,EAAWrC,gBAAkBqC,EAAWxC,WAUvH,OAPEuC,EADwB,iBAAfE,EACAA,EACU,IAAVJ,EACAI,EAAWxC,IAEXwC,EAAWvC,MAAMyC,QAAQ,YAAaC,OAAOP,IAGpDC,SAA0CA,EAAQI,UAChDJ,EAAQO,YAAcP,EAAQO,WAAa,EACtC,KAAOtB,EAAegB,GAAU,IAAM,IAAM,IAAMA,EAElD,QAAUhB,EAAegB,GAAU,IAAM,IAAM,IAAMA,EAIzDA,CACT,EAGA3C,EAAA,QAAkBuC,EAClBW,EAAOlD,QAAUA,EAAQmD,O,gBCnNzBrD,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIoD,EAAuB,CACzBC,SAAU,SAAkBC,GAC1B,IAAIC,EAAMD,EAAKE,YACXb,EAAS,WAQb,OANY,IAARY,GAAqB,IAARA,IAEfZ,GAAU,KAGZA,GAAU,eAEZ,EACAc,UAAW,kBACXC,MAAO,cACPC,SAAU,eACVC,SAAU,cACVtD,MAAO,KAaLiC,EAViB,SAAwBC,EAAOc,EAAMO,EAAWC,GACnE,IAAIC,EAASX,EAAqBZ,GAElC,MAAsB,mBAAXuB,EACFA,EAAOT,GAGTS,CACT,EAGA/D,EAAA,QAAkBuC,EAClBW,EAAOlD,QAAUA,EAAQmD,O,kBCpCzBrD,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIgE,EAASC,EAAuB,EAAQ,QAI5C,SAASA,EAAuBC,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEf,QAASe,EAAO,CAE9F,IAiGI3B,EA1CQ,CACV6B,eAAe,EA5DHH,EAAuB,EAAQ,MA4DhBd,SAAS,CAClCkB,aAzD4B,eA0D5BC,aAzD4B,OA0D5BC,cAAe,SAAuBtE,GACpC,OAAOmC,SAASnC,EAAO,GACzB,IAEFuE,KAAK,EAAIR,EAAOb,SAAS,CACvBsB,cA9DmB,CACrBC,OAAQ,gCACRC,YAAa,gCACbC,KAAM,6EA4DJC,kBAAmB,OACnBC,cA3DmB,CACrBC,IAAK,CAAC,MAAO,QA2DXC,kBAAmB,QAErBC,SAAS,EAAIjB,EAAOb,SAAS,CAC3BsB,cA5DuB,CACzBC,OAAQ,WACRC,YAAa,YACbC,KAAM,yBA0DJC,kBAAmB,OACnBC,cAzDuB,CACzBC,IAAK,CAAC,KAAM,KAAM,KAAM,OAyDtBC,kBAAmB,MACnBT,cAAe,SAAuBW,GACpC,OAAOA,EAAQ,CACjB,IAEFC,OAAO,EAAInB,EAAOb,SAAS,CACzBsB,cA7DqB,CACvBC,OAAQ,eACRC,YAAa,sDACbC,KAAM,4FA2DJC,kBAAmB,OACnBC,cA1DqB,CACvBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACtFK,IAAK,CAAC,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,QAAS,QAAS,OAAQ,MAAO,MAAO,MAAO,QAyD1FC,kBAAmB,QAErBzB,KAAK,EAAIS,EAAOb,SAAS,CACvBsB,cA1DmB,CACrBC,OAAQ,WACRU,MAAO,2BACPT,YAAa,4CACbC,KAAM,wEAuDJC,kBAAmB,OACnBC,cAtDmB,CACrBC,IAAK,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,SAsDnDC,kBAAmB,QAErBK,WAAW,EAAIrB,EAAOb,SAAS,CAC7BsB,cAvDyB,CAC3BC,OAAQ,kEACRC,YAAa,oEACbC,KAAM,oEAqDJC,kBAAmB,OACnBC,cApDyB,CAC3BC,IAAK,CACHO,GAAI,MACJC,GAAI,MACJC,SAAU,WACVC,KAAM,WACNC,QAAS,SACTC,UAAW,YAEXC,QAAS,QACTC,MAAO,WA2CPb,kBAAmB,SAIvBhF,EAAA,QAAkBuC,EAClBW,EAAOlD,QAAUA,EAAQmD,O,kBC9GzBrD,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCkE,EAF5BF,GAE4BE,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEf,QAASe,GAGvF,IAoCI3B,EAda,CACfe,MAAM,EAAIU,EAAOb,SAAS,CACxB2C,QAxBc,CAChBC,KAAM,kBAENC,KAAM,YAENC,OAAQ,WAERb,MAAO,YAkBLc,aAAc,SAEhBC,MAAM,EAAInC,EAAOb,SAAS,CACxB2C,QAlBc,CAChBC,KAAM,gBACNC,KAAM,aACNC,OAAQ,WACRb,MAAO,SAeLc,aAAc,SAEhBE,UAAU,EAAIpC,EAAOb,SAAS,CAC5B2C,QAhBkB,CACpBC,KAAM,yBACNC,KAAM,yBACNC,OAAQ,oBACRb,MAAO,qBAaLc,aAAc,UAIlBlG,EAAA,QAAkBuC,EAClBW,EAAOlD,QAAUA,EAAQmD,O,kBChDzBrD,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCkE,EAF5BF,GAE4BE,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEf,QAASe,GAEvF,IAuHI3B,EA5BW,CACb6B,cANkB,SAAuBiC,EAAavC,GAEtD,OADawC,OAAOD,GACJ,GAClB,EAIE7B,KAAK,EAAIR,EAAOb,SAAS,CACvBoD,OA9FY,CACd7B,OAAQ,CAAC,SAAU,UACnBC,YAAa,CAAC,SAAU,UACxBC,KAAM,CAAC,gBAAiB,gBA4FtBsB,aAAc,SAEhBjB,SAAS,EAAIjB,EAAOb,SAAS,CAC3BoD,OA7FgB,CAClB7B,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,KAAM,KAAM,KAAM,MAChCC,KAAM,CAAC,aAAc,aAAc,aAAc,eA2F/CsB,aAAc,OACdM,iBAAkB,SAA0BvB,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAInB,EAAOb,SAAS,CACzBoD,OA/Fc,CAChB7B,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAC3FC,KAAM,CAAC,SAAU,UAAW,QAAS,SAAU,MAAO,OAAQ,OAAQ,SAAU,YAAa,UAAW,WAAY,aA6FlHsB,aAAc,SAEhB3C,KAAK,EAAIS,EAAOb,SAAS,CACvBoD,OA9FY,CACd7B,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCU,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CT,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACxDC,KAAM,CAAC,UAAW,UAAW,YAAa,WAAY,cAAe,UAAW,cA2F9EsB,aAAc,SAEhBb,WAAW,EAAIrB,EAAOb,SAAS,CAC7BoD,OA5FkB,CACpB7B,OAAQ,CACNY,GAAI,MACJC,GAAI,QACJC,SAAU,eACVC,KAAM,SACNC,QAAS,QACTC,UAAW,WACXC,QAAS,QACTC,MAAO,UAETlB,YAAa,CACXW,GAAI,QACJC,GAAI,WACJC,SAAU,eACVC,KAAM,SACNC,QAAS,QACTC,UAAW,WACXC,QAAS,QACTC,MAAO,UAETjB,KAAM,CACJU,GAAI,QACJC,GAAI,WACJC,SAAU,eACVC,KAAM,SACNC,QAAS,QACTC,UAAW,WACXC,QAAS,QACTC,MAAO,WAgEPK,aAAc,OACdO,iBA9D4B,CAC9B/B,OAAQ,CACNY,GAAI,MACJC,GAAI,OACJC,SAAU,eACVC,KAAM,SACNC,QAAS,QACTC,UAAW,WACXC,QAAS,OACTC,MAAO,SAETlB,YAAa,CACXW,GAAI,QACJC,GAAI,WACJC,SAAU,eACVC,KAAM,SACNC,QAAS,QACTC,UAAW,WACXC,QAAS,OACTC,MAAO,SAETjB,KAAM,CACJU,GAAI,QACJC,GAAI,WACJC,SAAU,eACVC,KAAM,SACNC,QAAS,QACTC,UAAW,WACXC,QAAS,OACTC,MAAO,UAkCPa,uBAAwB,UAI5B1G,EAAA,QAAkBuC,EAClBW,EAAOlD,QAAUA,EAAQmD,O,kBClIzBrD,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIgE,EAASC,EAAuB,EAAQ,OAExC0C,EAAU1C,EAAuB,EAAQ,QAEzC2C,EAAU3C,EAAuB,EAAQ,QAEzC4C,EAAU5C,EAAuB,EAAQ,QAEzC6C,EAAU7C,EAAuB,EAAQ,QAE7C,SAASA,EAAuBC,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEf,QAASe,EAAO,CAU9F,IAcI3B,EAdS,CACXwE,KAAM,KACNC,eAAgBhD,EAAOb,QACvB8D,WAAYN,EAAQxD,QACpB+D,eAAgBN,EAAQzD,QACxBgE,SAAUN,EAAQ1D,QAClBiE,MAAON,EAAQ3D,QACfT,QAAS,CACP2E,aAAc,EAGdC,sBAAuB,IAI3BtH,EAAA,QAAkBuC,EAClBW,EAAOlD,QAAUA,EAAQmD,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/lb/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/lb/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/locale/lb/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/lb/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/lb/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/lb/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: 'manner wéi eng Sekonn',\n      other: 'manner wéi {{count}} Sekonnen'\n    },\n    withPreposition: {\n      one: 'manner wéi enger Sekonn',\n      other: 'manner wéi {{count}} Sekonnen'\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: 'eng Sekonn',\n      other: '{{count}} Sekonnen'\n    },\n    withPreposition: {\n      one: 'enger Sekonn',\n      other: '{{count}} Sekonnen'\n    }\n  },\n  halfAMinute: {\n    standalone: 'eng hallef Minutt',\n    withPreposition: 'enger hallwer Minutt'\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: 'manner wéi eng Minutt',\n      other: 'manner wéi {{count}} Minutten'\n    },\n    withPreposition: {\n      one: 'manner wéi enger Minutt',\n      other: 'manner wéi {{count}} Minutten'\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: 'eng Minutt',\n      other: '{{count}} Minutten'\n    },\n    withPreposition: {\n      one: 'enger Minutt',\n      other: '{{count}} Minutten'\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: 'ongeféier eng Stonn',\n      other: 'ongeféier {{count}} Stonnen'\n    },\n    withPreposition: {\n      one: 'ongeféier enger Stonn',\n      other: 'ongeféier {{count}} Stonnen'\n    }\n  },\n  xHours: {\n    standalone: {\n      one: 'eng Stonn',\n      other: '{{count}} Stonnen'\n    },\n    withPreposition: {\n      one: 'enger Stonn',\n      other: '{{count}} Stonnen'\n    }\n  },\n  xDays: {\n    standalone: {\n      one: 'een Dag',\n      other: '{{count}} Deeg'\n    },\n    withPreposition: {\n      one: 'engem Dag',\n      other: '{{count}} Deeg'\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: 'ongeféier eng Woch',\n      other: 'ongeféier {{count}} Wochen'\n    },\n    withPreposition: {\n      one: 'ongeféier enger Woche',\n      other: 'ongeféier {{count}} Wochen'\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: 'eng Woch',\n      other: '{{count}} Wochen'\n    },\n    withPreposition: {\n      one: 'enger Woch',\n      other: '{{count}} Wochen'\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: 'ongeféier ee Mount',\n      other: 'ongeféier {{count}} Méint'\n    },\n    withPreposition: {\n      one: 'ongeféier engem Mount',\n      other: 'ongeféier {{count}} Méint'\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: 'ee Mount',\n      other: '{{count}} Méint'\n    },\n    withPreposition: {\n      one: 'engem Mount',\n      other: '{{count}} Méint'\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: 'ongeféier ee Joer',\n      other: 'ongeféier {{count}} Joer'\n    },\n    withPreposition: {\n      one: 'ongeféier engem Joer',\n      other: 'ongeféier {{count}} Joer'\n    }\n  },\n  xYears: {\n    standalone: {\n      one: 'ee Joer',\n      other: '{{count}} Joer'\n    },\n    withPreposition: {\n      one: 'engem Joer',\n      other: '{{count}} Joer'\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: 'méi wéi ee Joer',\n      other: 'méi wéi {{count}} Joer'\n    },\n    withPreposition: {\n      one: 'méi wéi engem Joer',\n      other: 'méi wéi {{count}} Joer'\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: 'bal ee Joer',\n      other: 'bal {{count}} Joer'\n    },\n    withPreposition: {\n      one: 'bal engem Joer',\n      other: 'bal {{count}} Joer'\n    }\n  }\n};\nvar EXCEPTION_CONSONANTS = ['d', 'h', 'n', 't', 'z'];\nvar VOWELS = ['a,', 'e', 'i', 'o', 'u'];\nvar DIGITS_SPOKEN_N_NEEDED = [0, 1, 2, 3, 8, 9];\nvar FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED = [40, 50, 60, 70]; // Eifeler Regel\n\nfunction isFinalNNeeded(nextWords) {\n  var firstLetter = nextWords.charAt(0).toLowerCase();\n\n  if (VOWELS.indexOf(firstLetter) != -1 || EXCEPTION_CONSONANTS.indexOf(firstLetter) != -1) {\n    return true;\n  } // Numbers would need to converted into words for checking.\n  // Therefore, I have listed the digits that require a preceeding n with a few exceptions.\n\n\n  var firstWord = nextWords.split(' ')[0];\n  var number = parseInt(firstWord);\n\n  if (!isNaN(number) && DIGITS_SPOKEN_N_NEEDED.indexOf(number % 10) != -1 && FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED.indexOf(parseInt(firstWord.substring(0, 2))) == -1) {\n    return true;\n  } // Omit other checks as they are not expected here.\n\n\n  return false;\n}\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  var usageGroup = options !== null && options !== void 0 && options.addSuffix ? tokenValue.withPreposition : tokenValue.standalone;\n\n  if (typeof usageGroup === 'string') {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else {\n    result = usageGroup.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'a' + (isFinalNNeeded(result) ? 'n' : '') + ' ' + result;\n    } else {\n      return 'viru' + (isFinalNNeeded(result) ? 'n' : '') + ' ' + result;\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var day = date.getUTCDay();\n    var result = \"'läschte\";\n\n    if (day === 2 || day === 4) {\n      // Eifeler Regel: Add an n before the consonant d; Here \"Dënschdeg\" \"and Donneschde\".\n      result += 'n';\n    }\n\n    result += \"' eeee 'um' p\";\n    return result;\n  },\n  yesterday: \"'gëschter um' p\",\n  today: \"'haut um' p\",\n  tomorrow: \"'moien um' p\",\n  nextWeek: \"eeee 'um' p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n\n  if (typeof format === 'function') {\n    return format(date);\n  }\n\n  return format;\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)(\\.)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(v\\.? ?Chr\\.?|n\\.? ?Chr\\.?)/i,\n  abbreviated: /^(v\\.? ?Chr\\.?|n\\.? ?Chr\\.?)/i,\n  wide: /^(viru Christ<PERSON>|virun eiser Zäitrechnung|no <PERSON><PERSON>|eiser <PERSON>nung)/i\n};\nvar parseEraPatterns = {\n  any: [/^v/i, /^n/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234](\\.)? Quartal/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan|feb|mäe|abr|mee|jun|jul|aug|sep|okt|nov|dez)/i,\n  wide: /^(januar|februar|mäerz|abrëll|mee|juni|juli|august|september|oktober|november|dezember)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^mä/i, /^ab/i, /^me/i, /^jun/i, /^jul/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[smdf]/i,\n  short: /^(so|mé|dë|më|do|fr|sa)/i,\n  abbreviated: /^(son?|méi?|dën?|mët?|don?|fre?|sam?)\\.?/i,\n  wide: /^(sonndeg|méindeg|dënschdeg|mëttwoch|donneschdeg|freideg|samschdeg)/i\n};\nvar parseDayPatterns = {\n  any: [/^so/i, /^mé/i, /^dë/i, /^më/i, /^do/i, /^f/i, /^sa/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(mo\\.?|nomë\\.?|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i,\n  abbreviated: /^(moi\\.?|nomët\\.?|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i,\n  wide: /^(moies|nomëttes|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^m/i,\n    pm: /^n/i,\n    midnight: /^Mëtter/i,\n    noon: /^mëttes/i,\n    morning: /moies/i,\n    afternoon: /nomëttes/i,\n    // will never be matched. Afternoon is matched by `pm`\n    evening: /owes/i,\n    night: /nuets/i // will never be matched. Night is matched by `pm`\n\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// DIN 5008: https://de.wikipedia.org/wiki/Datumsformat#DIN_5008\nvar dateFormats = {\n  full: 'EEEE, do MMMM y',\n  // Méindeg, 7. Januar 2018\n  long: 'do MMMM y',\n  // 7. Januar 2018\n  medium: 'do MMM y',\n  // 7. Jan 2018\n  short: 'dd.MM.yy' // 07.01.18\n\n};\nvar timeFormats = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'um' {{time}}\",\n  long: \"{{date}} 'um' {{time}}\",\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['v.Chr.', 'n.Chr.'],\n  abbreviated: ['v.Chr.', 'n.Chr.'],\n  wide: ['viru Christus', 'no Christus']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1. Quartal', '2. Quartal', '3. Quartal', '4. Quartal']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['Jan', 'Feb', 'Mäe', 'Abr', 'Mee', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dez'],\n  wide: ['Januar', 'Februar', 'Mäerz', 'Abrëll', 'Mee', 'Juni', 'Juli', 'August', 'September', 'Oktober', 'November', 'Dezember']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'D', 'M', 'D', 'F', 'S'],\n  short: ['So', 'Mé', 'Dë', 'Më', 'Do', 'Fr', 'Sa'],\n  abbreviated: ['So.', 'Mé.', 'Dë.', 'Më.', 'Do.', 'Fr.', 'Sa.'],\n  wide: ['Sonndeg', 'Méindeg', 'Dënschdeg', 'Mëttwoch', 'Donneschdeg', 'Freideg', 'Samschdeg']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'mo.',\n    pm: 'nomë.',\n    midnight: 'Mëtternuecht',\n    noon: 'Mëtteg',\n    morning: 'Moien',\n    afternoon: 'Nomëtteg',\n    evening: 'Owend',\n    night: 'Nuecht'\n  },\n  abbreviated: {\n    am: 'moies',\n    pm: 'nomëttes',\n    midnight: 'Mëtternuecht',\n    noon: 'Mëtteg',\n    morning: 'Moien',\n    afternoon: 'Nomëtteg',\n    evening: 'Owend',\n    night: 'Nuecht'\n  },\n  wide: {\n    am: 'moies',\n    pm: 'nomëttes',\n    midnight: 'Mëtternuecht',\n    noon: 'Mëtteg',\n    morning: 'Moien',\n    afternoon: 'Nomëtteg',\n    evening: 'Owend',\n    night: 'Nuecht'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'mo.',\n    pm: 'nom.',\n    midnight: 'Mëtternuecht',\n    noon: 'mëttes',\n    morning: 'moies',\n    afternoon: 'nomëttes',\n    evening: 'owes',\n    night: 'nuets'\n  },\n  abbreviated: {\n    am: 'moies',\n    pm: 'nomëttes',\n    midnight: 'Mëtternuecht',\n    noon: 'mëttes',\n    morning: 'moies',\n    afternoon: 'nomëttes',\n    evening: 'owes',\n    night: 'nuets'\n  },\n  wide: {\n    am: 'moies',\n    pm: 'nomëttes',\n    midnight: 'Mëtternuecht',\n    noon: 'mëttes',\n    morning: 'moies',\n    afternoon: 'nomëttes',\n    evening: 'owes',\n    night: 'nuets'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Luxembourgish locale.\n * @language Luxembourgish\n * @iso-639-2 ltz\n * <AUTHOR> [@dwaxweiler]{@link https://github.com/dwaxweiler}\n */\nvar locale = {\n  code: 'lb',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 1\n    /* Monday */\n    ,\n    firstWeekContainsDate: 4\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "standalone", "one", "other", "withPreposition", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "EXCEPTION_CONSONANTS", "VOWELS", "DIGITS_SPOKEN_N_NEEDED", "FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED", "isFinalNNeeded", "nextWords", "firstLetter", "char<PERSON>t", "toLowerCase", "indexOf", "firstWord", "split", "number", "parseInt", "isNaN", "substring", "_default", "token", "count", "options", "result", "tokenValue", "usageGroup", "addSuffix", "replace", "String", "comparison", "module", "default", "formatRelativeLocale", "lastWeek", "date", "day", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "_baseDate", "_options", "format", "_index", "_interopRequireDefault", "obj", "__esModule", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "era", "matchPatterns", "narrow", "abbreviated", "wide", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "quarter", "index", "month", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formats", "full", "long", "medium", "defaultWidth", "time", "dateTime", "dirtyNumber", "Number", "values", "argument<PERSON>allback", "formattingValues", "defaultFormattingWidth", "_index2", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "weekStartsOn", "firstWeekContainsDate"], "sourceRoot": ""}
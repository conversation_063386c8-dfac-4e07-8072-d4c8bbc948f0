{"version": 3, "file": "86117.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAqFII,EA1BW,CACbC,cANkB,SAAuBC,EAAaC,GAEtD,OADaC,OAAOF,GACJ,GAClB,EAIEG,KAAK,EAAIR,EAAOE,SAAS,CACvBO,OA9DY,CACdC,OAAQ,CAAC,QAAS,SAClBC,YAAa,CAAC,QAAS,SACvBC,KAAM,CAAC,cAAe,kBA4DpBC,aAAc,SAEhBC,SAAS,EAAId,EAAOE,SAAS,CAC3BO,OA7DgB,CAClBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,KAAM,KAAM,KAAM,MAChCC,KAAM,CAAC,aAAc,aAAc,aAAc,eA2D/CC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAIhB,EAAOE,SAAS,CACzBO,OA/Dc,CAChBC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QACrGC,KAAM,CAAC,SAAU,UAAW,OAAQ,QAAS,MAAO,OAAQ,OAAQ,SAAU,YAAa,UAAW,WAAY,aA6DhHC,aAAc,SAEhBI,KAAK,EAAIjB,EAAOE,SAAS,CACvBO,OA9DY,CACdC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCQ,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,OAC5CP,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,QACxDC,KAAM,CAAC,SAAU,SAAU,SAAU,SAAU,UAAW,SAAU,YA2DlEC,aAAc,SAEhBM,WAAW,EAAInB,EAAOE,SAAS,CAC7BO,OA5DkB,CACpBC,OAAQ,CACNU,GAAI,IACJC,GAAI,IACJC,SAAU,UACVC,KAAM,SACNC,QAAS,WACTC,UAAW,aACXC,QAAS,aACTC,MAAO,YAEThB,YAAa,CACXS,GAAI,OACJC,GAAI,OACJC,SAAU,UACVC,KAAM,SACNC,QAAS,WACTC,UAAW,aACXC,QAAS,aACTC,MAAO,YAETf,KAAM,CACJQ,GAAI,OACJC,GAAI,OACJC,SAAU,UACVC,KAAM,SACNC,QAAS,cACTC,UAAW,mBACXC,QAAS,aACTC,MAAO,aAgCPd,aAAc,UAIlBhB,EAAA,QAAkBM,EAClByB,EAAO/B,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/nn/_lib/localize/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['f.Kr.', 'e.Kr.'],\n  abbreviated: ['f.Kr.', 'e.Kr.'],\n  wide: ['før <PERSON>', 'et<PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1. kvartal', '2. kvartal', '3. kvartal', '4. kvartal']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['jan.', 'feb.', 'mars', 'apr.', 'mai', 'juni', 'juli', 'aug.', 'sep.', 'okt.', 'nov.', 'des.'],\n  wide: ['januar', 'februar', 'mars', 'april', 'mai', 'juni', 'juli', 'august', 'september', 'oktober', 'november', 'desember']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'T', 'O', 'T', 'F', 'L'],\n  short: ['su', 'må', 'ty', 'on', 'to', 'fr', 'lau'],\n  abbreviated: ['sun', 'mån', 'tys', 'ons', 'tor', 'fre', 'laur'],\n  wide: ['sundag', 'måndag', 'tysdag', 'onsdag', 'torsdag', 'fredag', 'laurdag']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morg.',\n    afternoon: 'på etterm.',\n    evening: 'på kvelden',\n    night: 'på natta'\n  },\n  abbreviated: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morg.',\n    afternoon: 'på etterm.',\n    evening: 'på kvelden',\n    night: 'på natta'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'midnatt',\n    noon: 'middag',\n    morning: 'på morgonen',\n    afternoon: 'på ettermiddagen',\n    evening: 'på kvelden',\n    night: 'på natta'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "_options", "Number", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "module"], "sourceRoot": ""}
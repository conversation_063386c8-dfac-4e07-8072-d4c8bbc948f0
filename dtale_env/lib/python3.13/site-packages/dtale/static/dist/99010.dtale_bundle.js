"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[99010],{75387:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={lessThanXSeconds:"តិចជាង {{count}} វិនាទី",xSeconds:"{{count}} វិនាទី",halfAMinute:"កន្លះនាទី",lessThanXMinutes:"តិចជាង {{count}} នាទី",xMinutes:"{{count}} នាទី",aboutXHours:"ប្រហែល {{count}} ម៉ោង",xHours:"{{count}} ម៉ោង",xDays:"{{count}} ថ្ងៃ",aboutXWeeks:"ប្រហែល {{count}} សប្តាហ៍",xWeeks:"{{count}} សប្តាហ៍",aboutXMonths:"ប្រហែល {{count}} ខែ",xMonths:"{{count}} ខែ",aboutXYears:"ប្រហែល {{count}} ឆ្នាំ",xYears:"{{count}} ឆ្នាំ",overXYears:"ជាង {{count}} ឆ្នាំ",almostXYears:"ជិត {{count}} ឆ្នាំ"},u=function(t,e,u){var n=o[t];return"number"==typeof e&&(n=n.replace("{{count}}",e.toString())),null!=u&&u.addSuffix?u.comparison&&u.comparison>0?"ក្នុងរយៈពេល "+n:n+"មុន":n};e.default=u,t.exports=e.default}}]);
//# sourceMappingURL=99010.dtale_bundle.js.map
{"version": 3, "file": "80985.dtale_bundle.js", "mappings": "6EAwFAA,EAAOC,QAjFP,SAAgBC,GACd,MAAMC,EAAS,CACbC,UAAW,SACXC,UAAW,EACXC,SAAU,CACR,CACEC,MAAO,mBAETL,EAAKM,cAIT,MAAO,CACLC,KAAM,SACNC,kBAAkB,EAClBC,SAAU,CACRC,QAAS,qPAKTC,SAAU,uMAGVC,QAAS,sCAEXC,QAAS,OACTC,SAAU,CACRd,EAAKe,QAAQ,OAAQ,QACrBf,EAAKe,QACH,IACA,IACA,CACEZ,UAAW,IAGf,CACED,UAAW,WACXc,cAAe,kBACfC,IAAK,UACLJ,QAAS,KACTC,SAAU,CAAEd,EAAKkB,wBAEnB,CACEhB,UAAW,QACXc,cAAe,kBACfC,IAAK,IACLH,SAAU,CACR,CACEE,cAAe,sBAEjBhB,EAAKkB,wBAGT,CACEhB,UAAW,WACXG,MAAO,sBAET,CACEH,UAAW,OACXG,MAAO,QACPY,IAAK,IACLR,SAAU,CACR,eAAgB,kCAGpB,CACEP,UAAW,OACXG,MAAO,kBAET,CACEW,cAAe,QACfC,IAAK,IACLH,SAAU,CAAEd,EAAKkB,wBAEnBlB,EAAKmB,kBACLlB,GAGN,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/monkey.js"], "sourcesContent": ["/*\nLanguage: Monkey\nDescription: Monkey2 is an easy to use, cross platform, games oriented programming language from Blitz Research.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://blitzresearch.itch.io/monkey2\n*/\n\nfunction monkey(hljs) {\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      {\n        begin: '[$][a-fA-F0-9]+'\n      },\n      hljs.NUMBER_MODE\n    ]\n  };\n\n  return {\n    name: 'Monkey',\n    case_insensitive: true,\n    keywords: {\n      keyword: 'public private property continue exit extern new try catch ' +\n        'eachin not abstract final select case default const local global field ' +\n        'end if then else elseif endif while wend repeat until forever for ' +\n        'to step next return module inline throw import',\n\n      built_in: 'DebugLog DebugStop Error Print ACos ACosr ASin ASinr ATan ATan2 ATan2r ATanr Abs Abs Ceil ' +\n        'Clamp Clamp Cos Cosr Exp Floor Log Max Max Min Min Pow Sgn Sgn Sin Sinr Sqrt Tan Tanr Seed PI HALFPI TWOPI',\n\n      literal: 'true false null and or shl shr mod'\n    },\n    illegal: /\\/\\*/,\n    contains: [\n      hljs.COMMENT('#rem', '#end'),\n      hljs.COMMENT(\n        \"'\",\n        '$',\n        {\n          relevance: 0\n        }\n      ),\n      {\n        className: 'function',\n        beginKeywords: 'function method',\n        end: '[(=:]|$',\n        illegal: /\\n/,\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: '$',\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        className: 'built_in',\n        begin: '\\\\b(self|super)\\\\b'\n      },\n      {\n        className: 'meta',\n        begin: '\\\\s*#',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'if else elseif endif end then'\n        }\n      },\n      {\n        className: 'meta',\n        begin: '^\\\\s*strict\\\\b'\n      },\n      {\n        beginKeywords: 'alias',\n        end: '=',\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      hljs.QUOTE_STRING_MODE,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = monkey;\n"], "names": ["module", "exports", "hljs", "NUMBER", "className", "relevance", "variants", "begin", "NUMBER_MODE", "name", "case_insensitive", "keywords", "keyword", "built_in", "literal", "illegal", "contains", "COMMENT", "beginKeywords", "end", "UNDERSCORE_TITLE_MODE", "QUOTE_STRING_MODE"], "sourceRoot": ""}
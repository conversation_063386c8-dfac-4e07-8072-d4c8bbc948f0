{"version": 3, "file": "96349.dtale_bundle.js", "mappings": "8FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,SAAU,kCACVC,UAAW,qBACXC,MAAO,kBACPC,SAAU,oBACVC,SAAU,oCACVC,MAAO,KAOLC,EAJiB,SAAwBC,EAAOC,EAAOC,EAAWC,GACpE,OAAOX,EAAqBQ,EAC9B,EAGAV,EAAA,QAAkBS,EAClBK,EAAOd,QAAUA,EAAQe,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/km/_lib/formatRelative/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"'ថ្ងៃ'eeee'ស​ប្តា​ហ៍​មុនម៉ោង' p\",\n  yesterday: \"'ម្សិលមិញនៅម៉ោង' p\",\n  today: \"'ថ្ងៃនេះម៉ោង' p\",\n  tomorrow: \"'ថ្ងៃស្អែកម៉ោង' p\",\n  nextWeek: \"'ថ្ងៃ'eeee'ស​ប្តា​ហ៍​ក្រោយម៉ោង' p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "_default", "token", "_date", "_baseDate", "_options", "module", "default"], "sourceRoot": ""}
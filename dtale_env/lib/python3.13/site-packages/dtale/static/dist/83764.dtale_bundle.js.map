{"version": 3, "file": "83764.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAyIII,EA9BW,CACbC,cAjBkB,SAAuBC,EAAaC,GACtD,IAAIC,EAASC,OAAOH,GAChBI,EAAOH,aAAyC,EAASA,EAAQG,KAWrE,OAAOF,GARM,SAATE,EACO,KACS,SAATA,GAA4B,WAATA,GAA8B,WAATA,EACxC,KAEA,KAIb,EAIEC,KAAK,EAAIV,EAAOE,SAAS,CACvBS,OA9GY,CACdC,OAAQ,CAAC,UAAW,QACpBC,YAAa,CAAC,WAAY,SAC1BC,KAAM,CAAC,eAAgB,cA4GrBC,aAAc,SAEhBC,SAAS,EAAIhB,EAAOE,SAAS,CAC3BS,OA7GgB,CAClBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,UAAW,UAAW,UAAW,WAC/CC,KAAM,CAAC,cAAe,cAAe,cAAe,gBA2GlDC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAIlB,EAAOE,SAAS,CACzBS,OA/Gc,CAChBC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,QAAS,OAAQ,QAAS,QACvGC,KAAM,CAAC,SAAU,UAAW,OAAQ,SAAU,MAAO,OAAQ,OAAQ,SAAU,WAAY,UAAW,SAAU,YA6G9GC,aAAc,OACdI,iBA5GwB,CAC1BP,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,QAAS,OAAQ,QAAS,QACvGC,KAAM,CAAC,SAAU,UAAW,QAAS,SAAU,MAAO,OAAQ,OAAQ,UAAW,WAAY,UAAW,SAAU,YA0GhHM,uBAAwB,SAE1BC,KAAK,EAAIrB,EAAOE,SAAS,CACvBS,OA3GY,CACdC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCU,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CT,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACxDC,KAAM,CAAC,cAAe,cAAe,UAAW,QAAS,UAAW,UAAW,YAwG7EC,aAAc,SAEhBQ,WAAW,EAAIvB,EAAOE,SAAS,CAC7BS,OAzGkB,CACpBC,OAAQ,CACNY,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,QACNC,QAAS,OACTC,UAAW,OACXC,QAAS,OACTC,MAAO,QAETlB,YAAa,CACXW,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,QACNC,QAAS,OACTC,UAAW,OACXC,QAAS,OACTC,MAAO,QAETjB,KAAM,CACJU,GAAI,KACJC,GAAI,KACJC,SAAU,UACVC,KAAM,UACNC,QAAS,OACTC,UAAW,OACXC,QAAS,QACTC,MAAO,SA6EPhB,aAAc,MACdI,iBA3E4B,CAC9BP,OAAQ,CACNY,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,QACNC,QAAS,OACTC,UAAW,MACXC,QAAS,OACTC,MAAO,QAETlB,YAAa,CACXW,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,QACNC,QAAS,OACTC,UAAW,MACXC,QAAS,OACTC,MAAO,QAETjB,KAAM,CACJU,GAAI,KACJC,GAAI,KACJC,SAAU,UACVC,KAAM,UACNC,QAAS,OACTC,UAAW,MACXC,QAAS,SACTC,MAAO,SA+CPX,uBAAwB,UAI5BvB,EAAA,QAAkBM,EAClB6B,EAAOnC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/ru/_lib/localize/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['до н.э.', 'н.э.'],\n  abbreviated: ['до н. э.', 'н. э.'],\n  wide: ['до нашей эры', 'нашей эры']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-й кв.', '2-й кв.', '3-й кв.', '4-й кв.'],\n  wide: ['1-й квартал', '2-й квартал', '3-й квартал', '4-й квартал']\n};\nvar monthValues = {\n  narrow: ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n  abbreviated: ['янв.', 'фев.', 'март', 'апр.', 'май', 'июнь', 'июль', 'авг.', 'сент.', 'окт.', 'нояб.', 'дек.'],\n  wide: ['январь', 'февраль', 'март', 'апрель', 'май', 'июнь', 'июль', 'август', 'сентябрь', 'октябрь', 'ноябрь', 'декабрь']\n};\nvar formattingMonthValues = {\n  narrow: ['Я', 'Ф', 'М', 'А', 'М', 'И', 'И', 'А', 'С', 'О', 'Н', 'Д'],\n  abbreviated: ['янв.', 'фев.', 'мар.', 'апр.', 'мая', 'июн.', 'июл.', 'авг.', 'сент.', 'окт.', 'нояб.', 'дек.'],\n  wide: ['января', 'февраля', 'марта', 'апреля', 'мая', 'июня', 'июля', 'августа', 'сентября', 'октября', 'ноября', 'декабря']\n};\nvar dayValues = {\n  narrow: ['В', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['вс', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],\n  abbreviated: ['вск', 'пнд', 'втр', 'срд', 'чтв', 'птн', 'суб'],\n  wide: ['воскресенье', 'понедельник', 'вторник', 'среда', 'четверг', 'пятница', 'суббота']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утро',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ночь'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утро',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ночь'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полночь',\n    noon: 'полдень',\n    morning: 'утро',\n    afternoon: 'день',\n    evening: 'вечер',\n    night: 'ночь'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утра',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночи'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полн.',\n    noon: 'полд.',\n    morning: 'утра',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночи'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'полночь',\n    noon: 'полдень',\n    morning: 'утра',\n    afternoon: 'дня',\n    evening: 'вечера',\n    night: 'ночи'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  var suffix;\n\n  if (unit === 'date') {\n    suffix = '-е';\n  } else if (unit === 'week' || unit === 'minute' || unit === 'second') {\n    suffix = '-я';\n  } else {\n    suffix = '-й';\n  }\n\n  return number + suffix;\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "module"], "sourceRoot": ""}
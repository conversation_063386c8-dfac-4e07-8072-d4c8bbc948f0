{"version": 3, "file": "8948.dtale_bundle.js", "mappings": "4EAoBA,SAASA,EAAUC,GACjB,OAOF,YAAmBC,GAEjB,OADeA,EAAKC,IAAKC,GApB3B,SAAgBH,GACd,OAAKA,EACa,iBAAPA,EAAwBA,EAE5BA,EAAGI,OAHM,IAIlB,CAeiCA,CAAOD,IAAIE,KAAK,GAEjD,CAVSC,CAAO,MAAON,EAAI,IAC3B,CAkBA,SAASO,EAASA,EAAUC,EAAM,CAAC,GAEjC,OADAA,EAAID,SAAWA,EACRC,CACT,CAkIAC,EAAOC,QAhIP,SAAgBC,GACd,MAAMC,EAAW,iBACXC,EAAUN,EAAS,CACvBI,EAAKG,oBACLH,EAAKI,qBACLJ,EAAKE,QACH,UACA,OACA,CACEG,UAAW,EACXC,SAAU,CACR,CAEEC,MAAO,OACPF,UAAW,GAEb,CACEG,UAAW,SACXD,MAAO,mBAMXE,EAAS,CACbD,UAAW,SACXD,MAAO,iBACPD,SAAU,CAAEN,EAAKU,mBAEbC,EAASf,EAAS,CACtBI,EAAKY,mBACLZ,EAAKa,gBAEDC,EAASlB,EAAS,CACtB,CACEW,MAAO,MACPQ,IAAK,OAEP,CACER,MAAO,MACPQ,IAAK,OAEP,CACER,MAAO,OACPQ,IAAK,OACLV,UAAW,IAEbL,EAAKgB,iBACLhB,EAAKiB,mBAEP,CACET,UAAW,WAIb,MAAO,CACLU,KAAM,SACNC,SAAU,CACRC,SAAU,aACVC,QAAS,kBACTC,QACM,6TAQRhB,SAAU,CACRN,EAAKuB,QAAQ,CACXC,OAAQ,SACRnB,UAAW,KAEbH,EACAY,EACAL,EACAE,EACA,CACEH,UAAW,QACXiB,cAAe,6BACfV,IAAK,KACLW,QAAS,IACTpB,SAAU,CACR,CACEmB,cAAe,sBAEjBzB,EAAK2B,wBAGT,CACEnB,UAAW,OACXD,MAAO,aACPF,UAAW,GAEb,CAEEG,UAAW,OACXD,MAAON,EAAW,UAClBI,UAAW,GAEb,CAGEE,MAAO,KACPQ,IAAK,IACLV,UAAW,EACXC,SAAU,CACRJ,EACAY,EACAL,EACAE,EACA,SAGJ,CAEEH,UAAW,SACXD,MAAO,UAAYnB,EAAUa,EAAW,KACxC2B,cAAc,EACdb,IAAKd,EAAW,IAChBI,UAAW,IAGfqB,QAAS,QAEb,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/groovy.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\n Language: Groovy\n Author: <PERSON> <<EMAIL>>\n Description: Groovy programming language implementation inspired from Vsevolod's Java mode\n Website: https://groovy-lang.org\n */\n\nfunction variants(variants, obj = {}) {\n  obj.variants = variants;\n  return obj;\n}\n\nfunction groovy(hljs) {\n  const IDENT_RE = '[A-Za-z0-9_$]+';\n  const COMMENT = variants([\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    hljs.COMMENT(\n      '/\\\\*\\\\*',\n      '\\\\*/',\n      {\n        relevance: 0,\n        contains: [\n          {\n            // eat up @'s in emails to prevent them to be recognized as doctags\n            begin: /\\w+@/,\n            relevance: 0\n          },\n          {\n            className: 'doctag',\n            begin: '@[A-Za-z]+'\n          }\n        ]\n      }\n    )\n  ]);\n  const REGEXP = {\n    className: 'regexp',\n    begin: /~?\\/[^\\/\\n]+\\//,\n    contains: [ hljs.BACKSLASH_ESCAPE ]\n  };\n  const NUMBER = variants([\n    hljs.BINARY_NUMBER_MODE,\n    hljs.C_NUMBER_MODE\n  ]);\n  const STRING = variants([\n    {\n      begin: /\"\"\"/,\n      end: /\"\"\"/\n    },\n    {\n      begin: /'''/,\n      end: /'''/\n    },\n    {\n      begin: \"\\\\$/\",\n      end: \"/\\\\$\",\n      relevance: 10\n    },\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE\n  ],\n  {\n    className: \"string\"\n  }\n  );\n\n  return {\n    name: 'Groovy',\n    keywords: {\n      built_in: 'this super',\n      literal: 'true false null',\n      keyword:\n            'byte short char int long boolean float double void ' +\n            // groovy specific keywords\n            'def as in assert trait ' +\n            // common keywords with Java\n            'abstract static volatile transient public private protected synchronized final ' +\n            'class interface enum if else for while switch case break default continue ' +\n            'throw throws try catch finally implements extends new import package return instanceof'\n    },\n    contains: [\n      hljs.SHEBANG({\n        binary: \"groovy\",\n        relevance: 10\n      }),\n      COMMENT,\n      STRING,\n      REGEXP,\n      NUMBER,\n      {\n        className: 'class',\n        beginKeywords: 'class interface trait enum',\n        end: /\\{/,\n        illegal: ':',\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        className: 'meta',\n        begin: '@[A-Za-z]+',\n        relevance: 0\n      },\n      {\n        // highlight map keys and named parameters as attrs\n        className: 'attr',\n        begin: IDENT_RE + '[ \\t]*:',\n        relevance: 0\n      },\n      {\n        // catch middle element of the ternary operator\n        // to avoid highlight it as a label, named parameter, or map key\n        begin: /\\?/,\n        end: /:/,\n        relevance: 0,\n        contains: [\n          COMMENT,\n          STRING,\n          REGEXP,\n          NUMBER,\n          'self'\n        ]\n      },\n      {\n        // highlight labeled statements\n        className: 'symbol',\n        begin: '^[ \\t]*' + lookahead(IDENT_RE + ':'),\n        excludeBegin: true,\n        end: IDENT_RE + ':',\n        relevance: 0\n      }\n    ],\n    illegal: /#|<\\//\n  };\n}\n\nmodule.exports = groovy;\n"], "names": ["<PERSON><PERSON><PERSON>", "re", "args", "map", "x", "source", "join", "concat", "variants", "obj", "module", "exports", "hljs", "IDENT_RE", "COMMENT", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "relevance", "contains", "begin", "className", "REGEXP", "BACKSLASH_ESCAPE", "NUMBER", "BINARY_NUMBER_MODE", "C_NUMBER_MODE", "STRING", "end", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "name", "keywords", "built_in", "literal", "keyword", "SHEBANG", "binary", "beginKeywords", "illegal", "UNDERSCORE_TITLE_MODE", "excludeBegin"], "sourceRoot": ""}
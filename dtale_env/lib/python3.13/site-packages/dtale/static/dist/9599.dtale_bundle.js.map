{"version": 3, "file": "9599.dtale_bundle.js", "mappings": "6FAOA,SAASA,EAAWC,EAAQC,GAE1B,QAAmBC,IAAfF,EAAOG,KAA+B,IAAVF,EAC9B,OAAOD,EAAOG,IAGhB,IAAIC,EAAQH,EAAQ,GAChBI,EAASJ,EAAQ,IAErB,OAAc,IAAVG,GAA0B,KAAXC,EACVL,EAAOM,mBAAmBC,QAAQ,YAAaC,OAAOP,IACpDG,GAAS,GAAKA,GAAS,IAAMC,EAAS,IAAMA,EAAS,IACvDL,EAAOS,iBAAiBF,QAAQ,YAAaC,OAAOP,IAEpDD,EAAOU,eAAeH,QAAQ,YAAaC,OAAOP,GAE7D,CAEA,SAASU,EAAqBX,GAC5B,OAAO,SAAUC,EAAOW,GACtB,OAAIA,GAAWA,EAAQC,UACjBD,EAAQE,YAAcF,EAAQE,WAAa,EACzCd,EAAOe,OACFhB,EAAWC,EAAOe,OAAQd,GAE1B,QAAUF,EAAWC,EAAOgB,QAASf,GAG1CD,EAAOiB,KACFlB,EAAWC,EAAOiB,KAAMhB,GAExBF,EAAWC,EAAOgB,QAASf,GAAS,QAIxCF,EAAWC,EAAOgB,QAASf,EAEtC,CACF,CA3CAiB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EA0ClB,IAYIE,EAAuB,CACzBC,iBAAkBZ,EAAqB,CACrCK,QAAS,CACPb,IAAK,kBACLG,mBAAoB,4BACpBG,iBAAkB,4BAClBC,eAAgB,4BAElBK,OAAQ,CACNZ,IAAK,yBACLG,mBAAoB,mCACpBG,iBAAkB,mCAClBC,eAAgB,qCAGpBc,SAAUb,EAAqB,CAC7BK,QAAS,CACPV,mBAAoB,oBACpBG,iBAAkB,oBAClBC,eAAgB,oBAElBO,KAAM,CACJX,mBAAoB,yBACpBG,iBAAkB,yBAClBC,eAAgB,yBAElBK,OAAQ,CACNT,mBAAoB,yBACpBG,iBAAkB,yBAClBC,eAAgB,2BAGpBe,YA5CgB,SAAqBC,EAAGd,GACxC,OAAIA,GAAWA,EAAQC,UACjBD,EAAQE,YAAcF,EAAQE,WAAa,EACtC,kBAEA,kBAIJ,YACT,EAmCEa,iBAAkBhB,EAAqB,CACrCK,QAAS,CACPb,IAAK,kBACLG,mBAAoB,4BACpBG,iBAAkB,4BAClBC,eAAgB,4BAElBK,OAAQ,CACNZ,IAAK,yBACLG,mBAAoB,mCACpBG,iBAAkB,mCAClBC,eAAgB,qCAGpBkB,SAAUjB,EAAqB,CAC7BK,QAAS,CACPV,mBAAoB,oBACpBG,iBAAkB,oBAClBC,eAAgB,oBAElBO,KAAM,CACJX,mBAAoB,yBACpBG,iBAAkB,yBAClBC,eAAgB,yBAElBK,OAAQ,CACNT,mBAAoB,yBACpBG,iBAAkB,yBAClBC,eAAgB,2BAGpBmB,YAAalB,EAAqB,CAChCK,QAAS,CACPV,mBAAoB,yBACpBG,iBAAkB,wBAClBC,eAAgB,yBAElBK,OAAQ,CACNT,mBAAoB,mCACpBG,iBAAkB,mCAClBC,eAAgB,qCAGpBoB,OAAQnB,EAAqB,CAC3BK,QAAS,CACPV,mBAAoB,oBACpBG,iBAAkB,oBAClBC,eAAgB,oBAElBO,KAAM,CACJX,mBAAoB,yBACpBG,iBAAkB,yBAClBC,eAAgB,yBAElBK,OAAQ,CACNT,mBAAoB,yBACpBG,iBAAkB,yBAClBC,eAAgB,2BAGpBqB,MAAOpB,EAAqB,CAC1BK,QAAS,CACPV,mBAAoB,kBACpBG,iBAAkB,gBAClBC,eAAgB,oBAGpBsB,YAAarB,EAAqB,CAChCK,QAAS,CACPV,mBAAoB,wBAEpBG,iBAAkB,yBAElBC,eAAgB,0BAGlBK,OAAQ,CACNT,mBAAoB,iCAEpBG,iBAAkB,kCAElBC,eAAgB,sCAIpBuB,OAAQtB,EAAqB,CAC3BK,QAAS,CACPV,mBAAoB,kBACpBG,iBAAkB,mBAClBC,eAAgB,uBAGpBwB,aAAcvB,EAAqB,CACjCK,QAAS,CACPV,mBAAoB,wBACpBG,iBAAkB,yBAClBC,eAAgB,0BAElBK,OAAQ,CACNT,mBAAoB,iCACpBG,iBAAkB,kCAClBC,eAAgB,sCAGpByB,QAASxB,EAAqB,CAC5BK,QAAS,CACPV,mBAAoB,kBACpBG,iBAAkB,mBAClBC,eAAgB,uBAGpB0B,YAAazB,EAAqB,CAChCK,QAAS,CACPV,mBAAoB,sBACpBG,iBAAkB,uBAClBC,eAAgB,wBAElBK,OAAQ,CACNT,mBAAoB,+BACpBG,iBAAkB,gCAClBC,eAAgB,oCAGpB2B,OAAQ1B,EAAqB,CAC3BK,QAAS,CACPV,mBAAoB,gBACpBG,iBAAkB,iBAClBC,eAAgB,qBAGpB4B,WAAY3B,EAAqB,CAC/BK,QAAS,CACPV,mBAAoB,yBACpBG,iBAAkB,0BAClBC,eAAgB,4BAElBK,OAAQ,CACNT,mBAAoB,gCACpBG,iBAAkB,iCAClBC,eAAgB,qCAGpB6B,aAAc5B,EAAqB,CACjCK,QAAS,CACPV,mBAAoB,sBACpBG,iBAAkB,uBAClBC,eAAgB,yBAElBK,OAAQ,CACNT,mBAAoB,2BACpBG,iBAAkB,4BAClBC,eAAgB,iCAUlB8B,EALiB,SAAwBC,EAAOxC,EAAOW,GAEzD,OADAA,EAAUA,GAAW,CAAC,EACfU,EAAqBmB,GAAOxC,EAAOW,EAC5C,EAGAQ,EAAA,QAAkBoB,EAClBE,EAAOtB,QAAUA,EAAQuB,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/be/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nfunction declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n\n  var rem10 = count % 10;\n  var rem100 = count % 100; // 1, 21, 31, ...\n\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace('{{count}}', String(count)); // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace('{{count}}', String(count)); // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace('{{count}}', String(count));\n  }\n}\n\nfunction buildLocalizeTokenFn(scheme) {\n  return function (count, options) {\n    if (options && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return 'праз ' + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + ' таму';\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\n\nvar halfAMinute = function halfAMinute(_, options) {\n  if (options && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'праз паўхвіліны';\n    } else {\n      return 'паўхвіліны таму';\n    }\n  }\n\n  return 'паўхвіліны';\n};\n\nvar formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: 'менш за секунду',\n      singularNominative: 'менш за {{count}} секунду',\n      singularGenitive: 'менш за {{count}} секунды',\n      pluralGenitive: 'менш за {{count}} секунд'\n    },\n    future: {\n      one: 'менш, чым праз секунду',\n      singularNominative: 'менш, чым праз {{count}} секунду',\n      singularGenitive: 'менш, чым праз {{count}} секунды',\n      pluralGenitive: 'менш, чым праз {{count}} секунд'\n    }\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} секунда',\n      singularGenitive: '{{count}} секунды',\n      pluralGenitive: '{{count}} секунд'\n    },\n    past: {\n      singularNominative: '{{count}} секунду таму',\n      singularGenitive: '{{count}} секунды таму',\n      pluralGenitive: '{{count}} секунд таму'\n    },\n    future: {\n      singularNominative: 'праз {{count}} секунду',\n      singularGenitive: 'праз {{count}} секунды',\n      pluralGenitive: 'праз {{count}} секунд'\n    }\n  }),\n  halfAMinute: halfAMinute,\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: 'менш за хвіліну',\n      singularNominative: 'менш за {{count}} хвіліну',\n      singularGenitive: 'менш за {{count}} хвіліны',\n      pluralGenitive: 'менш за {{count}} хвілін'\n    },\n    future: {\n      one: 'менш, чым праз хвіліну',\n      singularNominative: 'менш, чым праз {{count}} хвіліну',\n      singularGenitive: 'менш, чым праз {{count}} хвіліны',\n      pluralGenitive: 'менш, чым праз {{count}} хвілін'\n    }\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} хвіліна',\n      singularGenitive: '{{count}} хвіліны',\n      pluralGenitive: '{{count}} хвілін'\n    },\n    past: {\n      singularNominative: '{{count}} хвіліну таму',\n      singularGenitive: '{{count}} хвіліны таму',\n      pluralGenitive: '{{count}} хвілін таму'\n    },\n    future: {\n      singularNominative: 'праз {{count}} хвіліну',\n      singularGenitive: 'праз {{count}} хвіліны',\n      pluralGenitive: 'праз {{count}} хвілін'\n    }\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'каля {{count}} гадзіны',\n      singularGenitive: 'каля {{count}} гадзін',\n      pluralGenitive: 'каля {{count}} гадзін'\n    },\n    future: {\n      singularNominative: 'прыблізна праз {{count}} гадзіну',\n      singularGenitive: 'прыблізна праз {{count}} гадзіны',\n      pluralGenitive: 'прыблізна праз {{count}} гадзін'\n    }\n  }),\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} гадзіна',\n      singularGenitive: '{{count}} гадзіны',\n      pluralGenitive: '{{count}} гадзін'\n    },\n    past: {\n      singularNominative: '{{count}} гадзіну таму',\n      singularGenitive: '{{count}} гадзіны таму',\n      pluralGenitive: '{{count}} гадзін таму'\n    },\n    future: {\n      singularNominative: 'праз {{count}} гадзіну',\n      singularGenitive: 'праз {{count}} гадзіны',\n      pluralGenitive: 'праз {{count}} гадзін'\n    }\n  }),\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} дзень',\n      singularGenitive: '{{count}} дні',\n      pluralGenitive: '{{count}} дзён'\n    }\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'каля {{count}} месяца',\n      // TODO\n      singularGenitive: 'каля {{count}} месяцаў',\n      // TODO\n      pluralGenitive: 'каля {{count}} месяцаў' // TODO\n\n    },\n    future: {\n      singularNominative: 'прыблізна праз {{count}} месяц',\n      // TODO\n      singularGenitive: 'прыблізна праз {{count}} месяцы',\n      // TODO\n      pluralGenitive: 'прыблізна праз {{count}} месяцаў' // TODO\n\n    }\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} месяц',\n      singularGenitive: '{{count}} месяцы',\n      pluralGenitive: '{{count}} месяцаў'\n    }\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'каля {{count}} месяца',\n      singularGenitive: 'каля {{count}} месяцаў',\n      pluralGenitive: 'каля {{count}} месяцаў'\n    },\n    future: {\n      singularNominative: 'прыблізна праз {{count}} месяц',\n      singularGenitive: 'прыблізна праз {{count}} месяцы',\n      pluralGenitive: 'прыблізна праз {{count}} месяцаў'\n    }\n  }),\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} месяц',\n      singularGenitive: '{{count}} месяцы',\n      pluralGenitive: '{{count}} месяцаў'\n    }\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'каля {{count}} года',\n      singularGenitive: 'каля {{count}} гадоў',\n      pluralGenitive: 'каля {{count}} гадоў'\n    },\n    future: {\n      singularNominative: 'прыблізна праз {{count}} год',\n      singularGenitive: 'прыблізна праз {{count}} гады',\n      pluralGenitive: 'прыблізна праз {{count}} гадоў'\n    }\n  }),\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} год',\n      singularGenitive: '{{count}} гады',\n      pluralGenitive: '{{count}} гадоў'\n    }\n  }),\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'больш за {{count}} год',\n      singularGenitive: 'больш за {{count}} гады',\n      pluralGenitive: 'больш за {{count}} гадоў'\n    },\n    future: {\n      singularNominative: 'больш, чым праз {{count}} год',\n      singularGenitive: 'больш, чым праз {{count}} гады',\n      pluralGenitive: 'больш, чым праз {{count}} гадоў'\n    }\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'амаль {{count}} год',\n      singularGenitive: 'амаль {{count}} гады',\n      pluralGenitive: 'амаль {{count}} гадоў'\n    },\n    future: {\n      singularNominative: 'амаль праз {{count}} год',\n      singularGenitive: 'амаль праз {{count}} гады',\n      pluralGenitive: 'амаль праз {{count}} гадоў'\n    }\n  })\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  options = options || {};\n  return formatDistanceLocale[token](count, options);\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["declension", "scheme", "count", "undefined", "one", "rem10", "rem100", "singularNominative", "replace", "String", "singularGenitive", "pluralGenitive", "buildLocalizeTokenFn", "options", "addSuffix", "comparison", "future", "regular", "past", "Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "xSeconds", "halfAMinute", "_", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_default", "token", "module", "default"], "sourceRoot": ""}
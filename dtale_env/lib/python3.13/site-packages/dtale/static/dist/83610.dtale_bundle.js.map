{"version": 3, "file": "83610.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAGvF,IAuJII,EA5BW,CACbC,cALkB,SAAuBC,EAAaC,GACtD,OAAOC,OAAOF,EAChB,EAIEG,KAAK,EAAIR,EAAOE,SAAS,CACvBO,OA9HY,CACdC,OAAQ,CAAC,SAAU,UACnBC,YAAa,CAAC,SAAU,UAExBC,KAAM,CAAC,sBAAuB,iBA2H5BC,aAAc,SAEhBC,SAAS,EAAId,EAAOE,SAAS,CAC3BO,OA3HgB,CAElBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KAExBC,YAAa,CAAC,SAAU,SAAU,SAAU,UAE5CC,KAAM,CAAC,mBAAoB,oBAAqB,oBAAqB,sBAsHnEC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAIhB,EAAOE,SAAS,CACzBO,OA1Hc,CAEhBC,OAAQ,CAAC,IAAK,KAAM,KAAM,IAAK,KAAM,KAAM,KAAM,IAAK,KAAM,IAAK,IAAK,MAEtEC,YAAa,CAAC,MAAO,QAAS,QAAS,OAAQ,KAAM,OAAQ,OAAQ,MAAO,QAAS,OAAQ,MAAO,QAEpGC,KAAM,CAAC,QACP,WACA,SACA,SACA,KACA,OACA,OACA,SACA,aACA,WACA,UACA,aA0GEC,aAAc,SAEhBI,KAAK,EAAIjB,EAAOE,SAAS,CACvBO,OA1GY,CAEdC,OAAQ,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAE7CQ,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAE5CP,YAAa,CAAC,QAAS,QAAS,QAAS,OAAQ,QAAS,QAAS,OAEnEC,KAAM,CAAC,SACP,UACA,WACA,QACA,UACA,SACA,QA6FEC,aAAc,SAEhBM,WAAW,EAAInB,EAAOE,SAAS,CAC7BO,OA5FkB,CACpBC,OAAQ,CACNU,GAAI,OACJC,GAAI,OACJC,SAAU,OACVC,KAAM,OACNC,QAAS,MACTC,UAAW,OACXC,QAAS,MACTC,MAAO,OAEThB,YAAa,CACXS,GAAI,WACJC,GAAI,WACJC,SAAU,WACVC,KAAM,UACNC,QAAS,OACTC,UAAW,SACXC,QAAS,OACTC,MAAO,QAETf,KAAM,CACJQ,GAAI,WACJC,GAAI,WACJC,SAAU,WACVC,KAAM,UACNC,QAAS,OACTC,UAAW,SACXC,QAAS,OACTC,MAAO,SAgEPd,aAAc,OACde,iBA7D4B,CAC9BlB,OAAQ,CACNU,GAAI,OACJC,GAAI,OACJC,SAAU,OACVC,KAAM,OACNC,QAAS,MACTC,UAAW,OACXC,QAAS,MACTC,MAAO,OAEThB,YAAa,CACXS,GAAI,WACJC,GAAI,WACJC,SAAU,WACVC,KAAM,UACNC,QAAS,OACTC,UAAW,SACXC,QAAS,OACTC,MAAO,QAETf,KAAM,CACJQ,GAAI,WACJC,GAAI,WACJC,SAAU,WACVC,KAAM,UACNC,QAAS,OACTC,UAAW,SACXC,QAAS,OACTC,MAAO,SAiCPE,uBAAwB,UAI5BhC,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/ta/_lib/localize/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// Ref: https://www.unicode.org/cldr/charts/32/summary/ta.html\nvar eraValues = {\n  narrow: ['கி.மு.', 'கி.பி.'],\n  abbreviated: ['கி.மு.', 'கி.பி.'],\n  // CLDR #1624, #1626\n  wide: ['கிறிஸ்துவுக்கு முன்', 'அன்னோ டோமினி'] // CLDR #1620, #1622\n\n};\nvar quarterValues = {\n  // CLDR #1644 - #1647\n  narrow: ['1', '2', '3', '4'],\n  // CLDR #1636 - #1639\n  abbreviated: ['காலா.1', 'காலா.2', 'காலா.3', 'காலா.4'],\n  // CLDR #1628 - #1631\n  wide: ['ஒன்றாம் காலாண்டு', 'இரண்டாம் காலாண்டு', 'மூன்றாம் காலாண்டு', 'நான்காம் காலாண்டு']\n};\nvar monthValues = {\n  // CLDR #700 - #711\n  narrow: ['ஜ', 'பி', 'மா', 'ஏ', 'மே', 'ஜூ', 'ஜூ', 'ஆ', 'செ', 'அ', 'ந', 'டி'],\n  // CLDR #1676 - #1687\n  abbreviated: ['ஜன.', 'பிப்.', 'மார்.', 'ஏப்.', 'மே', 'ஜூன்', 'ஜூலை', 'ஆக.', 'செப்.', 'அக்.', 'நவ.', 'டிச.'],\n  // CLDR #1652 - #1663\n  wide: ['ஜனவரி', // January\n  'பிப்ரவரி', // February\n  'மார்ச்', // March\n  'ஏப்ரல்', // April\n  'மே', // May\n  'ஜூன்', // June\n  'ஜூலை', // July\n  'ஆகஸ்ட்', // August\n  'செப்டம்பர்', // September\n  'அக்டோபர்', // October\n  'நவம்பர்', // November\n  'டிசம்பர்' // December\n  ]\n};\nvar dayValues = {\n  // CLDR #1766 - #1772\n  narrow: ['ஞா', 'தி', 'செ', 'பு', 'வி', 'வெ', 'ச'],\n  // CLDR #1752 - #1758\n  short: ['ஞா', 'தி', 'செ', 'பு', 'வி', 'வெ', 'ச'],\n  // CLDR #1738 - #1744\n  abbreviated: ['ஞாயி.', 'திங்.', 'செவ்.', 'புத.', 'வியா.', 'வெள்.', 'சனி'],\n  // CLDR #1724 - #1730\n  wide: ['ஞாயிறு', // Sunday\n  'திங்கள்', // Monday\n  'செவ்வாய்', // Tuesday\n  'புதன்', // Wednesday\n  'வியாழன்', // Thursday\n  'வெள்ளி', // Friday\n  'சனி' // Saturday\n  ]\n}; // CLDR #1780 - #1845\n\nvar dayPeriodValues = {\n  narrow: {\n    am: 'மு.ப',\n    pm: 'பி.ப',\n    midnight: 'நள்.',\n    noon: 'நண்.',\n    morning: 'கா.',\n    afternoon: 'மதி.',\n    evening: 'மா.',\n    night: 'இர.'\n  },\n  abbreviated: {\n    am: 'முற்பகல்',\n    pm: 'பிற்பகல்',\n    midnight: 'நள்ளிரவு',\n    noon: 'நண்பகல்',\n    morning: 'காலை',\n    afternoon: 'மதியம்',\n    evening: 'மாலை',\n    night: 'இரவு'\n  },\n  wide: {\n    am: 'முற்பகல்',\n    pm: 'பிற்பகல்',\n    midnight: 'நள்ளிரவு',\n    noon: 'நண்பகல்',\n    morning: 'காலை',\n    afternoon: 'மதியம்',\n    evening: 'மாலை',\n    night: 'இரவு'\n  }\n}; // CLDR #1780 - #1845\n\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'மு.ப',\n    pm: 'பி.ப',\n    midnight: 'நள்.',\n    noon: 'நண்.',\n    morning: 'கா.',\n    afternoon: 'மதி.',\n    evening: 'மா.',\n    night: 'இர.'\n  },\n  abbreviated: {\n    am: 'முற்பகல்',\n    pm: 'பிற்பகல்',\n    midnight: 'நள்ளிரவு',\n    noon: 'நண்பகல்',\n    morning: 'காலை',\n    afternoon: 'மதியம்',\n    evening: 'மாலை',\n    night: 'இரவு'\n  },\n  wide: {\n    am: 'முற்பகல்',\n    pm: 'பிற்பகல்',\n    midnight: 'நள்ளிரவு',\n    noon: 'நண்பகல்',\n    morning: 'காலை',\n    afternoon: 'மதியம்',\n    evening: 'மாலை',\n    night: 'இரவு'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "_options", "String", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingValues", "defaultFormattingWidth", "module"], "sourceRoot": ""}
"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[27062,66147,86864],{711:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"a second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"a minute",other:"{{count}} minutes"},aboutXHours:{one:"about an hour",other:"about {{count}} hours"},xHours:{one:"an hour",other:"{{count}} hours"},xDays:{one:"a day",other:"{{count}} days"},aboutXWeeks:{one:"about a week",other:"about {{count}} weeks"},xWeeks:{one:"a week",other:"{{count}} weeks"},aboutXMonths:{one:"about a month",other:"about {{count}} months"},xMonths:{one:"a month",other:"{{count}} months"},aboutXYears:{one:"about a year",other:"about {{count}} years"},xYears:{one:"a year",other:"{{count}} years"},overXYears:{one:"over a year",other:"over {{count}} years"},almostXYears:{one:"almost a year",other:"almost {{count}} years"}},a=function(e,t,a){var u,n=o[e];return u="string"==typeof n?n:1===t?n.one:n.other.replace("{{count}}",t.toString()),null!=a&&a.addSuffix?a.comparison&&a.comparison>0?"in "+u:u+" ago":u};t.default=a,e.exports=t.default},49218:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a,u=(a=o(19059))&&a.__esModule?a:{default:a};var n={date:(0,u.default)({formats:{full:"EEEE, MMMM do, yyyy",long:"MMMM do, yyyy",medium:"MMM d, yyyy",short:"yyyy-MM-dd"},defaultWidth:"full"}),time:(0,u.default)({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:(0,u.default)({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})};t.default=n,e.exports=t.default},81597:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=l(o(26376)),u=l(o(12568)),n=l(o(85024)),s=l(o(711)),r=l(o(49218));function l(e){return e&&e.__esModule?e:{default:e}}var d={code:"en-CA",formatDistance:s.default,formatLong:r.default,formatRelative:a.default,localize:u.default,match:n.default,options:{weekStartsOn:0,firstWeekContainsDate:1}};t.default=d,e.exports=t.default}}]);
//# sourceMappingURL=86864.dtale_bundle.js.map
{"version": 3, "file": "83580.dtale_bundle.js", "mappings": "6EAoCAA,EAAOC,QA1BP,SAAaC,GACX,MAAO,CACLC,KAAM,MACNC,kBAAkB,EAClBC,SAAU,CACRC,SAAU,yBACVC,QAAS,+KAIXC,SAAU,CACR,CACEC,UAAW,SACXC,MAAO,IACPC,IAAK,KAEP,CACEF,UAAW,YACXC,MAAO,WACPC,IAAK,IACLC,YAAY,IAIpB,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/csp.js"], "sourcesContent": ["/*\nLanguage: CSP\nDescription: Content Security Policy definition highlighting\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP\n\nvim: ts=2 sw=2 st=2\n*/\n\n/** @type LanguageFn */\nfunction csp(hljs) {\n  return {\n    name: 'CSP',\n    case_insensitive: false,\n    keywords: {\n      $pattern: '[a-zA-Z][a-zA-Z0-9_-]*',\n      keyword: 'base-uri child-src connect-src default-src font-src form-action ' +\n        'frame-ancestors frame-src img-src media-src object-src plugin-types ' +\n        'report-uri sandbox script-src style-src'\n    },\n    contains: [\n      {\n        className: 'string',\n        begin: \"'\",\n        end: \"'\"\n      },\n      {\n        className: 'attribute',\n        begin: '^Content',\n        end: ':',\n        excludeEnd: true\n      }\n    ]\n  };\n}\n\nmodule.exports = csp;\n"], "names": ["module", "exports", "hljs", "name", "case_insensitive", "keywords", "$pattern", "keyword", "contains", "className", "begin", "end", "excludeEnd"], "sourceRoot": ""}
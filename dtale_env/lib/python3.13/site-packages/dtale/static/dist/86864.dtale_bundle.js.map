{"version": 3, "file": "86864.dtale_bundle.js", "mappings": "wGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,qBACLC,MAAO,+BAETC,SAAU,CACRF,IAAK,WACLC,MAAO,qBAETE,YAAa,gBACbC,iBAAkB,CAChBJ,IAAK,qBACLC,MAAO,+BAETI,SAAU,CACRL,IAAK,WACLC,MAAO,qBAETK,YAAa,CACXN,IAAK,gBACLC,MAAO,yBAETM,OAAQ,CACNP,IAAK,UACLC,MAAO,mBAETO,MAAO,CACLR,IAAK,QACLC,MAAO,kBAETQ,YAAa,CACXT,IAAK,eACLC,MAAO,yBAETS,OAAQ,CACNV,IAAK,SACLC,MAAO,mBAETU,aAAc,CACZX,IAAK,gBACLC,MAAO,0BAETW,QAAS,CACPZ,IAAK,UACLC,MAAO,oBAETY,YAAa,CACXb,IAAK,eACLC,MAAO,yBAETa,OAAQ,CACNd,IAAK,SACLC,MAAO,mBAETc,WAAY,CACVf,IAAK,cACLC,MAAO,wBAETe,aAAc,CACZhB,IAAK,gBACLC,MAAO,2BA2BPgB,EAvBiB,SAAwBC,EAAOC,EAAOC,GACzD,IAAIC,EACAC,EAAaxB,EAAqBoB,GAUtC,OAPEG,EADwB,iBAAfC,EACAA,EACU,IAAVH,EACAG,EAAWtB,IAEXsB,EAAWrB,MAAMsB,QAAQ,YAAaJ,EAAMK,YAGnDJ,SAA0CA,EAAQK,UAChDL,EAAQM,YAAcN,EAAQM,WAAa,EACtC,MAAQL,EAERA,EAAS,OAIbA,CACT,EAGAzB,EAAA,QAAkBqB,EAClBU,EAAO/B,QAAUA,EAAQgC,O,kBC7FzBlC,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCiC,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAED,QAASC,GAEvF,IAgCIZ,EAda,CACfe,MAAM,EAAIF,EAAOF,SAAS,CACxBK,QApBc,CAChBC,KAAM,sBACNC,KAAM,gBACNC,OAAQ,cACRC,MAAO,cAiBLC,aAAc,SAEhBC,MAAM,EAAIT,EAAOF,SAAS,CACxBK,QAlBc,CAChBC,KAAM,iBACNC,KAAM,cACNC,OAAQ,YACRC,MAAO,UAeLC,aAAc,SAEhBE,UAAU,EAAIV,EAAOF,SAAS,CAC5BK,QAhBkB,CACpBC,KAAM,yBACNC,KAAM,yBACNC,OAAQ,qBACRC,MAAO,sBAaLC,aAAc,UAIlB1C,EAAA,QAAkBqB,EAClBU,EAAO/B,QAAUA,EAAQgC,O,kBC3CzBlC,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIkC,EAASW,EAAuB,EAAQ,QAExCC,EAAUD,EAAuB,EAAQ,QAEzCE,EAAUF,EAAuB,EAAQ,QAEzCG,EAAUH,EAAuB,EAAQ,MAEzCI,EAAUJ,EAAuB,EAAQ,QAE7C,SAASA,EAAuBZ,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAED,QAASC,EAAO,CAW9F,IAcIZ,EAdS,CACX6B,KAAM,QACNC,eAAgBH,EAAQhB,QACxBoB,WAAYH,EAAQjB,QACpBqB,eAAgBnB,EAAOF,QACvBsB,SAAUR,EAAQd,QAClBuB,MAAOR,EAAQf,QACfR,QAAS,CACPgC,aAAc,EAGdC,sBAAuB,IAI3BzD,EAAA,QAAkBqB,EAClBU,EAAO/B,QAAUA,EAAQgC,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/en-CA/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/en-CA/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/en-CA/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'less than a second',\n    other: 'less than {{count}} seconds'\n  },\n  xSeconds: {\n    one: 'a second',\n    other: '{{count}} seconds'\n  },\n  halfAMinute: 'half a minute',\n  lessThanXMinutes: {\n    one: 'less than a minute',\n    other: 'less than {{count}} minutes'\n  },\n  xMinutes: {\n    one: 'a minute',\n    other: '{{count}} minutes'\n  },\n  aboutXHours: {\n    one: 'about an hour',\n    other: 'about {{count}} hours'\n  },\n  xHours: {\n    one: 'an hour',\n    other: '{{count}} hours'\n  },\n  xDays: {\n    one: 'a day',\n    other: '{{count}} days'\n  },\n  aboutXWeeks: {\n    one: 'about a week',\n    other: 'about {{count}} weeks'\n  },\n  xWeeks: {\n    one: 'a week',\n    other: '{{count}} weeks'\n  },\n  aboutXMonths: {\n    one: 'about a month',\n    other: 'about {{count}} months'\n  },\n  xMonths: {\n    one: 'a month',\n    other: '{{count}} months'\n  },\n  aboutXYears: {\n    one: 'about a year',\n    other: 'about {{count}} years'\n  },\n  xYears: {\n    one: 'a year',\n    other: '{{count}} years'\n  },\n  overXYears: {\n    one: 'over a year',\n    other: 'over {{count}} years'\n  },\n  almostXYears: {\n    one: 'almost a year',\n    other: 'almost {{count}} years'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'in ' + result;\n    } else {\n      return result + ' ago';\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'EEEE, MMMM do, yyyy',\n  long: 'MMMM do, yyyy',\n  medium: 'MMM d, yyyy',\n  short: 'yyyy-MM-dd'\n};\nvar timeFormats = {\n  full: 'h:mm:ss a zzzz',\n  long: 'h:mm:ss a z',\n  medium: 'h:mm:ss a',\n  short: 'h:mm a'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'at' {{time}}\",\n  long: \"{{date}} 'at' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../en-US/_lib/formatRelative/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../en-US/_lib/localize/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"../en-US/_lib/match/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary English locale (Canada).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@markowsiak]{@link https://github.com/mark<PERSON>k}\n * <AUTHOR> [@mimperatore]{@link https://github.com/mimperator<PERSON>}\n */\nvar locale = {\n  code: 'en-CA',\n  formatDistance: _index4.default,\n  formatLong: _index5.default,\n  formatRelative: _index.default,\n  localize: _index2.default,\n  match: _index3.default,\n  options: {\n    weekStartsOn: 0\n    /* Sunday */\n    ,\n    firstWeekContainsDate: 1\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_default", "token", "count", "options", "result", "tokenValue", "replace", "toString", "addSuffix", "comparison", "module", "default", "obj", "_index", "__esModule", "date", "formats", "full", "long", "medium", "short", "defaultWidth", "time", "dateTime", "_interopRequireDefault", "_index2", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "weekStartsOn", "firstWeekContainsDate"], "sourceRoot": ""}
{"version": 3, "file": "92871.dtale_bundle.js", "mappings": "6EAAA,MAAMA,EAAW,2BACXC,EAAW,CACf,KACA,KACA,KACA,KACA,MACA,QACA,UACA,MACA,MACA,WACA,KACA,SACA,OACA,OACA,QACA,QACA,aACA,OACA,QACA,OACA,UACA,MACA,SACA,WACA,SACA,SACA,MACA,QACA,QACA,QAIA,WACA,QACA,QACA,SACA,SACA,OACA,SACA,WAEIC,EAAW,CACf,OACA,QACA,OACA,YACA,MACA,YAoFIC,EAAY,GAAGC,OAlCI,CACvB,cACA,aACA,gBACA,eAEA,UACA,UAEA,OACA,WACA,QACA,aACA,WACA,YACA,qBACA,YACA,qBACA,SACA,YAGyB,CACzB,YACA,OACA,QACA,UACA,SACA,WACA,eACA,SACA,UA9EY,CACZ,OACA,WACA,SACA,OACA,OACA,SACA,SACA,SACA,WACA,UACA,QACA,SACA,MACA,MACA,UACA,UACA,QACA,UACA,OACA,UACA,eACA,aACA,aACA,YACA,cACA,cACA,eACA,QACA,aACA,oBACA,cACA,gBACA,iBACA,UAGkB,CAClB,YACA,gBACA,aACA,iBACA,cACA,YACA,aAgEF,SAASC,EAAUC,GACjB,OAAOF,EAAO,MAAOE,EAAI,IAC3B,CAMA,SAASF,KAAUG,GAEjB,OADeA,EAAKC,IAAKC,IAAMC,OApBjBJ,EAoBwBG,GAlBpB,iBAAPH,EAAwBA,EAE5BA,EAAGI,OAHM,KADlB,IAAgBJ,IAoB4BK,KAAK,GAEjD,CA4gBAC,EAAOC,QAnFP,SAAoBC,GAClB,MAAMC,EAAaf,EAoCbgB,EAAa,CACjBC,SAAUjB,EACVkB,QAASjB,EAASG,OAfS,CAC3B,OACA,YACA,UACA,YACA,SACA,UACA,YACA,aACA,UACA,WACA,aAKAe,QAASjB,EACTkB,SAAUjB,EAAUC,OA3BR,CACZ,MACA,OACA,SACA,UACA,SACA,SACA,QACA,UAqBIiB,EAAY,CAChBC,UAAW,OACXC,MAAO,IAAMR,GAGTS,EAAW,CAACC,EAAMC,EAAOC,KAC7B,MAAMC,EAAOH,EAAKI,SAASC,UAAUC,GAAKA,EAAEL,QAAUA,GACtD,IAAc,IAAVE,EAAe,MAAM,IAAII,MAAM,gCACnCP,EAAKI,SAASI,OAAOL,EAAM,EAAGD,IAG1BO,EAreR,SAAoBpB,GAQlB,MAMMC,EAAaf,EACbmC,EACG,KADHA,EAEC,MAEDC,EAAU,CACdb,MAAO,sBACPc,IAAK,4BAKLC,kBAAmB,CAACC,EAAOC,KACzB,MAAMC,EAAkBF,EAAM,GAAGG,OAASH,EAAMI,MAC1CC,EAAWL,EAAMM,MAAMJ,GAIZ,MAAbG,EAMa,MAAbA,IA9Bc,EAACL,GAASO,YAC9B,MAAMC,EAAM,KAAOR,EAAM,GAAGS,MAAM,GAElC,OAAgB,IADJT,EAAMM,MAAMI,QAAQF,EAAKD,IA+B5BI,CAAcX,EAAO,CAAEO,MAAOL,KACjCD,EAASW,eATXX,EAASW,gBAcTnC,EAAa,CACjBC,SAAUjB,EACVkB,QAASjB,EACTkB,QAASjB,EACTkB,SAAUjB,GAINiD,EAAgB,kBAChBC,EAAO,OAAOD,KAGdE,EAAiB,sCACjBC,EAAS,CACbjC,UAAW,SACXkC,SAAU,CAER,CAAEjC,MAAO,QAAQ+B,OAAoBD,aAAgBA,gBACtCD,SACf,CAAE7B,MAAO,OAAO+B,UAAuBD,gBAAmBA,SAG1D,CAAE9B,MAAO,8BAGT,CAAEA,MAAO,4CACT,CAAEA,MAAO,gCACT,CAAEA,MAAO,gCAIT,CAAEA,MAAO,oBAEXkC,UAAW,GAGPC,EAAQ,CACZpC,UAAW,QACXC,MAAO,SACPc,IAAK,MACLsB,SAAU3C,EACVa,SAAU,IAEN+B,EAAgB,CACpBrC,MAAO,QACPc,IAAK,GACLwB,OAAQ,CACNxB,IAAK,IACLyB,WAAW,EACXjC,SAAU,CACRf,EAAKiD,iBACLL,GAEFM,YAAa,QAGXC,EAAe,CACnB1C,MAAO,OACPc,IAAK,GACLwB,OAAQ,CACNxB,IAAK,IACLyB,WAAW,EACXjC,SAAU,CACRf,EAAKiD,iBACLL,GAEFM,YAAa,QAGXE,EAAkB,CACtB5C,UAAW,SACXC,MAAO,IACPc,IAAK,IACLR,SAAU,CACRf,EAAKiD,iBACLL,IAoCES,EAAU,CACd7C,UAAW,UACXkC,SAAU,CAnCU1C,EAAKqD,QACzB,eACA,OACA,CACEV,UAAW,EACX5B,SAAU,CACR,CACEP,UAAW,SACXC,MAAO,aACPM,SAAU,CACR,CACEP,UAAW,OACXC,MAAO,MACPc,IAAK,MACLoB,UAAW,GAEb,CACEnC,UAAW,WACXC,MAAOR,EAAa,gBACpBqD,YAAY,EACZX,UAAW,GAIb,CACElC,MAAO,cACPkC,UAAW,QAWnB3C,EAAKuD,qBACLvD,EAAKwD,sBAGHC,EAAkB,CACtBzD,EAAK0D,iBACL1D,EAAK2D,kBACLb,EACAK,EACAC,EACAX,EACAzC,EAAK4D,aAEPhB,EAAM7B,SAAW0C,EACdnE,OAAO,CAGNmB,MAAO,KACPc,IAAK,KACLsB,SAAU3C,EACVa,SAAU,CACR,QACAzB,OAAOmE,KAEb,MAAMI,EAAqB,GAAGvE,OAAO+D,EAAST,EAAM7B,UAC9C+C,EAAkBD,EAAmBvE,OAAO,CAEhD,CACEmB,MAAO,KACPc,IAAK,KACLsB,SAAU3C,EACVa,SAAU,CAAC,QAAQzB,OAAOuE,MAGxBE,EAAS,CACbvD,UAAW,SACXC,MAAO,KACPc,IAAK,KACLyC,cAAc,EACdC,YAAY,EACZpB,SAAU3C,EACVa,SAAU+C,GAGZ,MAAO,CACLI,KAAM,aACNC,QAAS,CAAC,KAAM,MAAO,MAAO,OAC9BtB,SAAU3C,EAEVH,QAAS,CAAE+D,mBACXM,QAAS,eACTrD,SAAU,CACRf,EAAKqE,QAAQ,CACXzD,MAAO,UACP0D,OAAQ,OACR3B,UAAW,IAEb,CACE/B,MAAO,aACPJ,UAAW,OACXmC,UAAW,GACXlC,MAAO,gCAETT,EAAK0D,iBACL1D,EAAK2D,kBACLb,EACAK,EACAC,EACAC,EACAZ,EACA,CACEhC,MAAOnB,EAAO,YAWZC,EAAUD,EAGR,6CACAW,EAAa,WACjB0C,UAAW,EACX5B,SAAU,CACR,CACEP,UAAW,OACXC,MAAOR,EAAaV,EAAU,SAC9BoD,UAAW,KAIjB,CACElC,MAAO,IAAMT,EAAKuE,eAAiB,kCACnC1B,SAAU,oBACV9B,SAAU,CACRsC,EACArD,EAAK4D,YACL,CACEpD,UAAW,WAIXC,MAAO,2DAMET,EAAKwE,oBAAsB,UACpCC,aAAa,EACblD,IAAK,SACLR,SAAU,CACR,CACEP,UAAW,SACXkC,SAAU,CACR,CACEjC,MAAOT,EAAKwE,oBACZ7B,UAAW,GAEb,CACEnC,UAAW,KACXC,MAAO,UACPiE,MAAM,GAER,CACEjE,MAAO,KACPc,IAAK,KACLyC,cAAc,EACdC,YAAY,EACZpB,SAAU3C,EACVa,SAAU+C,OAMpB,CACErD,MAAO,IAAKkC,UAAW,GAEzB,CACEnC,UAAW,GACXC,MAAO,KACPc,IAAK,MACLmD,MAAM,GAER,CACEhC,SAAU,CACR,CAAEjC,MAAOY,EAAgBE,IAAKF,GAC9B,CACEZ,MAAOa,EAAQb,MAGf,WAAYa,EAAQE,kBACpBD,IAAKD,EAAQC,MAGjB2B,YAAa,MACbnC,SAAU,CACR,CACEN,MAAOa,EAAQb,MACfc,IAAKD,EAAQC,IACbmD,MAAM,EACN3D,SAAU,CAAC,YAKnB4B,UAAW,GAEb,CACEnC,UAAW,WACXmE,cAAe,WACfpD,IAAK,OACL0C,YAAY,EACZpB,SAAU3C,EACVa,SAAU,CACR,OACAf,EAAK4E,QAAQ5E,EAAK6E,WAAY,CAAEpE,MAAOR,IACvC8D,GAEFK,QAAS,KAEX,CAGEO,cAAe,6BAEjB,CACEnE,UAAW,WAIXC,MAAOT,EAAKwE,oBAALxE,gEAQPyE,aAAY,EACZ1D,SAAU,CACRgD,EACA/D,EAAK4E,QAAQ5E,EAAK6E,WAAY,CAAEpE,MAAOR,MAM3C,CACEyC,SAAU,CACR,CAAEjC,MAAO,MAAQR,GACjB,CAAEQ,MAAO,MAAQR,IAEnB0C,UAAW,GAEb,CACEnC,UAAW,QACXmE,cAAe,QACfpD,IAAK,QACL0C,YAAY,EACZG,QAAS,UACTrD,SAAU,CACR,CAAE4D,cAAe,WACjB3E,EAAK8E,wBAGT,CACErE,MAAO,oBACPc,IAAK,OACL0C,YAAY,EACZlD,SAAU,CACRf,EAAK4E,QAAQ5E,EAAK6E,WAAY,CAAEpE,MAAOR,IACvC,OACA8D,IAGJ,CACEtD,MAAO,mBAAqBR,EAAa,OACzCsB,IAAK,KACLsB,SAAU,UACV9B,SAAU,CACRf,EAAK4E,QAAQ5E,EAAK6E,WAAY,CAAEpE,MAAOR,IACvC,CAAEQ,MAAO,QACTsD,IAGJ,CACEtD,MAAO,WAIf,CAkEqBsE,CAAW/E,GA0B9B,OAtBAgF,OAAOC,OAAO7D,EAAWyB,SAAU3C,GAEnCkB,EAAWrB,QAAQ+D,gBAAgBoB,KAAK3E,GACxCa,EAAWL,SAAWK,EAAWL,SAASzB,OAAO,CAC/CiB,EA5DgB,CAChBoE,cAAe,YAAapD,IAAK,KAAM0C,YAAY,GAEnC,CAChBU,cAAe,YAAapD,IAAK,KAAM0C,YAAY,EACnDpB,SAAU,uBA6DZnC,EAASU,EAAY,UAAWpB,EAAKqE,WAErC3D,EAASU,EAAY,aA7DF,CACjBZ,UAAW,OACXmC,UAAW,GACXlC,MAAO,2BA4DmBW,EAAWL,SAASoE,KAAKlE,GAAqB,aAAhBA,EAAET,WACxCmC,UAAY,EAEhCqC,OAAOC,OAAO7D,EAAY,CACxB8C,KAAM,aACNC,QAAS,CAAC,KAAM,SAGX/C,CACT,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/typescript.js"], "sourcesContent": ["const IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\nconst KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // J<PERSON> handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\nconst TYPES = [\n  \"Intl\",\n  \"DataView\",\n  \"Number\",\n  \"Math\",\n  \"Date\",\n  \"String\",\n  \"RegExp\",\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Error\",\n  \"Symbol\",\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  \"Proxy\",\n  \"Reflect\",\n  \"JSON\",\n  \"Promise\",\n  \"Float64Array\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Int8Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"Float32Array\",\n  \"Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"ArrayBuffer\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  \"BigInt\"\n];\n\nconst ERROR_TYPES = [\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  BUILT_IN_VARIABLES,\n  TYPES,\n  ERROR_TYPES\n);\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: JavaScript\nDescription: JavaScript (JS) is a lightweight, interpreted, or just-in-time compiled programming language with first-class functions.\nCategory: common, scripting\nWebsite: https://developer.mozilla.org/en-US/docs/Web/JavaScript\n*/\n\n/** @type LanguageFn */\nfunction javascript(hljs) {\n  /**\n   * Takes a string like \"<Booger\" and checks to see\n   * if we can find a matching \"</Booger\" later in the\n   * content.\n   * @param {RegExpMatchArray} match\n   * @param {{after:number}} param1\n   */\n  const hasClosingTag = (match, { after }) => {\n    const tag = \"</\" + match[0].slice(1);\n    const pos = match.input.indexOf(tag, after);\n    return pos !== -1;\n  };\n\n  const IDENT_RE$1 = IDENT_RE;\n  const FRAGMENT = {\n    begin: '<>',\n    end: '</>'\n  };\n  const XML_TAG = {\n    begin: /<[A-Za-z0-9\\\\._:-]+/,\n    end: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\n    /**\n     * @param {RegExpMatchArray} match\n     * @param {CallbackResponse} response\n     */\n    isTrulyOpeningTag: (match, response) => {\n      const afterMatchIndex = match[0].length + match.index;\n      const nextChar = match.input[afterMatchIndex];\n      // nested type?\n      // HTML should not include another raw `<` inside a tag\n      // But a type might: `<Array<Array<number>>`, etc.\n      if (nextChar === \"<\") {\n        response.ignoreMatch();\n        return;\n      }\n      // <something>\n      // This is now either a tag or a type.\n      if (nextChar === \">\") {\n        // if we cannot find a matching closing tag, then we\n        // will ignore it\n        if (!hasClosingTag(match, { after: afterMatchIndex })) {\n          response.ignoreMatch();\n        }\n      }\n    }\n  };\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_INS\n  };\n\n  // https://tc39.es/ecma262/#sec-literals-numeric-literals\n  const decimalDigits = '[0-9](_?[0-9])*';\n  const frac = `\\\\.(${decimalDigits})`;\n  // DecimalIntegerLiteral, including Annex B NonOctalDecimalIntegerLiteral\n  // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n  const decimalInteger = `0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*`;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      // DecimalLiteral\n      { begin: `(\\\\b(${decimalInteger})((${frac})|\\\\.)?|(${frac}))` +\n        `[eE][+-]?(${decimalDigits})\\\\b` },\n      { begin: `\\\\b(${decimalInteger})\\\\b((${frac})\\\\b|\\\\.)?|(${frac})\\\\b` },\n\n      // DecimalBigIntegerLiteral\n      { begin: `\\\\b(0|[1-9](_?[0-9])*)n\\\\b` },\n\n      // NonDecimalIntegerLiteral\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\" },\n\n      // LegacyOctalIntegerLiteral (does not include underscore separators)\n      // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n      { begin: \"\\\\b0[0-7]+n?\\\\b\" },\n    ],\n    relevance: 0\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS$1,\n    contains: [] // defined later\n  };\n  const HTML_TEMPLATE = {\n    begin: 'html`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'xml'\n    }\n  };\n  const CSS_TEMPLATE = {\n    begin: 'css`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'css'\n    }\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  const JSDOC_COMMENT = hljs.COMMENT(\n    /\\/\\*\\*(?!\\/)/,\n    '\\\\*/',\n    {\n      relevance: 0,\n      contains: [\n        {\n          className: 'doctag',\n          begin: '@[A-Za-z]+',\n          contains: [\n            {\n              className: 'type',\n              begin: '\\\\{',\n              end: '\\\\}',\n              relevance: 0\n            },\n            {\n              className: 'variable',\n              begin: IDENT_RE$1 + '(?=\\\\s*(-)|$)',\n              endsParent: true,\n              relevance: 0\n            },\n            // eat spaces (not newlines) so we can find\n            // types or variables\n            {\n              begin: /(?=[^\\n])\\s/,\n              relevance: 0\n            }\n          ]\n        }\n      ]\n    }\n  );\n  const COMMENT = {\n    className: \"comment\",\n    variants: [\n      JSDOC_COMMENT,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_LINE_COMMENT_MODE\n    ]\n  };\n  const SUBST_INTERNALS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    HTML_TEMPLATE,\n    CSS_TEMPLATE,\n    TEMPLATE_STRING,\n    NUMBER,\n    hljs.REGEXP_MODE\n  ];\n  SUBST.contains = SUBST_INTERNALS\n    .concat({\n      // we need to pair up {} inside our subst to prevent\n      // it from ending too early by matching another }\n      begin: /\\{/,\n      end: /\\}/,\n      keywords: KEYWORDS$1,\n      contains: [\n        \"self\"\n      ].concat(SUBST_INTERNALS)\n    });\n  const SUBST_AND_COMMENTS = [].concat(COMMENT, SUBST.contains);\n  const PARAMS_CONTAINS = SUBST_AND_COMMENTS.concat([\n    // eat recursive parens in sub expressions\n    {\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS$1,\n      contains: [\"self\"].concat(SUBST_AND_COMMENTS)\n    }\n  ]);\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    keywords: KEYWORDS$1,\n    contains: PARAMS_CONTAINS\n  };\n\n  return {\n    name: 'Javascript',\n    aliases: ['js', 'jsx', 'mjs', 'cjs'],\n    keywords: KEYWORDS$1,\n    // this will be extended by TypeScript\n    exports: { PARAMS_CONTAINS },\n    illegal: /#(?![$_A-z])/,\n    contains: [\n      hljs.SHEBANG({\n        label: \"shebang\",\n        binary: \"node\",\n        relevance: 5\n      }),\n      {\n        label: \"use_strict\",\n        className: 'meta',\n        relevance: 10,\n        begin: /^\\s*['\"]use (strict|asm)['\"]/\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      HTML_TEMPLATE,\n      CSS_TEMPLATE,\n      TEMPLATE_STRING,\n      COMMENT,\n      NUMBER,\n      { // object attr container\n        begin: concat(/[{,\\n]\\s*/,\n          // we need to look ahead to make sure that we actually have an\n          // attribute coming up so we don't steal a comma from a potential\n          // \"value\" container\n          //\n          // NOTE: this might not work how you think.  We don't actually always\n          // enter this mode and stay.  Instead it might merely match `,\n          // <comments up next>` and then immediately end after the , because it\n          // fails to find any actual attrs. But this still does the job because\n          // it prevents the value contain rule from grabbing this instead and\n          // prevening this rule from firing when we actually DO have keys.\n          lookahead(concat(\n            // we also need to allow for multiple possible comments inbetween\n            // the first key:value pairing\n            /(((\\/\\/.*$)|(\\/\\*(\\*[^/]|[^*])*\\*\\/))\\s*)*/,\n            IDENT_RE$1 + '\\\\s*:'))),\n        relevance: 0,\n        contains: [\n          {\n            className: 'attr',\n            begin: IDENT_RE$1 + lookahead('\\\\s*:'),\n            relevance: 0\n          }\n        ]\n      },\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n        keywords: 'return throw case',\n        contains: [\n          COMMENT,\n          hljs.REGEXP_MODE,\n          {\n            className: 'function',\n            // we have to count the parens to make sure we actually have the\n            // correct bounding ( ) before the =>.  There could be any number of\n            // sub-expressions inside also surrounded by parens.\n            begin: '(\\\\(' +\n            '[^()]*(\\\\(' +\n            '[^()]*(\\\\(' +\n            '[^()]*' +\n            '\\\\)[^()]*)*' +\n            '\\\\)[^()]*)*' +\n            '\\\\)|' + hljs.UNDERSCORE_IDENT_RE + ')\\\\s*=>',\n            returnBegin: true,\n            end: '\\\\s*=>',\n            contains: [\n              {\n                className: 'params',\n                variants: [\n                  {\n                    begin: hljs.UNDERSCORE_IDENT_RE,\n                    relevance: 0\n                  },\n                  {\n                    className: null,\n                    begin: /\\(\\s*\\)/,\n                    skip: true\n                  },\n                  {\n                    begin: /\\(/,\n                    end: /\\)/,\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    keywords: KEYWORDS$1,\n                    contains: PARAMS_CONTAINS\n                  }\n                ]\n              }\n            ]\n          },\n          { // could be a comma delimited list of params to a function call\n            begin: /,/, relevance: 0\n          },\n          {\n            className: '',\n            begin: /\\s/,\n            end: /\\s*/,\n            skip: true\n          },\n          { // JSX\n            variants: [\n              { begin: FRAGMENT.begin, end: FRAGMENT.end },\n              {\n                begin: XML_TAG.begin,\n                // we carefully check the opening tag to see if it truly\n                // is a tag and not a false positive\n                'on:begin': XML_TAG.isTrulyOpeningTag,\n                end: XML_TAG.end\n              }\n            ],\n            subLanguage: 'xml',\n            contains: [\n              {\n                begin: XML_TAG.begin,\n                end: XML_TAG.end,\n                skip: true,\n                contains: ['self']\n              }\n            ]\n          }\n        ],\n        relevance: 0\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: /[{;]/,\n        excludeEnd: true,\n        keywords: KEYWORDS$1,\n        contains: [\n          'self',\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1 }),\n          PARAMS\n        ],\n        illegal: /%/\n      },\n      {\n        // prevent this from getting swallowed up by function\n        // since they appear \"function like\"\n        beginKeywords: \"while if switch catch for\"\n      },\n      {\n        className: 'function',\n        // we have to count the parens to make sure we actually have the correct\n        // bounding ( ).  There could be any number of sub-expressions inside\n        // also surrounded by parens.\n        begin: hljs.UNDERSCORE_IDENT_RE +\n          '\\\\(' + // first parens\n          '[^()]*(\\\\(' +\n            '[^()]*(\\\\(' +\n              '[^()]*' +\n            '\\\\)[^()]*)*' +\n          '\\\\)[^()]*)*' +\n          '\\\\)\\\\s*\\\\{', // end parens\n        returnBegin:true,\n        contains: [\n          PARAMS,\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1 }),\n        ]\n      },\n      // hack: prevents detection of keywords in some circumstances\n      // .keyword()\n      // $keyword = x\n      {\n        variants: [\n          { begin: '\\\\.' + IDENT_RE$1 },\n          { begin: '\\\\$' + IDENT_RE$1 }\n        ],\n        relevance: 0\n      },\n      { // ES6 class\n        className: 'class',\n        beginKeywords: 'class',\n        end: /[{;=]/,\n        excludeEnd: true,\n        illegal: /[:\"[\\]]/,\n        contains: [\n          { beginKeywords: 'extends' },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        begin: /\\b(?=constructor)/,\n        end: /[{;]/,\n        excludeEnd: true,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1 }),\n          'self',\n          PARAMS\n        ]\n      },\n      {\n        begin: '(get|set)\\\\s+(?=' + IDENT_RE$1 + '\\\\()',\n        end: /\\{/,\n        keywords: \"get set\",\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1 }),\n          { begin: /\\(\\)/ }, // eat to avoid empty params\n          PARAMS\n        ]\n      },\n      {\n        begin: /\\$[(.]/ // relevance booster for a pattern common to JS libs: `$(something)` and `$.something`\n      }\n    ]\n  };\n}\n\n/*\nLanguage: TypeScript\nAuthor: Panu Horsmalahti <<EMAIL>>\nContributors: Ike Ku <<EMAIL>>\nDescription: TypeScript is a strict superset of JavaScript\nWebsite: https://www.typescriptlang.org\nCategory: common, scripting\n*/\n\n/** @type LanguageFn */\nfunction typescript(hljs) {\n  const IDENT_RE$1 = IDENT_RE;\n  const NAMESPACE = {\n    beginKeywords: 'namespace', end: /\\{/, excludeEnd: true\n  };\n  const INTERFACE = {\n    beginKeywords: 'interface', end: /\\{/, excludeEnd: true,\n    keywords: 'interface extends'\n  };\n  const USE_STRICT = {\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use strict['\"]/\n  };\n  const TYPES = [\n    \"any\",\n    \"void\",\n    \"number\",\n    \"boolean\",\n    \"string\",\n    \"object\",\n    \"never\",\n    \"enum\"\n  ];\n  const TS_SPECIFIC_KEYWORDS = [\n    \"type\",\n    \"namespace\",\n    \"typedef\",\n    \"interface\",\n    \"public\",\n    \"private\",\n    \"protected\",\n    \"implements\",\n    \"declare\",\n    \"abstract\",\n    \"readonly\"\n  ];\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS.concat(TS_SPECIFIC_KEYWORDS),\n    literal: LITERALS,\n    built_in: BUILT_INS.concat(TYPES)\n  };\n  const DECORATOR = {\n    className: 'meta',\n    begin: '@' + IDENT_RE$1,\n  };\n\n  const swapMode = (mode, label, replacement) => {\n    const indx = mode.contains.findIndex(m => m.label === label);\n    if (indx === -1) { throw new Error(\"can not find mode to replace\"); }\n    mode.contains.splice(indx, 1, replacement);\n  };\n\n  const tsLanguage = javascript(hljs);\n\n  // this should update anywhere keywords is used since\n  // it will be the same actual JS object\n  Object.assign(tsLanguage.keywords, KEYWORDS$1);\n\n  tsLanguage.exports.PARAMS_CONTAINS.push(DECORATOR);\n  tsLanguage.contains = tsLanguage.contains.concat([\n    DECORATOR,\n    NAMESPACE,\n    INTERFACE,\n  ]);\n\n  // TS gets a simpler shebang rule than JS\n  swapMode(tsLanguage, \"shebang\", hljs.SHEBANG());\n  // JS use strict rule purposely excludes `asm` which makes no sense\n  swapMode(tsLanguage, \"use_strict\", USE_STRICT);\n\n  const functionDeclaration = tsLanguage.contains.find(m => m.className === \"function\");\n  functionDeclaration.relevance = 0; // () => {} is more typical in TypeScript\n\n  Object.assign(tsLanguage, {\n    name: 'TypeScript',\n    aliases: ['ts', 'tsx']\n  });\n\n  return tsLanguage;\n}\n\nmodule.exports = typescript;\n"], "names": ["IDENT_RE", "KEYWORDS", "LITERALS", "BUILT_INS", "concat", "<PERSON><PERSON><PERSON>", "re", "args", "map", "x", "source", "join", "module", "exports", "hljs", "IDENT_RE$1", "KEYWORDS$1", "$pattern", "keyword", "literal", "built_in", "DECORATOR", "className", "begin", "swapMode", "mode", "label", "replacement", "indx", "contains", "findIndex", "m", "Error", "splice", "tsLanguage", "FRAGMENT", "XML_TAG", "end", "isTrulyOpeningTag", "match", "response", "afterMatchIndex", "length", "index", "nextChar", "input", "after", "tag", "slice", "indexOf", "hasClosingTag", "ignoreMatch", "decimalDigits", "frac", "decimalInteger", "NUMBER", "variants", "relevance", "SUBST", "keywords", "HTML_TEMPLATE", "starts", "returnEnd", "BACKSLASH_ESCAPE", "subLanguage", "CSS_TEMPLATE", "TEMPLATE_STRING", "COMMENT", "endsParent", "C_BLOCK_COMMENT_MODE", "C_LINE_COMMENT_MODE", "SUBST_INTERNALS", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "REGEXP_MODE", "SUBST_AND_COMMENTS", "PARAMS_CONTAINS", "PARAMS", "excludeBegin", "excludeEnd", "name", "aliases", "illegal", "SHEBANG", "binary", "RE_STARTERS_RE", "UNDERSCORE_IDENT_RE", "returnBegin", "skip", "beginKeywords", "inherit", "TITLE_MODE", "UNDERSCORE_TITLE_MODE", "javascript", "Object", "assign", "push", "find"], "sourceRoot": ""}
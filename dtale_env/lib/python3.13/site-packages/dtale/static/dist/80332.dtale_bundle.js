"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[80332],{89161:(e,t,d)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l,a=(l=d(19059))&&l.__esModule?l:{default:l};var u={date:(0,a.default)({formats:{full:"d MMMM, y, EEEE",long:"d MMMM, y",medium:"d MMM, y",short:"dd.MM.yyyy"},defaultWidth:"full"}),time:(0,a.default)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,a.default)({formats:{full:"{{date}} 'ժ․'{{time}}",long:"{{date}} 'ժ․'{{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})};t.default=u,e.exports=t.default}}]);
//# sourceMappingURL=80332.dtale_bundle.js.map
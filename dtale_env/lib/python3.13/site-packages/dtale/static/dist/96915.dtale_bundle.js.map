{"version": 3, "file": "96915.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAAII,EAAqB,CAAC,aAAc,aAAc,aAAc,aAAc,aAAc,SAAU,WAO1G,SAASC,EAASC,GAEhB,MAAO,IADOF,EAAmBE,GACV,gBACzB,CAOA,IAAIC,EAAuB,CACzBC,SAAU,SAAkBC,EAAMC,EAAUC,GAC1C,IAAIL,EAAMG,EAAKG,YAEf,OAAI,EAAIX,EAAOE,SAASM,EAAMC,EAAUC,GAC/BN,EAASC,GApBtB,SAAmBA,GAEjB,MAAO,UADOF,EAAmBE,GACJ,gBAC/B,CAmBaO,CAAUP,EAErB,EACAQ,UAAW,sBACXC,MAAO,uBACPC,SAAU,uBACVC,SAAU,SAAkBR,EAAMC,EAAUC,GAC1C,IAAIL,EAAMG,EAAKG,YAEf,OAAI,EAAIX,EAAOE,SAASM,EAAMC,EAAUC,GAC/BN,EAASC,GAtBtB,SAAmBA,GAEjB,MAAO,WADOF,EAAmBE,GACH,gBAChC,CAqBaY,CAAUZ,EAErB,EACAa,MAAO,KAaLC,EAViB,SAAwBC,EAAOZ,EAAMC,EAAUC,GAClE,IAAIW,EAASf,EAAqBc,GAElC,MAAsB,mBAAXC,EACFA,EAAOb,EAAMC,EAAUC,GAGzBW,CACT,EAGAxB,EAAA,QAAkBsB,EAClBG,EAAOzB,QAAUA,EAAQK,O,kBC/DzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,QAQA,SAAuB0B,EAAeC,EAAgBd,IACpD,EAAIV,EAAOE,SAAS,EAAGuB,WACvB,IAAIC,GAAsB,EAAIC,EAAQzB,SAASqB,EAAeb,GAC1DkB,GAAuB,EAAID,EAAQzB,SAASsB,EAAgBd,GAChE,OAAOgB,EAAoBG,YAAcD,EAAqBC,SAChE,EAXA,IAAI7B,EAAS8B,EAAuB,EAAQ,QAExCH,EAAUG,EAAuB,EAAQ,QAE7C,SAASA,EAAuB/B,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAS9FuB,EAAOzB,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/kk/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/_lib/isSameUTCWeek/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../../_lib/isSameUTCWeek/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar accusativeWeekdays = ['жексенбіде', 'дүйсенбіде', 'сейсенбіде', 'сәрсенбіде', 'бейсенбіде', 'жұмада', 'сенбіде'];\n\nfunction _lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'өткен \" + weekday + \" сағат' p'-де'\";\n}\n\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'\" + weekday + \" сағат' p'-де'\";\n}\n\nfunction _nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'келесі \" + weekday + \" сағат' p'-де'\";\n}\n\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n\n    if ((0, _index.default)(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'кеше сағат' p'-де'\",\n  today: \"'бүгін сағат' p'-де'\",\n  tomorrow: \"'ертең сағат' p'-де'\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n\n    if ((0, _index.default)(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = isSameUTCWeek;\n\nvar _index = _interopRequireDefault(require(\"../requiredArgs/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../startOfUTCWeek/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction isSameUTCWeek(dirtyDateLeft, dirtyDateRight, options) {\n  (0, _index.default)(2, arguments);\n  var dateLeftStartOfWeek = (0, _index2.default)(dirtyDateLeft, options);\n  var dateRightStartOfWeek = (0, _index2.default)(dirtyDateRight, options);\n  return dateLeftStartOfWeek.getTime() === dateRightStartOfWeek.getTime();\n}\n\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "accusativeWeekdays", "thisWeek", "day", "formatRelativeLocale", "lastWeek", "date", "baseDate", "options", "getUTCDay", "_lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "_nextWeek", "other", "_default", "token", "format", "module", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeftStartOfWeek", "_index2", "dateRightStartOfWeek", "getTime", "_interopRequireDefault"], "sourceRoot": ""}
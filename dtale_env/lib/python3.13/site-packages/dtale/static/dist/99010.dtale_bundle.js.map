{"version": 3, "file": "99010.dtale_bundle.js", "mappings": "8FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,iBAAkB,0BAClBC,SAAU,mBACVC,YAAa,YACbC,iBAAkB,wBAClBC,SAAU,iBACVC,YAAa,wBACbC,OAAQ,iBACRC,MAAO,iBACPC,YAAa,2BACbC,OAAQ,oBACRC,aAAc,sBACdC,QAAS,eACTC,YAAa,yBACbC,OAAQ,kBACRC,WAAY,sBACZC,aAAc,uBAsBZC,EAnBiB,SAAwBC,EAAOC,EAAOC,GACzD,IACIC,EADarB,EAAqBkB,GAOtC,MAJqB,iBAAVC,IACTE,EAASA,EAAOC,QAAQ,YAAaH,EAAMI,aAGzCH,SAA0CA,EAAQI,UAChDJ,EAAQK,YAAcL,EAAQK,WAAa,EACtC,eAAiBJ,EAEjBA,EAAS,MAIbA,CACT,EAGAvB,EAAA,QAAkBmB,EAClBS,EAAO5B,QAAUA,EAAQ6B,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/km/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: 'តិចជាង {{count}} វិនាទី',\n  xSeconds: '{{count}} វិនាទី',\n  halfAMinute: 'កន្លះនាទី',\n  lessThanXMinutes: 'តិចជាង {{count}} នាទី',\n  xMinutes: '{{count}} នាទី',\n  aboutXHours: 'ប្រហែល {{count}} ម៉ោង',\n  xHours: '{{count}} ម៉ោង',\n  xDays: '{{count}} ថ្ងៃ',\n  aboutXWeeks: 'ប្រហែល {{count}} សប្តាហ៍',\n  xWeeks: '{{count}} សប្តាហ៍',\n  aboutXMonths: 'ប្រហែល {{count}} ខែ',\n  xMonths: '{{count}} ខែ',\n  aboutXYears: 'ប្រហែល {{count}} ឆ្នាំ',\n  xYears: '{{count}} ឆ្នាំ',\n  overXYears: 'ជាង {{count}} ឆ្នាំ',\n  almostXYears: 'ជិត {{count}} ឆ្នាំ'\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var tokenValue = formatDistanceLocale[token];\n  var result = tokenValue;\n\n  if (typeof count === 'number') {\n    result = result.replace('{{count}}', count.toString());\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'ក្នុងរយៈពេល ' + result;\n    } else {\n      return result + 'មុន';\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_default", "token", "count", "options", "result", "replace", "toString", "addSuffix", "comparison", "module", "default"], "sourceRoot": ""}
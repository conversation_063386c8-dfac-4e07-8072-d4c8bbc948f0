{"version": 3, "file": "84382.dtale_bundle.js", "mappings": "8FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,wBACLC,MAAO,iCAETC,SAAU,CACRF,IAAK,aACLC,MAAO,sBAETE,YAAa,kBACbC,iBAAkB,CAChBJ,IAAK,wBACLC,MAAO,iCAETI,SAAU,CACRL,IAAK,aACLC,MAAO,sBAETK,YAAa,CACXN,IAAK,kBACLC,MAAO,2BAETM,OAAQ,CACNP,IAAK,UACLC,MAAO,mBAETO,MAAO,CACLR,IAAK,SACLC,MAAO,mBAETQ,YAAa,CACXT,IAAK,iBACLC,MAAO,0BAETS,OAAQ,CACNV,IAAK,SACLC,MAAO,kBAETU,aAAc,CACZX,IAAK,mBACLC,MAAO,6BAETW,QAAS,CACPZ,IAAK,WACLC,MAAO,qBAETY,YAAa,CACXb,IAAK,iBACLC,MAAO,wBAETa,OAAQ,CACNd,IAAK,SACLC,MAAO,gBAETc,WAAY,CACVf,IAAK,cACLC,MAAO,qBAETe,aAAc,CACZhB,IAAK,gBACLC,MAAO,wBA2BPgB,EAvBiB,SAAwBC,EAAOC,EAAOC,GACzD,IAAIC,EACAC,EAAaxB,EAAqBoB,GAUtC,OAPEG,EADwB,iBAAfC,EACAA,EACU,IAAVH,EACAG,EAAWtB,IAEXsB,EAAWrB,MAAMsB,QAAQ,YAAaC,OAAOL,IAGpDC,SAA0CA,EAAQK,UAChDL,EAAQM,YAAcN,EAAQM,WAAa,EACtC,MAAQL,EAERA,EAAS,SAIbA,CACT,EAGAzB,EAAA,QAAkBqB,EAClBU,EAAO/B,QAAUA,EAAQgC,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/nb/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'mindre enn ett sekund',\n    other: 'mindre enn {{count}} sekunder'\n  },\n  xSeconds: {\n    one: 'ett sekund',\n    other: '{{count}} sekunder'\n  },\n  halfAMinute: 'et halvt minutt',\n  lessThanXMinutes: {\n    one: 'mindre enn ett minutt',\n    other: 'mindre enn {{count}} minutter'\n  },\n  xMinutes: {\n    one: 'ett minutt',\n    other: '{{count}} minutter'\n  },\n  aboutXHours: {\n    one: 'omtrent en time',\n    other: 'omtrent {{count}} timer'\n  },\n  xHours: {\n    one: 'en time',\n    other: '{{count}} timer'\n  },\n  xDays: {\n    one: 'en dag',\n    other: '{{count}} dager'\n  },\n  aboutXWeeks: {\n    one: 'omtrent en uke',\n    other: 'omtrent {{count}} uker'\n  },\n  xWeeks: {\n    one: 'en uke',\n    other: '{{count}} uker'\n  },\n  aboutXMonths: {\n    one: 'omtrent en måned',\n    other: 'omtrent {{count}} måneder'\n  },\n  xMonths: {\n    one: 'en måned',\n    other: '{{count}} måneder'\n  },\n  aboutXYears: {\n    one: 'omtrent ett år',\n    other: 'omtrent {{count}} år'\n  },\n  xYears: {\n    one: 'ett år',\n    other: '{{count}} år'\n  },\n  overXYears: {\n    one: 'over ett år',\n    other: 'over {{count}} år'\n  },\n  almostXYears: {\n    one: 'nesten ett år',\n    other: 'nesten {{count}} år'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'om ' + result;\n    } else {\n      return result + ' siden';\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_default", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison", "module", "default"], "sourceRoot": ""}
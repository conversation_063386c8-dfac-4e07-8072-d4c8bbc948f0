{"version": 3, "file": "84904.dtale_bundle.js", "mappings": "8FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,CACHC,WAAY,qBACZC,mBAAoB,qBACpBC,kBAAmB,sBAErBC,KAAM,6BACNC,MAAO,8BAETC,SAAU,CACRN,IAAK,CACHC,WAAY,YACZC,mBAAoB,YACpBC,kBAAmB,aAErBC,KAAM,oBACNC,MAAO,qBAETE,YAAa,cACbC,iBAAkB,CAChBR,IAAK,CACHC,WAAY,oBACZC,mBAAoB,oBACpBC,kBAAmB,qBAErBC,KAAM,4BACNC,MAAO,6BAETI,SAAU,CACRT,IAAK,CACHC,WAAY,WACZC,mBAAoB,WACpBC,kBAAmB,YAErBC,KAAM,mBACNC,MAAO,oBAETK,YAAa,CACXV,IAAK,CACHC,WAAY,YACZC,mBAAoB,YACpBC,kBAAmB,aAErBC,KAAM,qBACNC,MAAO,sBAETM,OAAQ,CACNX,IAAK,CACHC,WAAY,QACZC,mBAAoB,QACpBC,kBAAmB,SAErBC,KAAM,iBACNC,MAAO,kBAETO,MAAO,CACLZ,IAAK,CACHC,WAAY,QACZC,mBAAoB,QACpBC,kBAAmB,SAErBC,KAAM,iBACNC,MAAO,kBAETQ,YAAa,CACXb,IAAK,CACHC,WAAY,eACZC,mBAAoB,eACpBC,kBAAmB,gBAErBC,KAAM,uBACNC,MAAO,yBAETS,OAAQ,CACNd,IAAK,CACHC,WAAY,WACZC,mBAAoB,WACpBC,kBAAmB,YAErBC,KAAM,mBACNC,MAAO,qBAETU,aAAc,CACZf,IAAK,CACHC,WAAY,eACZC,mBAAoB,eACpBC,kBAAmB,gBAErBC,KAAM,wBACNC,MAAO,yBAETW,QAAS,CACPhB,IAAK,CACHC,WAAY,WACZC,mBAAoB,WACpBC,kBAAmB,YAErBC,KAAM,oBACNC,MAAO,qBAETY,YAAa,CACXjB,IAAK,CACHC,WAAY,eACZC,mBAAoB,eACpBC,kBAAmB,gBAErBC,KAAM,uBACNC,MAAO,wBAETa,OAAQ,CACNlB,IAAK,CACHC,WAAY,WACZC,mBAAoB,WACpBC,kBAAmB,YAErBC,KAAM,mBACNC,MAAO,oBAETc,WAAY,CACVnB,IAAK,CACHC,WAAY,iBACZC,mBAAoB,iBACpBC,kBAAmB,kBAErBC,KAAM,yBACNC,MAAO,0BAETe,aAAc,CACZpB,IAAK,CACHC,WAAY,kBACZC,mBAAoB,kBACpBC,kBAAmB,mBAErBC,KAAM,0BACNC,MAAO,4BAuCPgB,EAnCiB,SAAwBC,EAAOC,EAAOC,GACzD,IAAIC,EACAC,EAAa5B,EAAqBwB,GAsBtC,OAnBEG,EADwB,iBAAfC,EACAA,EACU,IAAVH,EACLC,SAA0CA,EAAQG,UAChDH,EAAQI,YAAcJ,EAAQI,WAAa,EACpCF,EAAW1B,IAAIG,kBAEfuB,EAAW1B,IAAIE,mBAGjBwB,EAAW1B,IAAIC,WAEjBsB,EAAQ,GAAK,GAAKA,EAAQ,GAAK,GACV,MAAhCM,OAAON,GAAOO,QAAQ,EAAG,GAEZJ,EAAWtB,KAAK2B,QAAQ,YAAaF,OAAON,IAE9CG,EAAWrB,MAAM0B,QAAQ,YAAaF,OAAON,IAGpDC,SAA0CA,EAAQG,UAChDH,EAAQI,YAAcJ,EAAQI,WAAa,EACtC,MAAQH,EAER,SAAWA,EAIfA,CACT,EAGA7B,EAAA,QAAkByB,EAClBW,EAAOpC,QAAUA,EAAQqC,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/hr/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      standalone: 'manje od 1 sekunde',\n      withPrepositionAgo: 'manje od 1 sekunde',\n      withPrepositionIn: 'manje od 1 sekundu'\n    },\n    dual: 'manje od {{count}} sekunde',\n    other: 'manje od {{count}} sekundi'\n  },\n  xSeconds: {\n    one: {\n      standalone: '1 sekunda',\n      withPrepositionAgo: '1 sekunde',\n      withPrepositionIn: '1 sekundu'\n    },\n    dual: '{{count}} sekunde',\n    other: '{{count}} sekundi'\n  },\n  halfAMinute: 'pola minute',\n  lessThanXMinutes: {\n    one: {\n      standalone: 'manje od 1 minute',\n      withPrepositionAgo: 'manje od 1 minute',\n      withPrepositionIn: 'manje od 1 minutu'\n    },\n    dual: 'manje od {{count}} minute',\n    other: 'manje od {{count}} minuta'\n  },\n  xMinutes: {\n    one: {\n      standalone: '1 minuta',\n      withPrepositionAgo: '1 minute',\n      withPrepositionIn: '1 minutu'\n    },\n    dual: '{{count}} minute',\n    other: '{{count}} minuta'\n  },\n  aboutXHours: {\n    one: {\n      standalone: 'oko 1 sat',\n      withPrepositionAgo: 'oko 1 sat',\n      withPrepositionIn: 'oko 1 sat'\n    },\n    dual: 'oko {{count}} sata',\n    other: 'oko {{count}} sati'\n  },\n  xHours: {\n    one: {\n      standalone: '1 sat',\n      withPrepositionAgo: '1 sat',\n      withPrepositionIn: '1 sat'\n    },\n    dual: '{{count}} sata',\n    other: '{{count}} sati'\n  },\n  xDays: {\n    one: {\n      standalone: '1 dan',\n      withPrepositionAgo: '1 dan',\n      withPrepositionIn: '1 dan'\n    },\n    dual: '{{count}} dana',\n    other: '{{count}} dana'\n  },\n  aboutXWeeks: {\n    one: {\n      standalone: 'oko 1 tjedan',\n      withPrepositionAgo: 'oko 1 tjedan',\n      withPrepositionIn: 'oko 1 tjedan'\n    },\n    dual: 'oko {{count}} tjedna',\n    other: 'oko {{count}} tjedana'\n  },\n  xWeeks: {\n    one: {\n      standalone: '1 tjedan',\n      withPrepositionAgo: '1 tjedan',\n      withPrepositionIn: '1 tjedan'\n    },\n    dual: '{{count}} tjedna',\n    other: '{{count}} tjedana'\n  },\n  aboutXMonths: {\n    one: {\n      standalone: 'oko 1 mjesec',\n      withPrepositionAgo: 'oko 1 mjesec',\n      withPrepositionIn: 'oko 1 mjesec'\n    },\n    dual: 'oko {{count}} mjeseca',\n    other: 'oko {{count}} mjeseci'\n  },\n  xMonths: {\n    one: {\n      standalone: '1 mjesec',\n      withPrepositionAgo: '1 mjesec',\n      withPrepositionIn: '1 mjesec'\n    },\n    dual: '{{count}} mjeseca',\n    other: '{{count}} mjeseci'\n  },\n  aboutXYears: {\n    one: {\n      standalone: 'oko 1 godinu',\n      withPrepositionAgo: 'oko 1 godinu',\n      withPrepositionIn: 'oko 1 godinu'\n    },\n    dual: 'oko {{count}} godine',\n    other: 'oko {{count}} godina'\n  },\n  xYears: {\n    one: {\n      standalone: '1 godina',\n      withPrepositionAgo: '1 godine',\n      withPrepositionIn: '1 godinu'\n    },\n    dual: '{{count}} godine',\n    other: '{{count}} godina'\n  },\n  overXYears: {\n    one: {\n      standalone: 'preko 1 godinu',\n      withPrepositionAgo: 'preko 1 godinu',\n      withPrepositionIn: 'preko 1 godinu'\n    },\n    dual: 'preko {{count}} godine',\n    other: 'preko {{count}} godina'\n  },\n  almostXYears: {\n    one: {\n      standalone: 'gotovo 1 godinu',\n      withPrepositionAgo: 'gotovo 1 godinu',\n      withPrepositionIn: 'gotovo 1 godinu'\n    },\n    dual: 'gotovo {{count}} godine',\n    other: 'gotovo {{count}} godina'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        result = tokenValue.one.withPrepositionIn;\n      } else {\n        result = tokenValue.one.withPrepositionAgo;\n      }\n    } else {\n      result = tokenValue.one.standalone;\n    }\n  } else if (count % 10 > 1 && count % 10 < 5 && // if last digit is between 2 and 4\n  String(count).substr(-2, 1) !== '1' // unless the 2nd to last digit is \"1\"\n  ) {\n      result = tokenValue.dual.replace('{{count}}', String(count));\n    } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'za ' + result;\n    } else {\n      return 'prije ' + result;\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "one", "standalone", "withPrepositionAgo", "withPrepositionIn", "dual", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_default", "token", "count", "options", "result", "tokenValue", "addSuffix", "comparison", "String", "substr", "replace", "module", "default"], "sourceRoot": ""}
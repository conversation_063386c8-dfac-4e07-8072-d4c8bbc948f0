{"version": 3, "file": "90552.dtale_bundle.js", "mappings": "8HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIE,EAASC,EAAuB,EAAQ,QAI5C,SAASA,EAAuBC,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAE9F,IA4FIG,EA1CQ,CACVC,eAAe,EAvDHL,EAAuB,EAAQ,MAuDhBG,SAAS,CAClCG,aApD4B,wBAqD5BC,aApD4B,OAqD5BC,cAAe,SAAuBV,GACpC,OAAOW,SAASX,EAAO,GACzB,IAEFY,KAAK,EAAIX,EAAOI,SAAS,CACvBQ,cAzDmB,CACrBC,OAAQ,uBACRC,YAAa,uBACbC,KAAM,kCAuDJC,kBAAmB,OACnBC,cAtDmB,CACrBC,IAAK,CAAC,MAAO,QAsDXC,kBAAmB,QAErBC,SAAS,EAAIpB,EAAOI,SAAS,CAC3BQ,cAvDuB,CACzBC,OAAQ,WACRC,YAAa,6BACbC,KAAM,iCAqDJC,kBAAmB,OACnBC,cApDuB,CACzBC,IAAK,CAAC,KAAM,KAAM,KAAM,OAoDtBC,kBAAmB,MACnBV,cAAe,SAAuBY,GACpC,OAAOA,EAAQ,CACjB,IAEFC,OAAO,EAAItB,EAAOI,SAAS,CACzBQ,cA9CqB,CACvBE,YAAa,uDACbC,KAAM,4FA6CJC,kBAAmB,OACnBC,cA5CqB,CACvBC,IAAK,CAAC,OAAQ,MAAO,QAAS,OAAQ,QAAS,QAAS,QAAS,OAAQ,OAAQ,QAAS,OAAQ,SA4ChGC,kBAAmB,QAErBI,KAAK,EAAIvB,EAAOI,SAAS,CACvBQ,cA9DmB,CACrBC,OAAQ,YACRW,MAAO,2BACPV,YAAa,kCACbC,KAAM,6DA2DJC,kBAAmB,OACnBC,cA1DmB,CACrBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACnDK,IAAK,CAAC,UAAW,UAAW,OAAQ,OAAQ,UAAW,UAAW,YAyDhEC,kBAAmB,QAErBM,WAAW,EAAIzB,EAAOI,SAAS,CAC7BQ,cAnDyB,CAC3BM,IAAK,+CAmDHF,kBAAmB,MACnBC,cAlDyB,CAC3BC,IAAK,CACHQ,GAAI,cACJC,GAAI,YACJC,SAAU,UACVC,KAAM,YACNC,QAAS,UACTC,UAAW,YACXC,QAAS,WACTC,MAAO,UA0CPd,kBAAmB,SAIvBrB,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,kBCzGzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCI,EAF5BF,GAE4BE,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IA6BIG,EAda,CACf8B,MAAM,EAAInC,EAAOI,SAAS,CACxBgC,QAjBc,CAChBC,KAAM,qBACNC,KAAM,eACNC,OAAQ,cACRf,MAAO,cAcLgB,aAAc,SAEhBC,MAAM,EAAIzC,EAAOI,SAAS,CACxBgC,QAfc,CAChBC,KAAM,gBACNC,KAAM,aACNC,OAAQ,WACRf,MAAO,QAYLgB,aAAc,SAEhBE,UAAU,EAAI1C,EAAOI,SAAS,CAC5BgC,QAbkB,CACpBlB,IAAK,qBAaHsB,aAAc,SAIlB1C,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,kBCxCzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCI,EAF5BF,GAE4BE,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAgFIG,EA1BW,CACbC,cAtBkB,SAAuBqC,EAAaC,GACtD,IAAIC,EAASC,OAAOH,GAChBI,EAASF,EAAS,IAEtB,GAAIE,EAAS,IAAMA,EAAS,GAC1B,OAAQA,EAAS,IACf,KAAK,EACH,OAAOF,EAAS,MAElB,KAAK,EACH,OAAOA,EAAS,MAElB,KAAK,EACL,KAAK,EACH,OAAOA,EAAS,MAItB,OAAOA,EAAS,KAClB,EAIElC,KAAK,EAAIX,EAAOI,SAAS,CACvB4C,OAzDY,CACdnC,OAAQ,CAAC,UAAW,QACpBC,YAAa,CAAC,aAAc,SAC5BC,KAAM,CAAC,kBAAmB,eAuDxByB,aAAc,SAEhBpB,SAAS,EAAIpB,EAAOI,SAAS,CAC3B4C,OAxDgB,CAClBnC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,WAAY,WAAY,WAAY,YAClDC,KAAM,CAAC,eAAgB,eAAgB,eAAgB,iBAsDrDyB,aAAc,OACdS,iBAAkB,SAA0B7B,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAItB,EAAOI,SAAS,CACzB4C,OA1Dc,CAChBlC,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAAQ,MAAO,OAAQ,OAC7FC,KAAM,CAAC,UAAW,WAAY,OAAQ,QAAS,MAAO,OAAQ,OAAQ,SAAU,YAAa,WAAY,UAAW,aAyDlHyB,aAAc,SAEhBjB,KAAK,EAAIvB,EAAOI,SAAS,CACvB4C,OA1DY,CACdnC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCW,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CV,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACxDC,KAAM,CAAC,SAAU,aAAc,UAAW,QAAS,WAAY,QAAS,WAuDtEyB,aAAc,SAEhBf,WAAW,EAAIzB,EAAOI,SAAS,CAC7B4C,OAxDkB,CACpBjC,KAAM,CACJW,GAAI,aACJC,GAAI,WACJC,SAAU,SACVC,KAAM,WACNC,QAAS,SACTC,UAAW,WACXC,QAAS,UACTC,MAAO,SAgDPO,aAAc,UAIlB1C,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,gBC3FzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIoD,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,qBACLC,MAAO,gCAETC,SAAU,CACRF,IAAK,YACLC,MAAO,qBAETE,YAAa,kBACbC,iBAAkB,CAChBJ,IAAK,oBACLC,MAAO,+BAETI,SAAU,CACRL,IAAK,WACLC,MAAO,oBAETK,YAAa,CACXN,IAAK,cACLC,MAAO,wBAETM,OAAQ,CACNP,IAAK,QACLC,MAAO,kBAETO,MAAO,CACLR,IAAK,QACLC,MAAO,kBAETQ,YAAa,CACXT,IAAK,iBACLC,MAAO,0BAETS,OAAQ,CACNV,IAAK,WACLC,MAAO,oBAETU,aAAc,CACZX,IAAK,gBACLC,MAAO,0BAETW,QAAS,CACPZ,IAAK,UACLC,MAAO,oBAETY,YAAa,CACXb,IAAK,iBACLC,MAAO,0BAETa,OAAQ,CACNd,IAAK,WACLC,MAAO,oBAETc,WAAY,CACVf,IAAK,qBACLC,MAAO,8BAETe,aAAc,CACZhB,IAAK,oBACLC,MAAO,8BA2BPhD,EAvBiB,SAAwBgE,EAAOC,EAAOC,GACzD,IAAIC,EACAC,EAAavB,EAAqBmB,GAUtC,OAPEG,EADwB,iBAAfC,EACAA,EACU,IAAVH,EACAG,EAAWrB,IAEXqB,EAAWpB,MAAMqB,QAAQ,YAAaC,OAAOL,IAGpDC,SAA0CA,EAAQK,UAChDL,EAAQM,YAAcN,EAAQM,WAAa,EACtC,MAAQL,EAER,QAAUA,EAIdA,CACT,EAGA1E,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,kBC7FzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCI,EAF5BF,GAE4BE,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAAI4E,EAAW,CAAC,SAAU,aAAc,UAAW,QAAS,WAAY,QAAS,UAmBjF,SAASC,EAASxD,GAChB,IAAIyD,EAAUF,EAASvD,GAEvB,OAAQA,GACN,KAAK,EACL,KAAK,EACL,KAAK,EACH,MAAO,QAAUyD,EAAU,SAE7B,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACH,MAAO,SAAWA,EAAU,SAElC,CAmBA,IAAIC,EAAuB,CACzBC,SAAU,SAAkB/C,EAAMgD,EAAUZ,GAC1C,IAAIhD,EAAMY,EAAKiD,YAEf,OAAI,EAAIpF,EAAOI,SAAS+B,EAAMgD,EAAUZ,GAC/BQ,EAASxD,GAxDtB,SAAmBA,GACjB,IAAIyD,EAAUF,EAASvD,GAEvB,OAAQA,GACN,KAAK,EACL,KAAK,EACL,KAAK,EACH,MAAO,aAAeyD,EAAU,SAElC,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACH,MAAO,aAAeA,EAAU,SAEtC,CA2CaK,CAAU9D,EAErB,EACA+D,UAAW,eACXC,MAAO,eACPC,SAAU,cACVC,SAAU,SAAkBtD,EAAMgD,EAAUZ,GAC1C,IAAIhD,EAAMY,EAAKiD,YAEf,OAAI,EAAIpF,EAAOI,SAAS+B,EAAMgD,EAAUZ,GAC/BQ,EAASxD,GAlCtB,SAAmBA,GACjB,IAAIyD,EAAUF,EAASvD,GAEvB,OAAQA,GACN,KAAK,EACL,KAAK,EACL,KAAK,EACH,MAAO,aAAeyD,EAAU,SAElC,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACH,MAAO,aAAeA,EAAU,SAEtC,CAqBaU,CAAUnE,EAErB,EACA8B,MAAO,KAaLhD,EAViB,SAAwBgE,EAAOlC,EAAMgD,EAAUZ,GAClE,IAAIoB,EAASV,EAAqBZ,GAElC,MAAsB,mBAAXsB,EACFA,EAAOxD,EAAMgD,EAAUZ,GAGzBoB,CACT,EAGA7F,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,kBCnGzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIE,EAASC,EAAuB,EAAQ,QAExC2F,EAAU3F,EAAuB,EAAQ,QAEzC4F,EAAU5F,EAAuB,EAAQ,QAEzC6F,EAAU7F,EAAuB,EAAQ,QAEzC8F,EAAU9F,EAAuB,EAAQ,QAE7C,SAASA,EAAuBC,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAW9F,IAcIG,EAdS,CACX2F,KAAM,KACNC,eAAgBjG,EAAOI,QACvB8F,WAAYN,EAAQxF,QACpB+F,eAAgBN,EAAQzF,QACxBgG,SAAUN,EAAQ1F,QAClBiG,MAAON,EAAQ3F,QACfmE,QAAS,CACP+B,aAAc,EAGdC,sBAAuB,IAI3BzG,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,kBC1CzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,QAQA,SAAuB0G,EAAeC,EAAgBlC,IACpD,EAAIvE,EAAOI,SAAS,EAAGsG,WACvB,IAAIC,GAAsB,EAAIf,EAAQxF,SAASoG,EAAejC,GAC1DqC,GAAuB,EAAIhB,EAAQxF,SAASqG,EAAgBlC,GAChE,OAAOoC,EAAoBE,YAAcD,EAAqBC,SAChE,EAXA,IAAI7G,EAASC,EAAuB,EAAQ,QAExC2F,EAAU3F,EAAuB,EAAQ,QAE7C,SAASA,EAAuBC,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAS9FgC,EAAOpC,QAAUA,EAAQM,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/mk/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/mk/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/mk/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/mk/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/mk/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/locale/mk/index.js", "webpack://dtale/./node_modules/date-fns/_lib/isSameUTCWeek/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)(-?[врмт][и])?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^((пр)?н\\.?\\s?е\\.?)/i,\n  abbreviated: /^((пр)?н\\.?\\s?е\\.?)/i,\n  wide: /^(пред нашата ера|нашата ера)/i\n};\nvar parseEraPatterns = {\n  any: [/^п/i, /^н/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234](-?[врт]?и?)? кв.?/i,\n  wide: /^[1234](-?[врт]?и?)? квартал/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[нпвсч]/i,\n  short: /^(не|по|вт|ср|че|пе|са)/i,\n  abbreviated: /^(нед|пон|вто|сре|чет|пет|саб)/i,\n  wide: /^(недела|понеделник|вторник|среда|четврток|петок|сабота)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^н/i, /^п/i, /^в/i, /^с/i, /^ч/i, /^п/i, /^с/i],\n  any: [/^н[ед]/i, /^п[он]/i, /^вт/i, /^ср/i, /^ч[ет]/i, /^п[ет]/i, /^с[аб]/i]\n};\nvar matchMonthPatterns = {\n  abbreviated: /^(јан|фев|мар|апр|мај|јун|јул|авг|сеп|окт|ноем|дек)/i,\n  wide: /^(јануари|февруари|март|април|мај|јуни|јули|август|септември|октомври|ноември|декември)/i\n};\nvar parseMonthPatterns = {\n  any: [/^ја/i, /^Ф/i, /^мар/i, /^ап/i, /^мај/i, /^јун/i, /^јул/i, /^ав/i, /^се/i, /^окт/i, /^но/i, /^де/i]\n};\nvar matchDayPeriodPatterns = {\n  any: /^(претп|попл|полноќ|утро|пладне|вечер|ноќ)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /претпладне/i,\n    pm: /попладне/i,\n    midnight: /полноќ/i,\n    noon: /напладне/i,\n    morning: /наутро/i,\n    afternoon: /попладне/i,\n    evening: /навечер/i,\n    night: /ноќе/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'EEEE, dd MMMM yyyy',\n  long: 'dd MMMM yyyy',\n  medium: 'dd MMM yyyy',\n  short: 'dd/MM/yyyy'\n};\nvar timeFormats = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  any: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'any'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['пр.н.е.', 'н.е.'],\n  abbreviated: ['пред н. е.', 'н. е.'],\n  wide: ['пред нашата ера', 'нашата ера']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-ви кв.', '2-ри кв.', '3-ти кв.', '4-ти кв.'],\n  wide: ['1-ви квартал', '2-ри квартал', '3-ти квартал', '4-ти квартал']\n};\nvar monthValues = {\n  abbreviated: ['јан', 'фев', 'мар', 'апр', 'мај', 'јун', 'јул', 'авг', 'септ', 'окт', 'ноем', 'дек'],\n  wide: ['јануари', 'февруари', 'март', 'април', 'мај', 'јуни', 'јули', 'август', 'септември', 'октомври', 'ноември', 'декември']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['не', 'по', 'вт', 'ср', 'че', 'пе', 'са'],\n  abbreviated: ['нед', 'пон', 'вто', 'сре', 'чет', 'пет', 'саб'],\n  wide: ['недела', 'понеделник', 'вторник', 'среда', 'четврток', 'петок', 'сабота']\n};\nvar dayPeriodValues = {\n  wide: {\n    am: 'претпладне',\n    pm: 'попладне',\n    midnight: 'полноќ',\n    noon: 'напладне',\n    morning: 'наутро',\n    afternoon: 'попладне',\n    evening: 'навечер',\n    night: 'ноќе'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var rem100 = number % 100;\n\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + '-ви';\n\n      case 2:\n        return number + '-ри';\n\n      case 7:\n      case 8:\n        return number + '-ми';\n    }\n  }\n\n  return number + '-ти';\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'помалку од секунда',\n    other: 'помалку од {{count}} секунди'\n  },\n  xSeconds: {\n    one: '1 секунда',\n    other: '{{count}} секунди'\n  },\n  halfAMinute: 'половина минута',\n  lessThanXMinutes: {\n    one: 'помалку од минута',\n    other: 'помалку од {{count}} минути'\n  },\n  xMinutes: {\n    one: '1 минута',\n    other: '{{count}} минути'\n  },\n  aboutXHours: {\n    one: 'околу 1 час',\n    other: 'околу {{count}} часа'\n  },\n  xHours: {\n    one: '1 час',\n    other: '{{count}} часа'\n  },\n  xDays: {\n    one: '1 ден',\n    other: '{{count}} дена'\n  },\n  aboutXWeeks: {\n    one: 'околу 1 недела',\n    other: 'околу {{count}} месеци'\n  },\n  xWeeks: {\n    one: '1 недела',\n    other: '{{count}} недели'\n  },\n  aboutXMonths: {\n    one: 'околу 1 месец',\n    other: 'околу {{count}} недели'\n  },\n  xMonths: {\n    one: '1 месец',\n    other: '{{count}} месеци'\n  },\n  aboutXYears: {\n    one: 'околу 1 година',\n    other: 'околу {{count}} години'\n  },\n  xYears: {\n    one: '1 година',\n    other: '{{count}} години'\n  },\n  overXYears: {\n    one: 'повеќе од 1 година',\n    other: 'повеќе од {{count}} години'\n  },\n  almostXYears: {\n    one: 'безмалку 1 година',\n    other: 'безмалку {{count}} години'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'за ' + result;\n    } else {\n      return 'пред ' + result;\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../../_lib/isSameUTCWeek/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar weekdays = ['недела', 'понеделник', 'вторник', 'среда', 'четврток', 'петок', 'сабота'];\n\nfunction _lastWeek(day) {\n  var weekday = weekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'минатата \" + weekday + \" во' p\";\n\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'минатиот \" + weekday + \" во' p\";\n  }\n}\n\nfunction thisWeek(day) {\n  var weekday = weekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'ова \" + weekday + \" вo' p\";\n\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'овој \" + weekday + \" вo' p\";\n  }\n}\n\nfunction _nextWeek(day) {\n  var weekday = weekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 6:\n      return \"'следната \" + weekday + \" вo' p\";\n\n    case 1:\n    case 2:\n    case 4:\n    case 5:\n      return \"'следниот \" + weekday + \" вo' p\";\n  }\n}\n\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n\n    if ((0, _index.default)(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _lastWeek(day);\n    }\n  },\n  yesterday: \"'вчера во' p\",\n  today: \"'денес во' p\",\n  tomorrow: \"'утре во' p\",\n  nextWeek: function nextWeek(date, baseDate, options) {\n    var day = date.getUTCDay();\n\n    if ((0, _index.default)(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return _nextWeek(day);\n    }\n  },\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Macedonian locale.\n * @language Macedonian\n * @iso-639-2 mkd\n * <AUTHOR> [@vlahupetar]{@link https://github.com/vlahupetar}\n * <AUTHOR> [@altrim]{@link https://github.com/altrim}\n */\nvar locale = {\n  code: 'mk',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 1\n    /* Monday */\n    ,\n    firstWeekContainsDate: 4\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = isSameUTCWeek;\n\nvar _index = _interopRequireDefault(require(\"../requiredArgs/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../startOfUTCWeek/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction isSameUTCWeek(dirtyDateLeft, dirtyDateRight, options) {\n  (0, _index.default)(2, arguments);\n  var dateLeftStartOfWeek = (0, _index2.default)(dirtyDateLeft, options);\n  var dateRightStartOfWeek = (0, _index2.default)(dirtyDateRight, options);\n  return dateLeftStartOfWeek.getTime() === dateRightStartOfWeek.getTime();\n}\n\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "_index", "_interopRequireDefault", "obj", "__esModule", "default", "_default", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "parseInt", "era", "matchPatterns", "narrow", "abbreviated", "wide", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "quarter", "index", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "module", "date", "formats", "full", "long", "medium", "defaultWidth", "time", "dateTime", "dirtyNumber", "_options", "number", "Number", "rem100", "values", "argument<PERSON>allback", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison", "weekdays", "thisWeek", "weekday", "formatRelativeLocale", "lastWeek", "baseDate", "getUTCDay", "_lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "_nextWeek", "format", "_index2", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "weekStartsOn", "firstWeekContainsDate", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeftStartOfWeek", "dateRightStartOfWeek", "getTime"], "sourceRoot": ""}
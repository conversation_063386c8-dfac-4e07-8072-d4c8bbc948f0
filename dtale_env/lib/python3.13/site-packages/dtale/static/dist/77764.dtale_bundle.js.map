{"version": 3, "file": "77764.dtale_bundle.js", "mappings": "6EAyEAA,EAAOC,QAhEP,SAAYC,GACV,MAAMC,EAAc,CAClBC,QACE,0RAIFC,QACG,sBACHC,SACE,2FAEJ,MAAO,CACLC,KAAM,KACNC,QAAS,CAAC,UACVC,SAAUN,EACVO,QAAS,KACTC,SAAU,CACRT,EAAKU,oBACLV,EAAKW,qBACL,CACEC,UAAW,SACXC,SAAU,CACRb,EAAKc,kBACLd,EAAKe,iBACL,CACEC,MAAO,IACPC,IAAK,OAIX,CACEL,UAAW,SACXC,SAAU,CACR,CACEG,MAAOhB,EAAKkB,YAAc,MAC1BC,UAAW,GAEbnB,EAAKoB,gBAGT,CACEJ,MAAO,MAET,CACEJ,UAAW,WACXS,cAAe,OACfJ,IAAK,cACLK,YAAY,EACZb,SAAU,CACRT,EAAKuB,WACL,CACEX,UAAW,SACXI,MAAO,KACPC,IAAK,KACLV,SAAUN,EACVO,QAAS,WAMrB,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/go.js"], "sourcesContent": ["/*\nLanguage: Go\nAuthor: <PERSON> aka StepLg <<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Google go language (golang). For info about language\nWebsite: http://golang.org/\nCategory: common, system\n*/\n\nfunction go(hljs) {\n  const GO_KEYWORDS = {\n    keyword:\n      'break default func interface select case map struct chan else goto package switch ' +\n      'const fallthrough if range type continue for import return var go defer ' +\n      'bool byte complex64 complex128 float32 float64 int8 int16 int32 int64 string uint8 ' +\n      'uint16 uint32 uint64 int uint uintptr rune',\n    literal:\n       'true false iota nil',\n    built_in:\n      'append cap close complex copy imag len make new panic print println real recover delete'\n  };\n  return {\n    name: 'Go',\n    aliases: ['golang'],\n    keywords: GO_KEYWORDS,\n    illegal: '</',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'string',\n        variants: [\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          {\n            begin: '`',\n            end: '`'\n          }\n        ]\n      },\n      {\n        className: 'number',\n        variants: [\n          {\n            begin: hljs.C_NUMBER_RE + '[i]',\n            relevance: 1\n          },\n          hljs.C_NUMBER_MODE\n        ]\n      },\n      {\n        begin: /:=/ // relevance booster\n      },\n      {\n        className: 'function',\n        beginKeywords: 'func',\n        end: '\\\\s*(\\\\{|$)',\n        excludeEnd: true,\n        contains: [\n          hljs.TITLE_MODE,\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: GO_KEYWORDS,\n            illegal: /[\"']/\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = go;\n"], "names": ["module", "exports", "hljs", "GO_KEYWORDS", "keyword", "literal", "built_in", "name", "aliases", "keywords", "illegal", "contains", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "className", "variants", "QUOTE_STRING_MODE", "APOS_STRING_MODE", "begin", "end", "C_NUMBER_RE", "relevance", "C_NUMBER_MODE", "beginKeywords", "excludeEnd", "TITLE_MODE"], "sourceRoot": ""}
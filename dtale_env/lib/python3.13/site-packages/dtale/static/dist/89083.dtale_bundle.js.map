{"version": 3, "file": "89083.dtale_bundle.js", "mappings": "4HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIE,EAASC,EAAuB,EAAQ,QAI5C,SAASA,EAAuBC,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAE9F,IA+FIG,EA1CQ,CACVC,eAAe,EA1DHL,EAAuB,EAAQ,MA0DhBG,SAAS,CAClCG,aAvD4B,uBAwD5BC,aAvD4B,OAwD5BC,cAAe,SAAuBV,GACpC,OAAOW,SAASX,EAAO,GACzB,IAEFY,KAAK,EAAIX,EAAOI,SAAS,CACvBQ,cA5DmB,CACrBC,OAAQ,UACRC,YAAa,6DACbC,KAAM,oEA0DJC,kBAAmB,OACnBC,cAzDmB,CACrBC,IAAK,CAAC,MAAO,YAyDXC,kBAAmB,QAErBC,SAAS,EAAIpB,EAAOI,SAAS,CAC3BQ,cA1DuB,CACzBC,OAAQ,WACRC,YAAa,YACbC,KAAM,mCAwDJC,kBAAmB,OACnBC,cAvDuB,CACzBC,IAAK,CAAC,KAAM,KAAM,KAAM,OAuDtBC,kBAAmB,MACnBV,cAAe,SAAuBY,GACpC,OAAOA,EAAQ,CACjB,IAEFC,OAAO,EAAItB,EAAOI,SAAS,CACzBQ,cA3DqB,CACvBC,OAAQ,gBACRC,YAAa,iEACbC,KAAM,qJAyDJC,kBAAmB,OACnBC,cAxDqB,CACvBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACtFK,IAAK,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,MAAO,MAAO,MAAO,OAAQ,MAAO,OAAQ,QAuDvFC,kBAAmB,QAErBI,KAAK,EAAIvB,EAAOI,SAAS,CACvBQ,cAxDmB,CACrBC,OAAQ,cACRW,MAAO,2BACPV,YAAa,kCACbC,KAAM,4EAqDJC,kBAAmB,OACnBC,cApDmB,CACrBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACnDK,IAAK,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,QAmD9CC,kBAAmB,QAErBM,WAAW,EAAIzB,EAAOI,SAAS,CAC7BQ,cApDyB,CAC3BC,OAAQ,2DACRK,IAAK,6FAmDHF,kBAAmB,MACnBC,cAlDyB,CAC3BC,IAAK,CACHQ,GAAI,MACJC,GAAI,MACJC,SAAU,oBACVC,KAAM,eACNC,QAAS,eACTC,UAAW,WACXC,QAAS,WACTC,MAAO,oBA0CPd,kBAAmB,SAIvBrB,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,kBC5GzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCI,EAF5BF,GAE4BE,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAgCIG,EAda,CACf8B,MAAM,EAAInC,EAAOI,SAAS,CACxBgC,QApBc,CAChBC,KAAM,mBACNC,KAAM,aACNC,OAAQ,WACRf,MAAO,cAiBLgB,aAAc,SAEhBC,MAAM,EAAIzC,EAAOI,SAAS,CACxBgC,QAlBc,CAChBC,KAAM,iBACNC,KAAM,cACNC,OAAQ,YACRf,MAAO,UAeLgB,aAAc,SAEhBE,UAAU,EAAI1C,EAAOI,SAAS,CAC5BgC,QAhBkB,CACpBC,KAAM,0BACNC,KAAM,0BACNC,OAAQ,qBACRf,MAAO,sBAaLgB,aAAc,UAIlB1C,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,gBC3CzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAI6C,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,oBACLC,MAAO,iCAETC,SAAU,CACRF,IAAK,SACLG,IAAK,UACLC,OAAQ,UACRH,MAAO,oBAETI,YAAa,gBACbC,iBAAkB,CAChBN,IAAK,uBACLC,MAAO,qCAETM,SAAU,CACRP,IAAK,YACLG,IAAK,aACLC,OAAQ,aACRH,MAAO,wBAETO,YAAa,CACXR,IAAK,mBACLC,MAAO,iCAETQ,OAAQ,CACNT,IAAK,kBACLG,IAAK,kBACLC,OAAQ,mBACRH,MAAO,8BAETS,MAAO,CACLV,IAAK,OACLC,MAAO,gBAETU,YAAa,CACXX,IAAK,iBACLC,MAAO,6BAETW,OAAQ,CACNZ,IAAK,cACLC,MAAO,0BAETY,aAAc,CACZb,IAAK,WACLC,MAAO,uBAETa,QAAS,CACPd,IAAK,SACLC,MAAO,oBAETc,YAAa,CACXf,IAAK,eACLC,MAAO,+BAETe,OAAQ,CACNhB,IAAK,cACLC,MAAO,sBAETgB,WAAY,CACVjB,IAAK,mBACLC,MAAO,oCAETiB,aAAc,CACZlB,IAAK,oBACLC,MAAO,sCA+BPzC,EA3BiB,SAAwB2D,EAAOC,EAAOC,GACzD,IAAIC,EACAC,EAAazB,EAAqBqB,GActC,OAXEG,EADwB,iBAAfC,EACAA,EACU,IAAVH,EACAG,EAAWvB,IACD,IAAVoB,GAAiBG,EAAWpB,IAC5BoB,EAAWpB,IACD,KAAViB,GAAkBG,EAAWnB,OAC7BmB,EAAWnB,OAEXmB,EAAWtB,MAAMuB,QAAQ,YAAaC,OAAOL,IAGpDC,SAA0CA,EAAQK,UAChDL,EAAQM,YAAcN,EAAQM,WAAa,EACtC,UAAYL,EAEZ,YAAcA,EAIlBA,CACT,EAGArE,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,kBCvGzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCI,EAF5BF,GAE4BE,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IA2IIG,EA5BW,CACbC,cAtBkB,SAAuBmE,GACzC,IAAIC,EAASC,OAAOF,GAChBG,EAASF,EAAS,IAEtB,GAAIE,EAAS,IAAMA,EAAS,GAC1B,OAAQA,EAAS,IACf,KAAK,EACH,OAAOF,EAAS,IAElB,KAAK,EACH,OAAOA,EAAS,KAItB,OAAe,KAAXE,EACKF,EAAS,KAGXA,EAAS,IAClB,EAIE/D,KAAK,EAAIX,EAAOI,SAAS,CACvByE,OAlHY,CACdhE,OAAQ,CAAC,IAAK,KACdC,YAAa,CAAC,KAAM,MACpBC,KAAM,CAAC,cAAe,gBAgHpByB,aAAc,SAEhBpB,SAAS,EAAIpB,EAAOI,SAAS,CAC3ByE,OAjHgB,CAClBhE,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,KAAM,KAAM,KAAM,MAChCC,KAAM,CAAC,qBAAsB,oBAAqB,oBAAqB,0BA+GrEyB,aAAc,OACdsC,iBAAkB,SAA0B1D,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAItB,EAAOI,SAAS,CACzByE,OA/Gc,CAChBhE,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,QACrGC,KAAM,CAAC,gBAAiB,aAAc,UAAW,aAAc,aAAc,eAAgB,cAAe,cAAe,eAAgB,aAAc,eAAgB,iBA6GvKyB,aAAc,SAEhBjB,KAAK,EAAIvB,EAAOI,SAAS,CACvByE,OA9GY,CACdhE,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCW,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CV,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACxDC,KAAM,CAAC,cAAe,UAAW,UAAW,YAAa,YAAa,WAAY,gBA2GhFyB,aAAc,SAEhBf,WAAW,EAAIzB,EAAOI,SAAS,CAC7ByE,OA5GkB,CACpBhE,OAAQ,CACNa,GAAI,IACJC,GAAI,IACJC,SAAU,OACVC,KAAM,OACNC,QAAS,UACTC,UAAW,UACXC,QAAS,UACTC,MAAO,WAETnB,YAAa,CACXY,GAAI,KACJC,GAAI,KACJC,SAAU,kBACVC,KAAM,aACNC,QAAS,UACTC,UAAW,UACXC,QAAS,UACTC,MAAO,WAETlB,KAAM,CACJW,GAAI,KACJC,GAAI,KACJC,SAAU,kBACVC,KAAM,aACNC,QAAS,UACTC,UAAW,UACXC,QAAS,UACTC,MAAO,YAgFPO,aAAc,OACduC,iBA9E4B,CAC9BlE,OAAQ,CACNa,GAAI,IACJC,GAAI,IACJC,SAAU,OACVC,KAAM,OACNC,QAAS,cACTC,UAAW,UACXC,QAAS,UACTC,MAAO,kBAETnB,YAAa,CACXY,GAAI,KACJC,GAAI,KACJC,SAAU,kBACVC,KAAM,aACNC,QAAS,cACTC,UAAW,UACXC,QAAS,UACTC,MAAO,kBAETlB,KAAM,CACJW,GAAI,KACJC,GAAI,KACJC,SAAU,kBACVC,KAAM,aACNC,QAAS,cACTC,UAAW,UACXC,QAAS,UACTC,MAAO,mBAkDP+C,uBAAwB,UAI5BlF,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,kBCtJzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIE,EAASC,EAAuB,EAAQ,QAExCgF,EAAUhF,EAAuB,EAAQ,QAEzCiF,EAAUjF,EAAuB,EAAQ,QAEzCkF,EAAUlF,EAAuB,EAAQ,QAEzCmF,EAAUnF,EAAuB,EAAQ,OAE7C,SAASA,EAAuBC,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAU9F,IAcIG,EAdS,CACXgF,KAAM,KACNC,eAAgBtF,EAAOI,QACvBmF,WAAYN,EAAQ7E,QACpBoF,eAAgBN,EAAQ9E,QACxBqF,SAAUN,EAAQ/E,QAClBsF,MAAON,EAAQhF,QACf8D,QAAS,CACPyB,aAAc,EAGdC,sBAAuB,IAI3B9F,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O,gBCzCzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAI+F,EAAuB,CACzBC,SAAU,8BAEVC,UAAW,gBACXC,MAAO,mBACPC,SAAU,qBACVC,SAAU,eACVpD,MAAO,KAOLzC,EAJiB,SAAwB2D,EAAOmC,EAAOC,EAAWC,GACpE,OAAOR,EAAqB7B,EAC9B,EAGAlE,EAAA,QAAkBO,EAClB6B,EAAOpC,QAAUA,EAAQM,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/gd/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/gd/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/gd/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/gd/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/gd/index.js", "webpack://dtale/./node_modules/date-fns/locale/gd/_lib/formatRelative/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)(d|na|tr|mh)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(r|a)/i,\n  abbreviated: /^(r\\.?\\s?c\\.?|r\\.?\\s?a\\.?\\s?c\\.?|a\\.?\\s?d\\.?|a\\.?\\s?c\\.?)/i,\n  wide: /^(ro <PERSON>|ron aois choitchinn|anno domini|aois choitcheann)/i\n};\nvar parseEraPatterns = {\n  any: [/^b/i, /^(a|c)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^c[1234]/i,\n  wide: /^[1234](cd|na|tr|mh)? cairteal/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[fgmcòilsd]/i,\n  abbreviated: /^(faoi|gear|màrt|gibl|cèit|ògmh|iuch|lùn|sult|dàmh|samh|dùbh)/i,\n  wide: /^(am faoilleach|an gearran|am màrt|an giblean|an cèitean|an t-Ògmhios|an t-Iuchar|an lùnastal|an t-Sultain|an dàmhair|an t-Samhain|an dùbhlachd)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^f/i, /^g/i, /^m/i, /^g/i, /^c/i, /^ò/i, /^i/i, /^l/i, /^s/i, /^d/i, /^s/i, /^d/i],\n  any: [/^fa/i, /^ge/i, /^mà/i, /^gi/i, /^c/i, /^ò/i, /^i/i, /^l/i, /^su/i, /^d/i, /^sa/i, /^d/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[dlmcahs]/i,\n  short: /^(dò|lu|mà|ci|ar|ha|sa)/i,\n  abbreviated: /^(did|dil|dim|dic|dia|dih|dis)/i,\n  wide: /^(didòmhnaich|diluain|dimàirt|diciadain|diardaoin|dihaoine|disathairne)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^d/i, /^l/i, /^m/i, /^c/i, /^a/i, /^h/i, /^s/i],\n  any: [/^d/i, /^l/i, /^m/i, /^c/i, /^a/i, /^h/i, /^s/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(a|p|mi|n|(san|aig) (madainn|feasgar|feasgar|oidhche))/i,\n  any: /^([ap]\\.?\\s?m\\.?|meadhan oidhche|meadhan là|(san|aig) (madainn|feasgar|feasgar|oidhche))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^m/i,\n    pm: /^f/i,\n    midnight: /^meadhan oidhche/i,\n    noon: /^meadhan là/i,\n    morning: /sa mhadainn/i,\n    afternoon: /feasgar/i,\n    evening: /feasgar/i,\n    night: /air an oidhche/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'EEEE, MMMM do, y',\n  long: 'MMMM do, y',\n  medium: 'MMM d, y',\n  short: 'MM/dd/yyyy'\n};\nvar timeFormats = {\n  full: 'h:mm:ss a zzzz',\n  long: 'h:mm:ss a z',\n  medium: 'h:mm:ss a',\n  short: 'h:mm a'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'aig' {{time}}\",\n  long: \"{{date}} 'aig' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'nas lugha na diog',\n    other: 'nas lugha na {{count}} diogan'\n  },\n  xSeconds: {\n    one: '1 diog',\n    two: '2 dhiog',\n    twenty: '20 diog',\n    other: '{{count}} diogan'\n  },\n  halfAMinute: 'leth mhionaid',\n  lessThanXMinutes: {\n    one: 'nas lugha na mionaid',\n    other: 'nas lugha na {{count}} mionaidean'\n  },\n  xMinutes: {\n    one: '1 mionaid',\n    two: '2 mhionaid',\n    twenty: '20 mionaid',\n    other: '{{count}} mionaidean'\n  },\n  aboutXHours: {\n    one: 'mu uair de thìde',\n    other: 'mu {{count}} uairean de thìde'\n  },\n  xHours: {\n    one: '1 uair de thìde',\n    two: '2 uair de thìde',\n    twenty: '20 uair de thìde',\n    other: '{{count}} uairean de thìde'\n  },\n  xDays: {\n    one: '1 là',\n    other: '{{count}} là'\n  },\n  aboutXWeeks: {\n    one: 'mu 1 seachdain',\n    other: 'mu {{count}} seachdainean'\n  },\n  xWeeks: {\n    one: '1 seachdain',\n    other: '{{count}} seachdainean'\n  },\n  aboutXMonths: {\n    one: 'mu mhìos',\n    other: 'mu {{count}} mìosan'\n  },\n  xMonths: {\n    one: '1 mìos',\n    other: '{{count}} mìosan'\n  },\n  aboutXYears: {\n    one: 'mu bhliadhna',\n    other: 'mu {{count}} bliadhnaichean'\n  },\n  xYears: {\n    one: '1 bhliadhna',\n    other: '{{count}} bliadhna'\n  },\n  overXYears: {\n    one: 'còrr is bliadhna',\n    other: 'còrr is {{count}} bliadhnaichean'\n  },\n  almostXYears: {\n    one: 'cha mhòr bliadhna',\n    other: 'cha mhòr {{count}} bliadhnaichean'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2 && !!tokenValue.two) {\n    result = tokenValue.two;\n  } else if (count === 20 && !!tokenValue.twenty) {\n    result = tokenValue.twenty;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'ann an ' + result;\n    } else {\n      return 'o chionn ' + result;\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['R', 'A'],\n  abbreviated: ['RC', 'AD'],\n  wide: ['ro Chrìosta', 'anno domini']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['C1', 'C2', 'C3', 'C4'],\n  wide: [\"a' chiad chairteal\", 'an dàrna cairteal', 'an treas cairteal', 'an ceathramh cairteal']\n}; // Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n\nvar monthValues = {\n  narrow: ['F', 'G', 'M', 'G', 'C', 'Ò', 'I', 'L', 'S', 'D', 'S', 'D'],\n  abbreviated: ['Faoi', 'Gear', 'Màrt', 'Gibl', 'Cèit', 'Ògmh', 'Iuch', 'Lùn', 'Sult', 'Dàmh', 'Samh', 'Dùbh'],\n  wide: ['Am Faoilleach', 'An Gearran', 'Am Màrt', 'An Giblean', 'An Cèitean', 'An t-Ògmhios', 'An t-Iuchar', 'An Lùnastal', 'An t-Sultain', 'An Dàmhair', 'An t-Samhain', 'An Dùbhlachd']\n};\nvar dayValues = {\n  narrow: ['D', 'L', 'M', 'C', 'A', 'H', 'S'],\n  short: ['Dò', 'Lu', 'Mà', 'Ci', 'Ar', 'Ha', 'Sa'],\n  abbreviated: ['Did', 'Dil', 'Dim', 'Dic', 'Dia', 'Dih', 'Dis'],\n  wide: ['Didòmhnaich', 'Diluain', 'Dimàirt', 'Diciadain', 'Diardaoin', 'Dihaoine', 'Disathairne']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'm',\n    pm: 'f',\n    midnight: 'm.o.',\n    noon: 'm.l.',\n    morning: 'madainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'oidhche'\n  },\n  abbreviated: {\n    am: 'M.',\n    pm: 'F.',\n    midnight: 'meadhan oidhche',\n    noon: 'meadhan là',\n    morning: 'madainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'oidhche'\n  },\n  wide: {\n    am: 'm.',\n    pm: 'f.',\n    midnight: 'meadhan oidhche',\n    noon: 'meadhan là',\n    morning: 'madainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'oidhche'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'm',\n    pm: 'f',\n    midnight: 'm.o.',\n    noon: 'm.l.',\n    morning: 'sa mhadainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'air an oidhche'\n  },\n  abbreviated: {\n    am: 'M.',\n    pm: 'F.',\n    midnight: 'meadhan oidhche',\n    noon: 'meadhan là',\n    morning: 'sa mhadainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'air an oidhche'\n  },\n  wide: {\n    am: 'm.',\n    pm: 'f.',\n    midnight: 'meadhan oidhche',\n    noon: 'meadhan là',\n    morning: 'sa mhadainn',\n    afternoon: 'feasgar',\n    evening: 'feasgar',\n    night: 'air an oidhche'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber) {\n  var number = Number(dirtyNumber);\n  var rem100 = number % 100;\n\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + 'd';\n\n      case 2:\n        return number + 'na';\n    }\n  }\n\n  if (rem100 === 12) {\n    return number + 'na';\n  }\n\n  return number + 'mh';\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Scottish Gaelic.\n * @language Scottish Gaelic\n * @iso-639-2 gla\n * <AUTHOR> [@leedriscoll]{@link https://github.com/leedriscoll}\n */\nvar locale = {\n  code: 'gd',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 0\n    /* Sunday */\n    ,\n    firstWeekContainsDate: 1\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"'mu dheireadh' eeee 'aig' p\",\n  //FIX\n  yesterday: \"'an-dè aig' p\",\n  today: \"'an-diugh aig' p\",\n  tomorrow: \"'a-màireach aig' p\",\n  nextWeek: \"eeee 'aig' p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "_index", "_interopRequireDefault", "obj", "__esModule", "default", "_default", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "parseInt", "era", "matchPatterns", "narrow", "abbreviated", "wide", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "quarter", "index", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "module", "date", "formats", "full", "long", "medium", "defaultWidth", "time", "dateTime", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "two", "twenty", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison", "dirtyNumber", "number", "Number", "rem100", "values", "argument<PERSON>allback", "formattingValues", "defaultFormattingWidth", "_index2", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "weekStartsOn", "firstWeekContainsDate", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "_date", "_baseDate", "_options"], "sourceRoot": ""}
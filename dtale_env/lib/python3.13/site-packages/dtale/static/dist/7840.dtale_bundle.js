"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[7840],{47365:(e,t,d)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a,l=(a=d(19059))&&a.__esModule?a:{default:a};var u={date:(0,l.default)({formats:{full:"EEEE, d MMMM yyyy",long:"d MMMM, yyyy",medium:"d MMM, yyyy",short:"dd/MM/yyyy"},defaultWidth:"full"}),time:(0,l.default)({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:(0,l.default)({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})};t.default=u,e.exports=t.default}}]);
//# sourceMappingURL=7840.dtale_bundle.js.map
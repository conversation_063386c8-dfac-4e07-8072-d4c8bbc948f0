{"version": 3, "file": "81040.dtale_bundle.js", "mappings": "6HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAIgCE,EAJ5BC,EAAS,EAAQ,OAEjBC,GAE4BF,EAFK,EAAQ,SAEQA,EAAIG,WAAaH,EAAM,CAAEI,QAASJ,GAEvF,IAAIK,EAAqB,CAAC,SAAU,YAAa,WAAY,SAAU,SAAU,WAAY,UAmB7F,SAASC,EAASC,GAEhB,MAAO,MADOF,EAAmBE,GACR,OAC3B,CAmBA,IAsBIC,EAAuB,CACzBC,SAvBmB,SAAwBC,EAAWC,EAAUC,GAChE,IAAIC,GAAO,EAAIZ,EAAOa,QAAQJ,GAC1BH,EAAMM,EAAKE,YAEf,OAAI,EAAIb,EAAQE,SAASS,EAAMF,EAAUC,GAChCN,EAASC,GA5CpB,SAAkBA,GAChB,IAAIS,EAAUX,EAAmBE,GAEjC,OAAQA,GACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACH,MAAO,aAAeS,EAAU,QAElC,KAAK,EACL,KAAK,EACL,KAAK,EACH,MAAO,cAAgBA,EAAU,QAEvC,CA+BWP,CAASF,EAEpB,EAeEU,UAAW,cACXC,MAAO,iBACPC,SAAU,eACVC,SAhBmB,SAAwBV,EAAWC,EAAUC,GAChE,IAAIC,GAAO,EAAIZ,EAAOa,QAAQJ,GAC1BH,EAAMM,EAAKE,YAEf,OAAI,EAAIb,EAAQE,SAASS,EAAMF,EAAUC,GAChCN,EAASC,GAjCpB,SAAkBA,GAChB,IAAIS,EAAUX,EAAmBE,GAEjC,OAAQA,GACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACH,MAAO,eAAiBS,EAAU,QAEpC,KAAK,EACL,KAAK,EACL,KAAK,EACH,MAAO,gBAAkBA,EAAU,QAEzC,CAoBWI,CAASb,EAEpB,EAQEc,MAAO,KAaLC,EAViB,SAAwBC,EAAOV,EAAMF,EAAUC,GAClE,IAAIY,EAAShB,EAAqBe,GAElC,MAAsB,mBAAXC,EACFA,EAAOX,EAAMF,EAAUC,GAGzBY,CACT,EAGA1B,EAAA,QAAkBwB,EAClBG,EAAO3B,QAAUA,EAAQM,O,kBC/FzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIG,WAAaH,EAAM,CAAEI,QAASJ,GAEvF,IAgCIsB,EAda,CACfT,MAAM,EAAIZ,EAAOG,SAAS,CACxBsB,QApBc,CAChBC,KAAM,uBACNC,KAAM,iBACNC,OAAQ,eACRC,MAAO,WAiBLC,aAAc,SAEhBC,MAAM,EAAI/B,EAAOG,SAAS,CACxBsB,QAlBc,CAChBC,KAAM,eACNC,KAAM,YACNC,OAAQ,UACRC,MAAO,QAeLC,aAAc,SAEhBE,UAAU,EAAIhC,EAAOG,SAAS,CAC5BsB,QAhBkB,CACpBC,KAAM,wBACNC,KAAM,wBACNC,OAAQ,qBACRC,MAAO,sBAaLC,aAAc,UAIlBjC,EAAA,QAAkBwB,EAClBG,EAAO3B,QAAUA,EAAQM,O,kBC3CzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAASiC,EAAuB,EAAQ,QAI5C,SAASA,EAAuBlC,GAAO,OAAOA,GAAOA,EAAIG,WAAaH,EAAM,CAAEI,QAASJ,EAAO,CAE9F,IAgGIsB,EA1CQ,CACVa,eAAe,EA3DHD,EAAuB,EAAQ,MA2DhB9B,SAAS,CAClCgC,aAxD4B,0BAyD5BC,aAxD4B,OAyD5BC,cAAe,SAAuBvC,GACpC,OAAOwC,SAASxC,EAAO,GACzB,IAEFyC,KAAK,EAAIvC,EAAOG,SAAS,CACvBqC,cA7DmB,CACrBC,OAAQ,wBACRC,YAAa,wBACbC,KAAM,uCA2DJC,kBAAmB,OACnBC,cA1DmB,CACrBC,IAAK,CAAC,MAAO,QA0DXC,kBAAmB,QAErBC,SAAS,EAAIhD,EAAOG,SAAS,CAC3BqC,cA3DuB,CACzBC,OAAQ,WACRC,YAAa,4BACbC,KAAM,gCAyDJC,kBAAmB,OACnBC,cAxDuB,CACzBC,IAAK,CAAC,KAAM,KAAM,KAAM,OAwDtBC,kBAAmB,MACnBV,cAAe,SAAuBY,GACpC,OAAOA,EAAQ,CACjB,IAEFC,OAAO,EAAIlD,EAAOG,SAAS,CACzBqC,cA5DqB,CACvBC,OAAQ,gBACRC,YAAa,+EACbC,KAAM,uLA0DJC,kBAAmB,OACnBC,cAzDqB,CACvBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACtFK,IAAK,CAAC,OAAQ,OAAQ,MAAO,MAAO,MAAO,MAAO,QAAS,OAAQ,MAAO,MAAO,QAAS,QAwDxFC,kBAAmB,QAErBzC,KAAK,EAAIN,EAAOG,SAAS,CACvBqC,cAzDmB,CACrBC,OAAQ,YACRZ,MAAO,8BACPa,YAAa,wCACbC,KAAM,+FAsDJC,kBAAmB,OACnBC,cArDmB,CACrBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACnDK,IAAK,CAAC,MAAO,UAAW,MAAO,UAAW,MAAO,cAAe,YAoD9DC,kBAAmB,QAErBI,WAAW,EAAInD,EAAOG,SAAS,CAC7BqC,cArDyB,CAC3BC,OAAQ,gEACRC,YAAa,gEACbC,KAAM,wEAmDJC,kBAAmB,OACnBC,cAlDyB,CAC3BC,IAAK,CACHM,GAAI,OACJC,GAAI,OACJC,SAAU,SACVC,KAAM,QACNC,QAAS,MACTC,UAAW,UACXC,QAAS,MACTC,MAAO,QA0CPZ,kBAAmB,SAIvBlD,EAAA,QAAkBwB,EAClBG,EAAO3B,QAAUA,EAAQM,O,kBC7GzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIG,WAAaH,EAAM,CAAEI,QAASJ,GAEvF,IA8IIsB,EA9BW,CACba,cArBkB,SAAuB0B,EAAajD,GACtD,IAAIkD,EAAOC,OAAOnD,aAAyC,EAASA,EAAQkD,MACxEE,EAASC,OAAOJ,GAepB,OAAOG,GAZM,SAATF,EACa,IAAXE,GAA2B,KAAXA,EACT,KAEA,KAEO,WAATF,GAA8B,WAATA,GAA8B,SAATA,EAC1C,KAEA,KAIb,EAIEtB,KAAK,EAAIvC,EAAOG,SAAS,CACvB8D,OAnHY,CACdxB,OAAQ,CAAC,UAAW,QACpBC,YAAa,CAAC,WAAY,SAC1BC,KAAM,CAAC,eAAgB,cAiHrBb,aAAc,SAEhBkB,SAAS,EAAIhD,EAAOG,SAAS,CAC3B8D,OAlHgB,CAClBxB,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,UAAW,UAAW,UAAW,WAC/CC,KAAM,CAAC,cAAe,cAAe,cAAe,gBAgHlDb,aAAc,OACdoC,iBAAkB,SAA0BlB,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAIlD,EAAOG,SAAS,CACzB8D,OApHc,CAEhBxB,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,OAAQ,OAAQ,SAAU,QAAS,QAAS,QAAS,OAAQ,QAAS,SAAU,QAAS,UAAW,SAClHC,KAAM,CAAC,SAAU,QAAS,WAAY,UAAW,UAAW,UAAW,SAAU,UAAW,WAAY,UAAW,WAAY,YAiH7Hb,aAAc,OACdqC,iBAhHwB,CAC1B1B,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,OAAQ,OAAQ,SAAU,QAAS,QAAS,QAAS,OAAQ,QAAS,SAAU,QAAS,UAAW,SAClHC,KAAM,CAAC,QAAS,SAAU,UAAW,SAAU,SAAU,SAAU,QAAS,SAAU,UAAW,SAAU,YAAa,WA8GtHyB,uBAAwB,SAE1B9D,KAAK,EAAIN,EAAOG,SAAS,CACvB8D,OA/GY,CACdxB,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCZ,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5Ca,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACxDC,KAAM,CAAC,SAAU,YAAa,WAAY,SAAU,SAAU,WAAY,WA4GxEb,aAAc,SAEhBqB,WAAW,EAAInD,EAAOG,SAAS,CAC7B8D,OA7GkB,CACpBxB,OAAQ,CACNW,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,OACNC,QAAS,QACTC,UAAW,OACXC,QAAS,OACTC,MAAO,OAETjB,YAAa,CACXU,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,OACNC,QAAS,QACTC,UAAW,OACXC,QAAS,OACTC,MAAO,OAEThB,KAAM,CACJS,GAAI,KACJC,GAAI,KACJC,SAAU,SACVC,KAAM,WACNC,QAAS,QACTC,UAAW,OACXC,QAAS,QACTC,MAAO,QAiFP7B,aAAc,MACdqC,iBA/E4B,CAC9B1B,OAAQ,CACNW,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,OACNC,QAAS,QACTC,UAAW,MACXC,QAAS,OACTC,MAAO,QAETjB,YAAa,CACXU,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,OACNC,QAAS,QACTC,UAAW,MACXC,QAAS,OACTC,MAAO,QAEThB,KAAM,CACJS,GAAI,KACJC,GAAI,KACJC,SAAU,SACVC,KAAM,WACNC,QAAS,QACTC,UAAW,MACXC,QAAS,OACTC,MAAO,SAmDPS,uBAAwB,UAI5BvE,EAAA,QAAkBwB,EAClBG,EAAO3B,QAAUA,EAAQM,O,gBCpJzB,SAASkE,EAAWC,EAAQC,GAE1B,QAAmBC,IAAfF,EAAOG,KAA+B,IAAVF,EAC9B,OAAOD,EAAOG,IAGhB,IAAIC,EAAQH,EAAQ,GAChBI,EAASJ,EAAQ,IAErB,OAAc,IAAVG,GAA0B,KAAXC,EACVL,EAAOM,mBAAmBC,QAAQ,YAAaf,OAAOS,IACpDG,GAAS,GAAKA,GAAS,IAAMC,EAAS,IAAMA,EAAS,IACvDL,EAAOQ,iBAAiBD,QAAQ,YAAaf,OAAOS,IAEpDD,EAAOS,eAAeF,QAAQ,YAAaf,OAAOS,GAE7D,CAEA,SAASS,EAAqBV,GAC5B,OAAO,SAAUC,EAAO5D,GACtB,OAAIA,GAAWA,EAAQsE,UACjBtE,EAAQuE,YAAcvE,EAAQuE,WAAa,EACzCZ,EAAOa,OACFd,EAAWC,EAAOa,OAAQZ,GAE1B,MAAQF,EAAWC,EAAOc,QAASb,GAGxCD,EAAOe,KACFhB,EAAWC,EAAOe,KAAMd,GAExBF,EAAWC,EAAOc,QAASb,GAAS,QAIxCF,EAAWC,EAAOc,QAASb,EAEtC,CACF,CA3CA5E,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EA0ClB,IAYIyF,EAAuB,CACzBC,iBAAkBP,EAAqB,CACrCI,QAAS,CACPX,IAAK,gBACLG,mBAAoB,0BACpBE,iBAAkB,yBAClBC,eAAgB,0BAElBI,OAAQ,CACNV,IAAK,wBACLG,mBAAoB,kCACpBE,iBAAkB,kCAClBC,eAAgB,oCAGpBS,SAAUR,EAAqB,CAC7BI,QAAS,CACPR,mBAAoB,oBACpBE,iBAAkB,oBAClBC,eAAgB,oBAElBM,KAAM,CACJT,mBAAoB,yBACpBE,iBAAkB,yBAClBC,eAAgB,yBAElBI,OAAQ,CACNP,mBAAoB,uBACpBE,iBAAkB,uBAClBC,eAAgB,yBAGpBU,YA5CiB,SAAsBC,EAAG/E,GAC1C,OAAIA,GAAWA,EAAQsE,UACjBtE,EAAQuE,YAAcvE,EAAQuE,WAAa,EACtC,gBAEA,kBAIJ,YACT,EAmCES,iBAAkBX,EAAqB,CACrCI,QAAS,CACPX,IAAK,gBACLG,mBAAoB,0BACpBE,iBAAkB,yBAClBC,eAAgB,0BAElBI,OAAQ,CACNV,IAAK,wBACLG,mBAAoB,kCACpBE,iBAAkB,kCAClBC,eAAgB,oCAGpBa,SAAUZ,EAAqB,CAC7BI,QAAS,CACPR,mBAAoB,oBACpBE,iBAAkB,oBAClBC,eAAgB,oBAElBM,KAAM,CACJT,mBAAoB,yBACpBE,iBAAkB,yBAClBC,eAAgB,yBAElBI,OAAQ,CACNP,mBAAoB,uBACpBE,iBAAkB,uBAClBC,eAAgB,yBAGpBc,YAAab,EAAqB,CAChCI,QAAS,CACPR,mBAAoB,2BACpBE,iBAAkB,0BAClBC,eAAgB,2BAElBI,OAAQ,CACNP,mBAAoB,gCACpBE,iBAAkB,gCAClBC,eAAgB,kCAGpBe,OAAQd,EAAqB,CAC3BI,QAAS,CACPR,mBAAoB,mBACpBE,iBAAkB,mBAClBC,eAAgB,qBAGpBgB,MAAOf,EAAqB,CAC1BI,QAAS,CACPR,mBAAoB,iBACpBE,iBAAkB,gBAClBC,eAAgB,oBAGpBiB,YAAahB,EAAqB,CAChCI,QAAS,CACPR,mBAAoB,0BACpBE,iBAAkB,2BAClBC,eAAgB,4BAElBI,OAAQ,CACNP,mBAAoB,iCACpBE,iBAAkB,+BAClBC,eAAgB,mCAGpBkB,OAAQjB,EAAqB,CAC3BI,QAAS,CACPR,mBAAoB,oBACpBE,iBAAkB,kBAClBC,eAAgB,sBAGpBmB,aAAclB,EAAqB,CACjCI,QAAS,CACPR,mBAAoB,2BACpBE,iBAAkB,4BAClBC,eAAgB,6BAElBI,OAAQ,CACNP,mBAAoB,gCACpBE,iBAAkB,gCAClBC,eAAgB,oCAGpBoB,QAASnB,EAAqB,CAC5BI,QAAS,CACPR,mBAAoB,mBACpBE,iBAAkB,mBAClBC,eAAgB,uBAGpBqB,YAAapB,EAAqB,CAChCI,QAAS,CACPR,mBAAoB,yBACpBE,iBAAkB,0BAClBC,eAAgB,2BAElBI,OAAQ,CACNP,mBAAoB,6BACpBE,iBAAkB,8BAClBC,eAAgB,kCAGpBsB,OAAQrB,EAAqB,CAC3BI,QAAS,CACPR,mBAAoB,gBACpBE,iBAAkB,iBAClBC,eAAgB,qBAGpBuB,WAAYtB,EAAqB,CAC/BI,QAAS,CACPR,mBAAoB,wBACpBE,iBAAkB,yBAClBC,eAAgB,0BAElBI,OAAQ,CACNP,mBAAoB,+BACpBE,iBAAkB,gCAClBC,eAAgB,oCAGpBwB,aAAcvB,EAAqB,CACjCI,QAAS,CACPR,mBAAoB,sBACpBE,iBAAkB,uBAClBC,eAAgB,yBAElBI,OAAQ,CACNP,mBAAoB,yBACpBE,iBAAkB,0BAClBC,eAAgB,+BAUlB1D,EALiB,SAAwBC,EAAOiD,EAAO5D,GAEzD,OADAA,EAAUA,GAAW,CAAC,EACf2E,EAAqBhE,GAAOiD,EAAO5D,EAC5C,EAGAd,EAAA,QAAkBwB,EAClBG,EAAO3B,QAAUA,EAAQM,O,kBC7OzBR,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAASiC,EAAuB,EAAQ,QAExChC,EAAUgC,EAAuB,EAAQ,QAEzCuE,EAAUvE,EAAuB,EAAQ,QAEzCwE,EAAUxE,EAAuB,EAAQ,QAEzCyE,EAAUzE,EAAuB,EAAQ,QAE7C,SAASA,EAAuBlC,GAAO,OAAOA,GAAOA,EAAIG,WAAaH,EAAM,CAAEI,QAASJ,EAAO,CAW9F,IAcIsB,EAdS,CACXsF,KAAM,KACNC,eAAgB5G,EAAOG,QACvB0G,WAAY5G,EAAQE,QACpB2G,eAAgBN,EAAQrG,QACxB4G,SAAUN,EAAQtG,QAClB6G,MAAON,EAAQvG,QACfQ,QAAS,CACPsG,aAAc,EAGdC,sBAAuB,IAI3BrH,EAAA,QAAkBwB,EAClBG,EAAO3B,QAAUA,EAAQM,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/uk/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/locale/uk/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/uk/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/uk/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/uk/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/uk/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = require(\"../../../../index.js\");\n\nvar _index2 = _interopRequireDefault(require(\"../../../../_lib/isSameUTCWeek/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar accusativeWeekdays = ['неділю', 'понеділок', 'вівторок', 'середу', 'четвер', 'п’ятницю', 'суботу'];\n\nfunction lastWeek(day) {\n  var weekday = accusativeWeekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у минулу \" + weekday + \" о' p\";\n\n    case 1:\n    case 2:\n    case 4:\n      return \"'у минулий \" + weekday + \" о' p\";\n  }\n}\n\nfunction thisWeek(day) {\n  var weekday = accusativeWeekdays[day];\n  return \"'у \" + weekday + \" о' p\";\n}\n\nfunction nextWeek(day) {\n  var weekday = accusativeWeekdays[day];\n\n  switch (day) {\n    case 0:\n    case 3:\n    case 5:\n    case 6:\n      return \"'у наступну \" + weekday + \" о' p\";\n\n    case 1:\n    case 2:\n    case 4:\n      return \"'у наступний \" + weekday + \" о' p\";\n  }\n}\n\nvar lastWeekFormat = function lastWeekFormat(dirtyDate, baseDate, options) {\n  var date = (0, _index.toDate)(dirtyDate);\n  var day = date.getUTCDay();\n\n  if ((0, _index2.default)(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return lastWeek(day);\n  }\n};\n\nvar nextWeekFormat = function nextWeekFormat(dirtyDate, baseDate, options) {\n  var date = (0, _index.toDate)(dirtyDate);\n  var day = date.getUTCDay();\n\n  if ((0, _index2.default)(date, baseDate, options)) {\n    return thisWeek(day);\n  } else {\n    return nextWeek(day);\n  }\n};\n\nvar formatRelativeLocale = {\n  lastWeek: lastWeekFormat,\n  yesterday: \"'вчора о' p\",\n  today: \"'сьогодні о' p\",\n  tomorrow: \"'завтра о' p\",\n  nextWeek: nextWeekFormat,\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, date, baseDate, options) {\n  var format = formatRelativeLocale[token];\n\n  if (typeof format === 'function') {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: \"EEEE, do MMMM y 'р.'\",\n  long: \"do MMMM y 'р.'\",\n  medium: \"d MMM y 'р.'\",\n  short: 'dd.MM.y'\n};\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'о' {{time}}\",\n  long: \"{{date}} 'о' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)(-?(е|й|є|а|я))?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^((до )?н\\.?\\s?е\\.?)/i,\n  abbreviated: /^((до )?н\\.?\\s?е\\.?)/i,\n  wide: /^(до нашої ери|нашої ери|наша ера)/i\n};\nvar parseEraPatterns = {\n  any: [/^д/i, /^н/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234](-?[иі]?й?)? кв.?/i,\n  wide: /^[1234](-?[иі]?й?)? квартал/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[слбктчвжг]/i,\n  abbreviated: /^(січ|лют|бер(ез)?|квіт|трав|черв|лип|серп|вер(ес)?|жовт|лис(топ)?|груд)\\.?/i,\n  wide: /^(січень|січня|лютий|лютого|березень|березня|квітень|квітня|травень|травня|червня|червень|липень|липня|серпень|серпня|вересень|вересня|жовтень|жовтня|листопад[а]?|грудень|грудня)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^с/i, /^л/i, /^б/i, /^к/i, /^т/i, /^ч/i, /^л/i, /^с/i, /^в/i, /^ж/i, /^л/i, /^г/i],\n  any: [/^сі/i, /^лю/i, /^б/i, /^к/i, /^т/i, /^ч/i, /^лип/i, /^се/i, /^в/i, /^ж/i, /^лис/i, /^г/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[нпвсч]/i,\n  short: /^(нд|пн|вт|ср|чт|пт|сб)\\.?/i,\n  abbreviated: /^(нед|пон|вів|сер|че?тв|птн?|суб)\\.?/i,\n  wide: /^(неділ[яі]|понеділ[ок][ка]|вівтор[ок][ка]|серед[аи]|четвер(га)?|п\\W*?ятниц[яі]|субот[аи])/i\n};\nvar parseDayPatterns = {\n  narrow: [/^н/i, /^п/i, /^в/i, /^с/i, /^ч/i, /^п/i, /^с/i],\n  any: [/^н/i, /^п[он]/i, /^в/i, /^с[ер]/i, /^ч/i, /^п\\W*?[ят]/i, /^с[уб]/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n  abbreviated: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n  wide: /^([дп]п|північ|полудень|ранок|ранку|день|дня|вечір|вечора|ніч|ночі)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^дп/i,\n    pm: /^пп/i,\n    midnight: /^півн/i,\n    noon: /^пол/i,\n    morning: /^р/i,\n    afternoon: /^д[ен]/i,\n    evening: /^в/i,\n    night: /^н/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['до н.е.', 'н.е.'],\n  abbreviated: ['до н. е.', 'н. е.'],\n  wide: ['до нашої ери', 'нашої ери']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-й кв.', '2-й кв.', '3-й кв.', '4-й кв.'],\n  wide: ['1-й квартал', '2-й квартал', '3-й квартал', '4-й квартал']\n};\nvar monthValues = {\n  // ДСТУ 3582:2013\n  narrow: ['С', 'Л', 'Б', 'К', 'Т', 'Ч', 'Л', 'С', 'В', 'Ж', 'Л', 'Г'],\n  abbreviated: ['січ.', 'лют.', 'берез.', 'квіт.', 'трав.', 'черв.', 'лип.', 'серп.', 'верес.', 'жовт.', 'листоп.', 'груд.'],\n  wide: ['січень', 'лютий', 'березень', 'квітень', 'травень', 'червень', 'липень', 'серпень', 'вересень', 'жовтень', 'листопад', 'грудень']\n};\nvar formattingMonthValues = {\n  narrow: ['С', 'Л', 'Б', 'К', 'Т', 'Ч', 'Л', 'С', 'В', 'Ж', 'Л', 'Г'],\n  abbreviated: ['січ.', 'лют.', 'берез.', 'квіт.', 'трав.', 'черв.', 'лип.', 'серп.', 'верес.', 'жовт.', 'листоп.', 'груд.'],\n  wide: ['січня', 'лютого', 'березня', 'квітня', 'травня', 'червня', 'липня', 'серпня', 'вересня', 'жовтня', 'листопада', 'грудня']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],\n  abbreviated: ['нед', 'пон', 'вів', 'сер', 'чтв', 'птн', 'суб'],\n  wide: ['неділя', 'понеділок', 'вівторок', 'середа', 'четвер', 'п’ятниця', 'субота']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ніч'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ніч'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'північ',\n    noon: 'полудень',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'вечір',\n    night: 'ніч'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'північ',\n    noon: 'полудень',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  var number = Number(dirtyNumber);\n  var suffix;\n\n  if (unit === 'date') {\n    if (number === 3 || number === 23) {\n      suffix = '-є';\n    } else {\n      suffix = '-е';\n    }\n  } else if (unit === 'minute' || unit === 'second' || unit === 'hour') {\n    suffix = '-а';\n  } else {\n    suffix = '-й';\n  }\n\n  return number + suffix;\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nfunction declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n\n  var rem10 = count % 10;\n  var rem100 = count % 100; // 1, 21, 31, ...\n\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace('{{count}}', String(count)); // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace('{{count}}', String(count)); // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace('{{count}}', String(count));\n  }\n}\n\nfunction buildLocalizeTokenFn(scheme) {\n  return function (count, options) {\n    if (options && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return 'за ' + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + ' тому';\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\n\nvar halfAtMinute = function halfAtMinute(_, options) {\n  if (options && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'за півхвилини';\n    } else {\n      return 'півхвилини тому';\n    }\n  }\n\n  return 'півхвилини';\n};\n\nvar formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: 'менше секунди',\n      singularNominative: 'менше {{count}} секунди',\n      singularGenitive: 'менше {{count}} секунд',\n      pluralGenitive: 'менше {{count}} секунд'\n    },\n    future: {\n      one: 'менше, ніж за секунду',\n      singularNominative: 'менше, ніж за {{count}} секунду',\n      singularGenitive: 'менше, ніж за {{count}} секунди',\n      pluralGenitive: 'менше, ніж за {{count}} секунд'\n    }\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} секунда',\n      singularGenitive: '{{count}} секунди',\n      pluralGenitive: '{{count}} секунд'\n    },\n    past: {\n      singularNominative: '{{count}} секунду тому',\n      singularGenitive: '{{count}} секунди тому',\n      pluralGenitive: '{{count}} секунд тому'\n    },\n    future: {\n      singularNominative: 'за {{count}} секунду',\n      singularGenitive: 'за {{count}} секунди',\n      pluralGenitive: 'за {{count}} секунд'\n    }\n  }),\n  halfAMinute: halfAtMinute,\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: 'менше хвилини',\n      singularNominative: 'менше {{count}} хвилини',\n      singularGenitive: 'менше {{count}} хвилин',\n      pluralGenitive: 'менше {{count}} хвилин'\n    },\n    future: {\n      one: 'менше, ніж за хвилину',\n      singularNominative: 'менше, ніж за {{count}} хвилину',\n      singularGenitive: 'менше, ніж за {{count}} хвилини',\n      pluralGenitive: 'менше, ніж за {{count}} хвилин'\n    }\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} хвилина',\n      singularGenitive: '{{count}} хвилини',\n      pluralGenitive: '{{count}} хвилин'\n    },\n    past: {\n      singularNominative: '{{count}} хвилину тому',\n      singularGenitive: '{{count}} хвилини тому',\n      pluralGenitive: '{{count}} хвилин тому'\n    },\n    future: {\n      singularNominative: 'за {{count}} хвилину',\n      singularGenitive: 'за {{count}} хвилини',\n      pluralGenitive: 'за {{count}} хвилин'\n    }\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'близько {{count}} години',\n      singularGenitive: 'близько {{count}} годин',\n      pluralGenitive: 'близько {{count}} годин'\n    },\n    future: {\n      singularNominative: 'приблизно за {{count}} годину',\n      singularGenitive: 'приблизно за {{count}} години',\n      pluralGenitive: 'приблизно за {{count}} годин'\n    }\n  }),\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} годину',\n      singularGenitive: '{{count}} години',\n      pluralGenitive: '{{count}} годин'\n    }\n  }),\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} день',\n      singularGenitive: '{{count}} днi',\n      pluralGenitive: '{{count}} днів'\n    }\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'близько {{count}} тижня',\n      singularGenitive: 'близько {{count}} тижнів',\n      pluralGenitive: 'близько {{count}} тижнів'\n    },\n    future: {\n      singularNominative: 'приблизно за {{count}} тиждень',\n      singularGenitive: 'приблизно за {{count}} тижні',\n      pluralGenitive: 'приблизно за {{count}} тижнів'\n    }\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} тиждень',\n      singularGenitive: '{{count}} тижні',\n      pluralGenitive: '{{count}} тижнів'\n    }\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'близько {{count}} місяця',\n      singularGenitive: 'близько {{count}} місяців',\n      pluralGenitive: 'близько {{count}} місяців'\n    },\n    future: {\n      singularNominative: 'приблизно за {{count}} місяць',\n      singularGenitive: 'приблизно за {{count}} місяці',\n      pluralGenitive: 'приблизно за {{count}} місяців'\n    }\n  }),\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} місяць',\n      singularGenitive: '{{count}} місяці',\n      pluralGenitive: '{{count}} місяців'\n    }\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'близько {{count}} року',\n      singularGenitive: 'близько {{count}} років',\n      pluralGenitive: 'близько {{count}} років'\n    },\n    future: {\n      singularNominative: 'приблизно за {{count}} рік',\n      singularGenitive: 'приблизно за {{count}} роки',\n      pluralGenitive: 'приблизно за {{count}} років'\n    }\n  }),\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} рік',\n      singularGenitive: '{{count}} роки',\n      pluralGenitive: '{{count}} років'\n    }\n  }),\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'більше {{count}} року',\n      singularGenitive: 'більше {{count}} років',\n      pluralGenitive: 'більше {{count}} років'\n    },\n    future: {\n      singularNominative: 'більше, ніж за {{count}} рік',\n      singularGenitive: 'більше, ніж за {{count}} роки',\n      pluralGenitive: 'більше, ніж за {{count}} років'\n    }\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'майже {{count}} рік',\n      singularGenitive: 'майже {{count}} роки',\n      pluralGenitive: 'майже {{count}} років'\n    },\n    future: {\n      singularNominative: 'майже за {{count}} рік',\n      singularGenitive: 'майже за {{count}} роки',\n      pluralGenitive: 'майже за {{count}} років'\n    }\n  })\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  options = options || {};\n  return formatDistanceLocale[token](count, options);\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Ukrainian locale.\n * @language Ukrainian\n * @iso-639-2 ukr\n * <AUTHOR> [@korzhyk]{@link https://github.com/korzhyk}\n * <AUTHOR> [@shcherbyakdev]{@link https://github.com/shcherbyakdev}\n */\nvar locale = {\n  code: 'uk',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 1\n    /* Monday */\n    ,\n    firstWeekContainsDate: 1\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "_index2", "__esModule", "default", "accusativeWeekdays", "thisWeek", "day", "formatRelativeLocale", "lastWeek", "dirtyDate", "baseDate", "options", "date", "toDate", "getUTCDay", "weekday", "yesterday", "today", "tomorrow", "nextWeek", "other", "_default", "token", "format", "module", "formats", "full", "long", "medium", "short", "defaultWidth", "time", "dateTime", "_interopRequireDefault", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "parseInt", "era", "matchPatterns", "narrow", "abbreviated", "wide", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "quarter", "index", "month", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "dirtyNumber", "unit", "String", "number", "Number", "values", "argument<PERSON>allback", "formattingValues", "defaultFormattingWidth", "declension", "scheme", "count", "undefined", "one", "rem10", "rem100", "singularNominative", "replace", "singularGenitive", "pluralGenitive", "buildLocalizeTokenFn", "addSuffix", "comparison", "future", "regular", "past", "formatDistanceLocale", "lessThanXSeconds", "xSeconds", "halfAMinute", "_", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "weekStartsOn", "firstWeekContainsDate"], "sourceRoot": ""}
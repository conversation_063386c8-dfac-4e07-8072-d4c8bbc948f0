{"version": 3, "file": "93474.dtale_bundle.js", "mappings": "8HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAgCII,EAda,CACfC,MAAM,EAAIJ,EAAOE,SAAS,CACxBG,QApBc,CAChBC,KAAM,oCACNC,KAAM,uBACNC,OAAQ,UACRC,MAAO,YAiBLC,aAAc,SAEhBC,MAAM,EAAIX,EAAOE,SAAS,CACxBG,QAlBc,CAChBC,KAAM,gBACNC,KAAM,aACNC,OAAQ,WACRC,MAAO,SAeLC,aAAc,SAEhBE,UAAU,EAAIZ,EAAOE,SAAS,CAC5BG,QAhBkB,CACpBC,KAAM,0BACNC,KAAM,0BACNC,OAAQ,qBACRC,MAAO,sBAaLC,aAAc,UAIlBb,EAAA,QAAkBM,EAClBU,EAAOhB,QAAUA,EAAQK,O,kBC3CzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAASc,EAAuB,EAAQ,QAExCC,EAAUD,EAAuB,EAAQ,QAEzCE,EAAUF,EAAuB,EAAQ,QAEzCG,EAAUH,EAAuB,EAAQ,QAEzCI,EAAUJ,EAAuB,EAAQ,QAE7C,SAASA,EAAuBf,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAU9F,IAcII,EAdS,CACXgB,KAAM,KACNC,eAAgBpB,EAAOE,QACvBmB,WAAYN,EAAQb,QACpBoB,eAAgBN,EAAQd,QACxBqB,SAAUN,EAAQf,QAClBsB,MAAON,EAAQhB,QACfuB,QAAS,CACPC,aAAc,EAGdC,sBAAuB,IAI3B9B,EAAA,QAAkBM,EAClBU,EAAOhB,QAAUA,EAAQK,O,gBCzCzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAI+B,EAAuB,CACzBC,SAAU,sBACVC,UAAW,YACXC,MAAO,YACPC,SAAU,aACVC,SAAU,UACVC,MAAO,KAELC,EAA6B,CAC/BN,SAAU,qBACVC,UAAW,YACXC,MAAO,YACPC,SAAU,aACVC,SAAU,UACVC,MAAO,KAWL/B,EARiB,SAAwBiC,EAAOhC,GAClD,OAA2B,IAAvBA,EAAKiC,cACAF,EAA2BC,GAG7BR,EAAqBQ,EAC9B,EAGAvC,EAAA,QAAkBM,EAClBU,EAAOhB,QAAUA,EAAQK,O,kBC/BzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAASc,EAAuB,EAAQ,QAI5C,SAASA,EAAuBf,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAE9F,IA6GII,EA1CQ,CACVmC,eAAe,EAxEHxB,EAAuB,EAAQ,MAwEhBZ,SAAS,CAClCqC,aArE4B,cAsE5BC,aArE4B,OAsE5BC,cAAe,SAAuB3C,GACpC,OAAO4C,SAAS5C,EAAO,GACzB,IAEF6C,KAAK,EAAI3C,EAAOE,SAAS,CACvB0C,cA1EmB,CACrBC,OAAQ,gBACRC,YAAa,gBACbC,KAAM,sCAwEJC,kBAAmB,OACnBC,cAvEmB,CACrBJ,OAAQ,CAAC,SAAU,UACnBC,YAAa,CAAC,WAAY,YAC1BC,KAAM,CAAC,sBAAuB,uBAqE5BG,kBAAmB,SAErBC,SAAS,EAAInD,EAAOE,SAAS,CAC3B0C,cAtEuB,CACzBC,OAAQ,WACRC,YAAa,YACbC,KAAM,4BAoEJC,kBAAmB,OACnBC,cAnEuB,CACzBG,IAAK,CAAC,KAAM,KAAM,KAAM,OAmEtBF,kBAAmB,MACnBT,cAAe,SAAuBY,GACpC,OAAOA,EAAQ,CACjB,IAEFC,OAAO,EAAItD,EAAOE,SAAS,CACzB0C,cAvEqB,CACvBC,OAAQ,aACRC,YAAa,sDACbC,KAAM,oGAqEJC,kBAAmB,OACnBC,cApEqB,CACvBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACtFO,IAAK,CAAC,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,UAmEvGF,kBAAmB,QAErBK,KAAK,EAAIvD,EAAOE,SAAS,CACvB0C,cApEmB,CACrBC,OAAQ,WACRpC,MAAO,2BACPqC,YAAa,kCACbC,KAAM,2EAiEJC,kBAAmB,OACnBC,cAhEmB,CACrBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACnDpC,MAAO,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QACxDqC,YAAa,CAAC,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,SACpEC,KAAM,CAAC,YAAa,eAAgB,cAAe,eAAgB,aAAc,aAAc,gBA6D7FG,kBAAmB,SAErBM,WAAW,EAAIxD,EAAOE,SAAS,CAC7B0C,cA9DyB,CAC3BC,OAAQ,4DACRO,IAAK,oFA6DHJ,kBAAmB,MACnBC,cA5DyB,CAC3BJ,OAAQ,CACNY,GAAI,MACJC,GAAI,MACJC,SAAU,OACVC,KAAM,OACNC,QAAS,QACTC,UAAW,eACXC,QAAS,eACTC,MAAO,QAETZ,IAAK,CACHK,GAAI,MACJC,GAAI,MACJC,SAAU,aACVC,KAAM,aACNC,QAAS,QACTC,UAAW,eACXC,QAAS,eACTC,MAAO,SA0CPd,kBAAmB,SAIvBrD,EAAA,QAAkBM,EAClBU,EAAOhB,QAAUA,EAAQK,O,gBC1HzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIoE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,6BACLjC,MAAO,oCAETkC,SAAU,CACRD,IAAK,YACLjC,MAAO,qBAETmC,YAAa,cACbC,iBAAkB,CAChBH,IAAK,4BACLjC,MAAO,mCAETqC,SAAU,CACRJ,IAAK,WACLjC,MAAO,oBAETsC,YAAa,CACXL,IAAK,0BACLjC,MAAO,mCAETuC,OAAQ,CACNN,IAAK,SACLjC,MAAO,kBAETwC,MAAO,CACLP,IAAK,SACLjC,MAAO,kBAETyC,YAAa,CACXR,IAAK,gBACLjC,MAAO,yBAET0C,OAAQ,CACNT,IAAK,SACLjC,MAAO,oBAET2C,aAAc,CACZV,IAAK,8BACLjC,MAAO,uCAET4C,QAAS,CACPX,IAAK,aACLjC,MAAO,sBAET6C,YAAa,CACXZ,IAAK,0BACLjC,MAAO,mCAET8C,OAAQ,CACNb,IAAK,SACLjC,MAAO,kBAET+C,WAAY,CACVd,IAAK,uBACLjC,MAAO,gCAETgD,aAAc,CACZf,IAAK,YACLjC,MAAO,sBA2BP/B,EAvBiB,SAAwBiC,EAAO+C,EAAO1D,GACzD,IAAI2D,EACAC,EAAapB,EAAqB7B,GAUtC,OAPEgD,EADwB,iBAAfC,EACAA,EACU,IAAVF,EACAE,EAAWlB,IAEXkB,EAAWnD,MAAMoD,QAAQ,YAAaC,OAAOJ,IAGpD1D,SAA0CA,EAAQ+D,UAChD/D,EAAQgE,YAAchE,EAAQgE,WAAa,EACtC,MAAQL,EAER,SAAWA,EAIfA,CACT,EAGAvF,EAAA,QAAkBM,EAClBU,EAAOhB,QAAUA,EAAQK,O,kBC7FzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAuHII,EA5BW,CACbmC,cANkB,SAAuBoD,EAAaC,GAEtD,OADaC,OAAOF,GACJ,GAClB,EAIE/C,KAAK,EAAI3C,EAAOE,SAAS,CACvB2F,OA9FY,CACdhD,OAAQ,CAAC,OAAQ,QACjBC,YAAa,CAAC,OAAQ,QACtBC,KAAM,CAAC,kBAAmB,mBA4FxBrC,aAAc,SAEhByC,SAAS,EAAInD,EAAOE,SAAS,CAC3B2F,OA7FgB,CAClBhD,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,KAAM,KAAM,KAAM,MAChCC,KAAM,CAAC,iBAAkB,iBAAkB,iBAAkB,mBA2F3DrC,aAAc,OACdoF,iBAAkB,SAA0B3C,GAC1C,OAAOA,EAAU,CACnB,IAEFG,OAAO,EAAItD,EAAOE,SAAS,CACzB2F,OA/Fc,CAChBhD,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAC3FC,KAAM,CAAC,YAAa,UAAW,UAAW,UAAW,UAAW,SAAU,UAAW,UAAW,SAAU,QAAS,SAAU,YA6F3HrC,aAAc,SAEhB6C,KAAK,EAAIvD,EAAOE,SAAS,CACvB2F,OA9FY,CACdhD,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCpC,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CqC,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACxDC,KAAM,CAAC,UAAW,aAAc,YAAa,aAAc,WAAY,WAAY,cA2FjFrC,aAAc,SAEhB8C,WAAW,EAAIxD,EAAOE,SAAS,CAC7B2F,OA5FkB,CACpBhD,OAAQ,CACNY,GAAI,IACJC,GAAI,IACJC,SAAU,KACVC,KAAM,KACNC,QAAS,QACTC,UAAW,cACXC,QAAS,cACTC,MAAO,QAETlB,YAAa,CACXW,GAAI,KACJC,GAAI,KACJC,SAAU,WACVC,KAAM,WACNC,QAAS,QACTC,UAAW,cACXC,QAAS,cACTC,MAAO,QAETjB,KAAM,CACJU,GAAI,OACJC,GAAI,OACJC,SAAU,WACVC,KAAM,WACNC,QAAS,QACTC,UAAW,cACXC,QAAS,cACTC,MAAO,SAgEPtD,aAAc,OACdqF,iBA9D4B,CAC9BlD,OAAQ,CACNY,GAAI,IACJC,GAAI,IACJC,SAAU,KACVC,KAAM,KACNC,QAAS,UACTC,UAAW,eACXC,QAAS,eACTC,MAAO,UAETlB,YAAa,CACXW,GAAI,KACJC,GAAI,KACJC,SAAU,WACVC,KAAM,WACNC,QAAS,UACTC,UAAW,eACXC,QAAS,eACTC,MAAO,UAETjB,KAAM,CACJU,GAAI,OACJC,GAAI,OACJC,SAAU,WACVC,KAAM,WACNC,QAAS,UACTC,UAAW,eACXC,QAAS,eACTC,MAAO,WAkCPgC,uBAAwB,UAI5BnG,EAAA,QAAkBM,EAClBU,EAAOhB,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/eu/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/eu/index.js", "webpack://dtale/./node_modules/date-fns/locale/eu/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/locale/eu/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/eu/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/eu/_lib/localize/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: \"EEEE, y'ko' MMMM'ren' d'a' y'ren'\",\n  long: \"y'ko' MMMM'ren' d'a'\",\n  medium: 'y MMM d',\n  short: 'yy/MM/dd'\n};\nvar timeFormats = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'tan' {{time}}\",\n  long: \"{{date}} 'tan' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Basque locale.\n * @language Basque\n * @iso-639-2 eus\n * <AUTHOR> [@JacobSoderblom]{@link https://github.com/JacobSoderblom}\n */\nvar locale = {\n  code: 'eu',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 1\n    /* Monday */\n    ,\n    firstWeekContainsDate: 1\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"'joan den' eeee, LT\",\n  yesterday: \"'atzo,' p\",\n  today: \"'gaur,' p\",\n  tomorrow: \"'bihar,' p\",\n  nextWeek: 'eeee, p',\n  other: 'P'\n};\nvar formatRelativeLocalePlural = {\n  lastWeek: \"'joan den' eeee, p\",\n  yesterday: \"'atzo,' p\",\n  today: \"'gaur,' p\",\n  tomorrow: \"'bihar,' p\",\n  nextWeek: 'eeee, p',\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, date) {\n  if (date.getUTCHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)(.)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(k.a.|k.o.)/i,\n  abbreviated: /^(k.a.|k.o.)/i,\n  wide: /^(kristo aurretik|kristo ondoren)/i\n};\nvar parseEraPatterns = {\n  narrow: [/^k.a./i, /^k.o./i],\n  abbreviated: [/^(k.a.)/i, /^(k.o.)/i],\n  wide: [/^(kristo aurretik)/i, /^(kristo ondoren)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234]H/i,\n  wide: /^[1234](.)? hiruhilekoa/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[uomaei]/i,\n  abbreviated: /^(urt|ots|mar|api|mai|eka|uzt|abu|ira|urr|aza|abe)/i,\n  wide: /^(urtarrila|otsaila|martxoa|apirila|maiatza|ekaina|uztaila|abuztua|iraila|urria|azaroa|abendua)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^u/i, /^o/i, /^m/i, /^a/i, /^m/i, /^e/i, /^u/i, /^a/i, /^i/i, /^u/i, /^a/i, /^a/i],\n  any: [/^urt/i, /^ots/i, /^mar/i, /^api/i, /^mai/i, /^eka/i, /^uzt/i, /^abu/i, /^ira/i, /^urr/i, /^aza/i, /^abe/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[iaol]/i,\n  short: /^(ig|al|as|az|og|or|lr)/i,\n  abbreviated: /^(iga|ast|ast|ast|ost|ost|lar)/i,\n  wide: /^(igandea|astelehena|asteartea|asteazkena|osteguna|ostirala|larunbata)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^i/i, /^a/i, /^a/i, /^a/i, /^o/i, /^o/i, /^l/i],\n  short: [/^ig/i, /^al/i, /^as/i, /^az/i, /^og/i, /^or/i, /^lr/i],\n  abbreviated: [/^iga/i, /^ast/i, /^ast/i, /^ast/i, /^ost/i, /^ost/i, /^lar/i],\n  wide: [/^igandea/i, /^astelehena/i, /^asteartea/i, /^asteazkena/i, /^osteguna/i, /^ostirala/i, /^larunbata/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(a|p|ge|eg|((goiza|goizean)|arratsaldea|(gaua|gauean)))/i,\n  any: /^([ap]\\.?\\s?m\\.?|gauerdia|eguerdia|((goiza|goizean)|arratsaldea|(gaua|gauean)))/i\n};\nvar parseDayPeriodPatterns = {\n  narrow: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^ge/i,\n    noon: /^eg/i,\n    morning: /goiz/i,\n    afternoon: /arratsaldea/i,\n    evening: /arratsaldea/i,\n    night: /gau/i\n  },\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^gauerdia/i,\n    noon: /^eguerdia/i,\n    morning: /goiz/i,\n    afternoon: /arratsaldea/i,\n    evening: /arratsaldea/i,\n    night: /gau/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'segundo bat baino gutxiago',\n    other: '{{count}} segundo baino gutxiago'\n  },\n  xSeconds: {\n    one: '1 segundo',\n    other: '{{count}} segundo'\n  },\n  halfAMinute: 'minutu erdi',\n  lessThanXMinutes: {\n    one: 'minutu bat baino gutxiago',\n    other: '{{count}} minutu baino gutxiago'\n  },\n  xMinutes: {\n    one: '1 minutu',\n    other: '{{count}} minutu'\n  },\n  aboutXHours: {\n    one: '1 ordu gutxi gorabehera',\n    other: '{{count}} ordu gutxi gorabehera'\n  },\n  xHours: {\n    one: '1 ordu',\n    other: '{{count}} ordu'\n  },\n  xDays: {\n    one: '1 egun',\n    other: '{{count}} egun'\n  },\n  aboutXWeeks: {\n    one: 'aste 1 inguru',\n    other: '{{count}} aste inguru'\n  },\n  xWeeks: {\n    one: '1 aste',\n    other: '{{count}} astean'\n  },\n  aboutXMonths: {\n    one: '1 hilabete gutxi gorabehera',\n    other: '{{count}} hilabete gutxi gorabehera'\n  },\n  xMonths: {\n    one: '1 hilabete',\n    other: '{{count}} hilabete'\n  },\n  aboutXYears: {\n    one: '1 urte gutxi gorabehera',\n    other: '{{count}} urte gutxi gorabehera'\n  },\n  xYears: {\n    one: '1 urte',\n    other: '{{count}} urte'\n  },\n  overXYears: {\n    one: '1 urte baino gehiago',\n    other: '{{count}} urte baino gehiago'\n  },\n  almostXYears: {\n    one: 'ia 1 urte',\n    other: 'ia {{count}} urte'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'en ' + result;\n    } else {\n      return 'duela ' + result;\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['k.a.', 'k.o.'],\n  abbreviated: ['k.a.', 'k.o.'],\n  wide: ['kristo aurretik', 'kristo ondoren']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1H', '2H', '3H', '4H'],\n  wide: ['1. hiruhilekoa', '2. hiruhilekoa', '3. hiruhilekoa', '4. hiruhilekoa']\n};\nvar monthValues = {\n  narrow: ['u', 'o', 'm', 'a', 'm', 'e', 'u', 'a', 'i', 'u', 'a', 'a'],\n  abbreviated: ['urt', 'ots', 'mar', 'api', 'mai', 'eka', 'uzt', 'abu', 'ira', 'urr', 'aza', 'abe'],\n  wide: ['urtarrila', 'otsaila', 'martxoa', 'apirila', 'maiatza', 'ekaina', 'uztaila', 'abuztua', 'iraila', 'urria', 'azaroa', 'abendua']\n};\nvar dayValues = {\n  narrow: ['i', 'a', 'a', 'a', 'o', 'o', 'l'],\n  short: ['ig', 'al', 'as', 'az', 'og', 'or', 'lr'],\n  abbreviated: ['iga', 'ast', 'ast', 'ast', 'ost', 'ost', 'lar'],\n  wide: ['igandea', 'astelehena', 'asteartea', 'asteazkena', 'osteguna', 'ostirala', 'larunbata']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'ge',\n    noon: 'eg',\n    morning: 'goiza',\n    afternoon: 'arratsaldea',\n    evening: 'arratsaldea',\n    night: 'gaua'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'gauerdia',\n    noon: 'eguerdia',\n    morning: 'goiza',\n    afternoon: 'arratsaldea',\n    evening: 'arratsaldea',\n    night: 'gaua'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'gauerdia',\n    noon: 'eguerdia',\n    morning: 'goiza',\n    afternoon: 'arratsaldea',\n    evening: 'arratsaldea',\n    night: 'gaua'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'ge',\n    noon: 'eg',\n    morning: 'goizean',\n    afternoon: 'arratsaldean',\n    evening: 'arratsaldean',\n    night: 'gauean'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'gauerdia',\n    noon: 'eguerdia',\n    morning: 'goizean',\n    afternoon: 'arratsaldean',\n    evening: 'arratsaldean',\n    night: 'gauean'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'gauerdia',\n    noon: 'eguerdia',\n    morning: 'goizean',\n    afternoon: 'arratsaldean',\n    evening: 'arratsaldean',\n    night: 'gauean'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "date", "formats", "full", "long", "medium", "short", "defaultWidth", "time", "dateTime", "module", "_interopRequireDefault", "_index2", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "options", "weekStartsOn", "firstWeekContainsDate", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelativeLocalePlural", "token", "getUTCHours", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "parseInt", "era", "matchPatterns", "narrow", "abbreviated", "wide", "defaultMatchWidth", "parsePatterns", "defaultParseWidth", "quarter", "any", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formatDistanceLocale", "lessThanXSeconds", "one", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "count", "result", "tokenValue", "replace", "String", "addSuffix", "comparison", "dirtyNumber", "_options", "Number", "values", "argument<PERSON>allback", "formattingValues", "defaultFormattingWidth"], "sourceRoot": ""}
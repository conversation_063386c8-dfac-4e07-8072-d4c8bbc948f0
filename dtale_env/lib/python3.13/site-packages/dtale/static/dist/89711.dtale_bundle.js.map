{"version": 3, "file": "89711.dtale_bundle.js", "mappings": "8HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAuGII,EA9BW,CACbC,cALkB,SAAuBC,EAAaC,GACtD,OAAOC,OAAOF,EAChB,EAIEG,KAAK,EAAIR,EAAOE,SAAS,CACvBO,OA5EY,CACdC,OAAQ,CAAC,MAAO,MAChBC,YAAa,CAAC,MAAO,MACrBC,KAAM,CAAC,wBAAyB,oBA0E9BC,aAAc,SAEhBC,SAAS,EAAId,EAAOE,SAAS,CAC3BO,OA3EgB,CAClBC,OAAQ,CAAC,IAAK,KAAM,MAAO,MAC3BC,YAAa,CAAC,WAAY,YAAa,aAAc,aACrDC,KAAM,CAAC,aAAc,aAAc,aAAc,eAyE/CC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAIhB,EAAOE,SAAS,CACzBO,OAzEc,CAChBC,OAAQ,CAAC,IAAK,KAAM,MAAO,KAAM,IAAK,KAAM,MAAO,OAAQ,KAAM,IAAK,KAAM,OAC5EC,YAAa,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAAY,WAAY,YACzIC,KAAM,CAAC,gBAAiB,iBAAkB,kBAAmB,kBAAmB,gBAAiB,mBAAoB,kBAAmB,iBAAkB,eAAgB,iBAAkB,qBAAsB,yBAuEhNC,aAAc,OACdI,iBAtEwB,CAC1BP,OAAQ,CAAC,IAAK,KAAM,MAAO,KAAM,IAAK,KAAM,MAAO,OAAQ,KAAM,IAAK,KAAM,OAC5EC,YAAa,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAAY,WAAY,YACzIC,KAAM,CAAC,gBAAiB,iBAAkB,kBAAmB,kBAAmB,gBAAiB,mBAAoB,kBAAmB,iBAAkB,eAAgB,iBAAkB,qBAAsB,yBAoEhNM,uBAAwB,SAE1BC,KAAK,EAAInB,EAAOE,SAAS,CACvBO,OArEY,CACdC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCU,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CT,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACxDC,KAAM,CAAC,MAAO,QAAS,SAAU,SAAU,QAAS,SAAU,UAkE5DC,aAAc,OACdI,iBAjEsB,CACxBP,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCU,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CT,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACxDC,KAAM,CAAC,MAAO,QAAS,SAAU,SAAU,QAAS,SAAU,UA8D5DM,uBAAwB,SAE1BG,WAAW,EAAIrB,EAAOE,SAAS,CAC7BO,OA/DkB,CACpBC,OAAQ,CACNY,GAAI,OACJC,GAAI,OACJC,SAAU,YACVC,KAAM,UACNC,QAAS,QACTC,UAAW,OACXC,QAAS,OACTC,MAAO,QAETlB,YAAa,CACXW,GAAI,OACJC,GAAI,OACJC,SAAU,YACVC,KAAM,UACNC,QAAS,QACTC,UAAW,OACXC,QAAS,OACTC,MAAO,QAETjB,KAAM,CACJU,GAAI,OACJC,GAAI,OACJC,SAAU,YACVC,KAAM,UACNC,QAAS,QACTC,UAAW,OACXC,QAAS,OACTC,MAAO,SAmCPhB,aAAc,UAIlBhB,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,kBClHzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAAS+B,EAAuB,EAAQ,QAExCC,EAAUD,EAAuB,EAAQ,QAEzCE,EAAUF,EAAuB,EAAQ,QAEzCG,EAAUH,EAAuB,EAAQ,QAEzCI,EAAUJ,EAAuB,EAAQ,QAE7C,SAASA,EAAuBhC,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAU9F,IAcII,EAdS,CACXiC,KAAM,KACNC,eAAgBrC,EAAOE,QACvBoC,WAAYN,EAAQ9B,QACpBqC,eAAgBN,EAAQ/B,QACxBsC,SAAUN,EAAQhC,QAClBuC,MAAON,EAAQjC,QACfwC,QAAS,CACPC,aAAc,EAGdC,sBAAuB,IAI3B/C,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,gBCzCzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIgD,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,kBACLC,MAAO,6BAETC,SAAU,CACRF,IAAK,WACLC,MAAO,oBAETE,YAAa,cACbC,iBAAkB,CAChBJ,IAAK,iBACLC,MAAO,4BAETI,SAAU,CACRL,IAAK,UACLC,MAAO,mBAETK,YAAa,CACXN,IAAK,qBACLC,MAAO,8BAETM,OAAQ,CACNP,IAAK,QACLC,MAAO,iBAETO,MAAO,CACLR,IAAK,SACLC,MAAO,kBAETQ,YAAa,CACXT,IAAK,6BACLC,MAAO,sCAETS,OAAQ,CACNV,IAAK,gBACLC,MAAO,yBAETU,aAAc,CACZX,IAAK,qBACLC,MAAO,8BAETW,QAAS,CACPZ,IAAK,QACLC,MAAO,iBAETY,YAAa,CACXb,IAAK,qBACLC,MAAO,8BAETa,OAAQ,CACNd,IAAK,QACLC,MAAO,iBAETc,WAAY,CACVf,IAAK,cACLC,MAAO,uBAETe,aAAc,CACZhB,IAAK,cACLC,MAAO,wBA2EP7C,EAvEiB,SAAwB6D,EAAOC,EAAOvB,GACzD,IAAIwB,EACAC,EAAatB,EAAqBmB,GAUtC,GAPEE,EADwB,iBAAfC,EACAA,EACU,IAAVF,EACAE,EAAWpB,IAEXoB,EAAWnB,MAAMoB,QAAQ,YAAa7D,OAAO0D,IAGpDvB,SAA0CA,EAAQ2B,UAAW,CAI/D,IAAIC,EAAQJ,EAAOK,MAAM,KACrBC,EAAWF,EAAMG,MAGrB,OAFAP,EAASI,EAAMI,KAAK,KAEZF,GACN,IAAK,SACHN,GAAU,aACV,MAEF,IAAK,QACHA,GAAU,WACV,MAEF,IAAK,MACHA,GAAU,UACV,MAEF,IAAK,OACHA,GAAU,UACV,MAEF,IAAK,MACHA,GAAU,SACV,MAEF,IAAK,MACHA,GAAU,UACV,MAEF,IAAK,QACHA,GAAU,YACV,MAEF,IAAK,QACHA,GAAU,UACV,MAEF,IAAK,WACHA,GAAU,sBACV,MAEF,QACEA,GAAUM,EAAW,KAGzB,OAAI9B,EAAQiC,YAAcjC,EAAQiC,WAAa,EACtCT,EAAS,SAETA,EAAS,OAEpB,CAEA,OAAOA,CACT,EAGArE,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,kBC7IzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAgCII,EAda,CACfyE,MAAM,EAAI5E,EAAOE,SAAS,CACxB2E,QApBc,CAChBC,KAAM,mCACNC,KAAM,qBACNC,OAAQ,oBACR5D,MAAO,WAiBLP,aAAc,SAEhBoE,MAAM,EAAIjF,EAAOE,SAAS,CACxB2E,QAlBc,CAChBC,KAAM,eACNC,KAAM,YACNC,OAAQ,UACR5D,MAAO,QAeLP,aAAc,SAEhBqE,UAAU,EAAIlF,EAAOE,SAAS,CAC5B2E,QAhBkB,CACpBC,KAAM,oBACNC,KAAM,oBACNC,OAAQ,oBACR5D,MAAO,qBAaLP,aAAc,UAIlBhB,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,kBC3CzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAAS+B,EAAuB,EAAQ,QAI5C,SAASA,EAAuBhC,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAE9F,IA+FII,EA1CQ,CACVC,eAAe,EA1DH2B,EAAuB,EAAQ,MA0DhB7B,SAAS,CAClCiF,aAvD4B,OAwD5BC,aAvD4B,OAwD5BC,cAAe,SAAuBvF,GACpC,OAAOwF,SAASxF,EAAO,GACzB,IAEFU,KAAK,EAAIR,EAAOE,SAAS,CACvBqF,cA5DmB,CACrB7E,OAAQ,aACRC,YAAa,aACbC,KAAM,4CA0DJ4E,kBAAmB,OACnBC,cAzDmB,CACrBC,IAAK,CAAC,+BAAgC,2BAyDpCC,kBAAmB,QAErB7E,SAAS,EAAId,EAAOE,SAAS,CAC3BqF,cA1DuB,CACzB7E,OAAQ,kBACRC,YAAa,yBACbC,KAAM,oBAwDJ4E,kBAAmB,OACnBC,cAvDuB,CACzBC,IAAK,CAAC,gBAAiB,iBAAkB,kBAAmB,mBAuD1DC,kBAAmB,MACnBN,cAAe,SAAuBO,GACpC,OAAOA,EAAQ,CACjB,IAEF5E,OAAO,EAAIhB,EAAOE,SAAS,CACzBqF,cA3DqB,CACvB7E,OAAQ,4CACRC,YAAa,yGACbC,KAAM,uMAyDJ4E,kBAAmB,OACnBC,cAxDqB,CACvB/E,OAAQ,CAAC,OAAQ,QAAS,SAAU,QAAS,OAAQ,QAAS,SAAU,UAAW,QAAS,OAAQ,QAAS,UAC7GgF,IAAK,CAAC,kBAAmB,mBAAoB,oBAAqB,oBAAqB,kBAAmB,qBAAsB,oBAAqB,mBAAoB,iBAAkB,oBAAqB,yBAA0B,4BAuDxOC,kBAAmB,QAErBxE,KAAK,EAAInB,EAAOE,SAAS,CACvBqF,cAxDmB,CACrB7E,OAAQ,cACRU,MAAO,2BACPT,YAAa,kCACbC,KAAM,kDAqDJ4E,kBAAmB,OACnBC,cApDmB,CACrB/E,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACnDgF,IAAK,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,SAmDpDC,kBAAmB,QAErBtE,WAAW,EAAIrB,EAAOE,SAAS,CAC7BqF,cApDyB,CAC3B7E,OAAQ,2DACRgF,IAAK,4DAmDHF,kBAAmB,MACnBC,cAlDyB,CAC3BC,IAAK,CACHpE,GAAI,WACJC,GAAI,WACJC,SAAU,cACVC,KAAM,YACNC,QAAS,SACTC,UAAW,QACXC,QAAS,QACTC,MAAO,UA0CP8D,kBAAmB,SAIvB9F,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,gBC5GzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIgG,EAAuB,CACzBC,SAAU,sCACVC,UAAW,qBACXC,MAAO,qBACPC,SAAU,qBACVC,SAAU,kCACVlD,MAAO,KAOL7C,EAJiB,SAAwB6D,EAAOmC,EAAOC,EAAW9F,GACpE,OAAOuF,EAAqB7B,EAC9B,EAGAnE,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/mn/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/mn/index.js", "webpack://dtale/./node_modules/date-fns/locale/mn/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/mn/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/mn/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/mn/_lib/formatRelative/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['НТӨ', 'НТ'],\n  abbreviated: ['НТӨ', 'НТ'],\n  wide: ['нийтийн тооллын өмнөх', 'нийтийн тооллын']\n};\nvar quarterValues = {\n  narrow: ['I', 'II', 'III', 'IV'],\n  abbreviated: ['I улирал', 'II улирал', 'III улирал', 'IV улирал'],\n  wide: ['1-р улирал', '2-р улирал', '3-р улирал', '4-р улирал']\n}; // Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n\nvar monthValues = {\n  narrow: ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'],\n  abbreviated: ['1-р сар', '2-р сар', '3-р сар', '4-р сар', '5-р сар', '6-р сар', '7-р сар', '8-р сар', '9-р сар', '10-р сар', '11-р сар', '12-р сар'],\n  wide: ['Нэгдүгээр сар', 'Хоёрдугаар сар', 'Гуравдугаар сар', 'Дөрөвдүгээр сар', 'Тавдугаар сар', 'Зургаадугаар сар', 'Долоодугаар сар', 'Наймдугаар сар', 'Есдүгээр сар', 'Аравдугаар сар', 'Арваннэгдүгээр сар', 'Арван хоёрдугаар сар']\n};\nvar formattingMonthValues = {\n  narrow: ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII'],\n  abbreviated: ['1-р сар', '2-р сар', '3-р сар', '4-р сар', '5-р сар', '6-р сар', '7-р сар', '8-р сар', '9-р сар', '10-р сар', '11-р сар', '12-р сар'],\n  wide: ['нэгдүгээр сар', 'хоёрдугаар сар', 'гуравдугаар сар', 'дөрөвдүгээр сар', 'тавдугаар сар', 'зургаадугаар сар', 'долоодугаар сар', 'наймдугаар сар', 'есдүгээр сар', 'аравдугаар сар', 'арваннэгдүгээр сар', 'арван хоёрдугаар сар']\n};\nvar dayValues = {\n  narrow: ['Н', 'Д', 'М', 'Л', 'П', 'Б', 'Б'],\n  short: ['Ня', 'Да', 'Мя', 'Лх', 'Пү', 'Ба', 'Бя'],\n  abbreviated: ['Ням', 'Дав', 'Мяг', 'Лха', 'Пүр', 'Баа', 'Бям'],\n  wide: ['Ням', 'Даваа', 'Мягмар', 'Лхагва', 'Пүрэв', 'Баасан', 'Бямба']\n};\nvar formattingDayValues = {\n  narrow: ['Н', 'Д', 'М', 'Л', 'П', 'Б', 'Б'],\n  short: ['Ня', 'Да', 'Мя', 'Лх', 'Пү', 'Ба', 'Бя'],\n  abbreviated: ['Ням', 'Дав', 'Мяг', 'Лха', 'Пүр', 'Баа', 'Бям'],\n  wide: ['ням', 'даваа', 'мягмар', 'лхагва', 'пүрэв', 'баасан', 'бямба']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ү.ө.',\n    pm: 'ү.х.',\n    midnight: 'шөнө дунд',\n    noon: 'үд дунд',\n    morning: 'өглөө',\n    afternoon: 'өдөр',\n    evening: 'орой',\n    night: 'шөнө'\n  },\n  abbreviated: {\n    am: 'ү.ө.',\n    pm: 'ү.х.',\n    midnight: 'шөнө дунд',\n    noon: 'үд дунд',\n    morning: 'өглөө',\n    afternoon: 'өдөр',\n    evening: 'орой',\n    night: 'шөнө'\n  },\n  wide: {\n    am: 'ү.ө.',\n    pm: 'ү.х.',\n    midnight: 'шөнө дунд',\n    noon: 'үд дунд',\n    morning: 'өглөө',\n    afternoon: 'өдөр',\n    evening: 'орой',\n    night: 'шөнө'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Mongolian locale.\n * @language Mongolian\n * @iso-639-2 mon\n * <AUTHOR> [@bilguun0203]{@link https://github.com/bilguun0203}\n */\nvar locale = {\n  code: 'mn',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 1\n    /* Monday */\n    ,\n    firstWeekContainsDate: 1\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'секунд хүрэхгүй',\n    other: '{{count}} секунд хүрэхгүй'\n  },\n  xSeconds: {\n    one: '1 секунд',\n    other: '{{count}} секунд'\n  },\n  halfAMinute: 'хагас минут',\n  lessThanXMinutes: {\n    one: 'минут хүрэхгүй',\n    other: '{{count}} минут хүрэхгүй'\n  },\n  xMinutes: {\n    one: '1 минут',\n    other: '{{count}} минут'\n  },\n  aboutXHours: {\n    one: 'ойролцоогоор 1 цаг',\n    other: 'ойролцоогоор {{count}} цаг'\n  },\n  xHours: {\n    one: '1 цаг',\n    other: '{{count}} цаг'\n  },\n  xDays: {\n    one: '1 өдөр',\n    other: '{{count}} өдөр'\n  },\n  aboutXWeeks: {\n    one: 'ойролцоогоор 1 долоо хоног',\n    other: 'ойролцоогоор {{count}} долоо хоног'\n  },\n  xWeeks: {\n    one: '1 долоо хоног',\n    other: '{{count}} долоо хоног'\n  },\n  aboutXMonths: {\n    one: 'ойролцоогоор 1 сар',\n    other: 'ойролцоогоор {{count}} сар'\n  },\n  xMonths: {\n    one: '1 сар',\n    other: '{{count}} сар'\n  },\n  aboutXYears: {\n    one: 'ойролцоогоор 1 жил',\n    other: 'ойролцоогоор {{count}} жил'\n  },\n  xYears: {\n    one: '1 жил',\n    other: '{{count}} жил'\n  },\n  overXYears: {\n    one: '1 жил гаран',\n    other: '{{count}} жил гаран'\n  },\n  almostXYears: {\n    one: 'бараг 1 жил',\n    other: 'бараг {{count}} жил'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    /**\n     * Append genitive case\n     */\n    var words = result.split(' ');\n    var lastword = words.pop();\n    result = words.join(' ');\n\n    switch (lastword) {\n      case 'секунд':\n        result += ' секундийн';\n        break;\n\n      case 'минут':\n        result += ' минутын';\n        break;\n\n      case 'цаг':\n        result += ' цагийн';\n        break;\n\n      case 'өдөр':\n        result += ' өдрийн';\n        break;\n\n      case 'сар':\n        result += ' сарын';\n        break;\n\n      case 'жил':\n        result += ' жилийн';\n        break;\n\n      case 'хоног':\n        result += ' хоногийн';\n        break;\n\n      case 'гаран':\n        result += ' гараны';\n        break;\n\n      case 'хүрэхгүй':\n        result += ' хүрэхгүй хугацааны';\n        break;\n\n      default:\n        result += lastword + '-н';\n    }\n\n    if (options.comparison && options.comparison > 0) {\n      return result + ' дараа';\n    } else {\n      return result + ' өмнө';\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: \"y 'оны' MMMM'ын' d, EEEE 'гараг'\",\n  long: \"y 'оны' MMMM'ын' d\",\n  medium: \"y 'оны' MMM'ын' d\",\n  short: 'y.MM.dd'\n};\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  full: '{{date}} {{time}}',\n  long: '{{date}} {{time}}',\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /\\d+/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(нтө|нт)/i,\n  abbreviated: /^(нтө|нт)/i,\n  wide: /^(нийтийн тооллын өмнө|нийтийн тооллын)/i\n};\nvar parseEraPatterns = {\n  any: [/^(нтө|нийтийн тооллын өмнө)/i, /^(нт|нийтийн тооллын)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^(iv|iii|ii|i)/i,\n  abbreviated: /^(iv|iii|ii|i) улирал/i,\n  wide: /^[1-4]-р улирал/i\n};\nvar parseQuarterPatterns = {\n  any: [/^(i(\\s|$)|1)/i, /^(ii(\\s|$)|2)/i, /^(iii(\\s|$)|3)/i, /^(iv(\\s|$)|4)/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^(xii|xi|x|ix|viii|vii|vi|v|iv|iii|ii|i)/i,\n  abbreviated: /^(1-р сар|2-р сар|3-р сар|4-р сар|5-р сар|6-р сар|7-р сар|8-р сар|9-р сар|10-р сар|11-р сар|12-р сар)/i,\n  wide: /^(нэгдүгээр сар|хоёрдугаар сар|гуравдугаар сар|дөрөвдүгээр сар|тавдугаар сар|зургаадугаар сар|долоодугаар сар|наймдугаар сар|есдүгээр сар|аравдугаар сар|арван нэгдүгээр сар|арван хоёрдугаар сар)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^i$/i, /^ii$/i, /^iii$/i, /^iv$/i, /^v$/i, /^vi$/i, /^vii$/i, /^viii$/i, /^ix$/i, /^x$/i, /^xi$/i, /^xii$/i],\n  any: [/^(1|нэгдүгээр)/i, /^(2|хоёрдугаар)/i, /^(3|гуравдугаар)/i, /^(4|дөрөвдүгээр)/i, /^(5|тавдугаар)/i, /^(6|зургаадугаар)/i, /^(7|долоодугаар)/i, /^(8|наймдугаар)/i, /^(9|есдүгээр)/i, /^(10|аравдугаар)/i, /^(11|арван нэгдүгээр)/i, /^(12|арван хоёрдугаар)/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[ндмлпбб]/i,\n  short: /^(ня|да|мя|лх|пү|ба|бя)/i,\n  abbreviated: /^(ням|дав|мяг|лха|пүр|баа|бям)/i,\n  wide: /^(ням|даваа|мягмар|лхагва|пүрэв|баасан|бямба)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^н/i, /^д/i, /^м/i, /^л/i, /^п/i, /^б/i, /^б/i],\n  any: [/^ня/i, /^да/i, /^мя/i, /^лх/i, /^пү/i, /^ба/i, /^бя/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(ү\\.ө\\.|ү\\.х\\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i,\n  any: /^(ү\\.ө\\.|ү\\.х\\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^ү\\.ө\\./i,\n    pm: /^ү\\.х\\./i,\n    midnight: /^шөнө дунд/i,\n    noon: /^үд дунд/i,\n    morning: /өглөө/i,\n    afternoon: /өдөр/i,\n    evening: /орой/i,\n    night: /шөнө/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"'өнгөрсөн' eeee 'гарагийн' p 'цагт'\",\n  yesterday: \"'өчигдөр' p 'цагт'\",\n  today: \"'өнөөдөр' p 'цагт'\",\n  tomorrow: \"'маргааш' p 'цагт'\",\n  nextWeek: \"'ирэх' eeee 'гарагийн' p 'цагт'\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "_options", "String", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "module", "_interopRequireDefault", "_index2", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "options", "weekStartsOn", "firstWeekContainsDate", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "token", "count", "result", "tokenValue", "replace", "addSuffix", "words", "split", "lastword", "pop", "join", "comparison", "date", "formats", "full", "long", "medium", "time", "dateTime", "matchPattern", "parsePattern", "valueCallback", "parseInt", "matchPatterns", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "index", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "_date", "_baseDate"], "sourceRoot": ""}
{"version": 3, "file": "74931.dtale_bundle.js", "mappings": "6EAiEAA,EAAOC,QA1DP,SAAgBC,GAiBd,MAAO,CACLC,KAAM,eACNC,QAAS,CACP,MACA,OACA,OAEFC,kBAAkB,EAClBC,SAvBsB,CACtBC,SAFsB,oBAGtBC,QAAS,sBAsBTC,SAAU,CApBS,CACnBC,UAAW,OACXC,MAAO,gBACPC,UAAW,IAEQ,CACnBF,UAAW,OACXC,MAAO,oBACPC,UAAW,IAeTV,EAAKW,oBACLX,EAAKY,qBACLZ,EAAKa,QAAQ,WAAY,QACzBb,EAAKc,cACLd,EAAKe,QAAQf,EAAKgB,iBAAkB,CAClCC,QAAS,OAEXjB,EAAKe,QAAQf,EAAKkB,kBAAmB,CACnCD,QAAS,OAEX,CACET,UAAW,SACXC,MAAO,IACPU,IAAK,KAEP,CACEX,UAAW,SACXY,SAAU,CACR,CACEX,MAAO,IACPU,IAAK,OACLF,QAAS,UAMrB,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/step21.js"], "sourcesContent": ["/*\nLanguage: STEP Part 21\nContributors: <PERSON> <<EMAIL>>\nDescription: Syntax highlighter for STEP Part 21 files (ISO 10303-21).\nWebsite: https://en.wikipedia.org/wiki/ISO_10303-21\n*/\n\nfunction step21(hljs) {\n  const STEP21_IDENT_RE = '[A-Z_][A-Z0-9_.]*';\n  const STEP21_KEYWORDS = {\n    $pattern: STEP21_IDENT_RE,\n    keyword: 'HEADER ENDSEC DATA'\n  };\n  const STEP21_START = {\n    className: 'meta',\n    begin: 'ISO-10303-21;',\n    relevance: 10\n  };\n  const STEP21_CLOSE = {\n    className: 'meta',\n    begin: 'END-ISO-10303-21;',\n    relevance: 10\n  };\n\n  return {\n    name: 'STEP Part 21',\n    aliases: [\n      'p21',\n      'step',\n      'stp'\n    ],\n    case_insensitive: true, // STEP 21 is case insensitive in theory, in practice all non-comments are capitalized.\n    keywords: STEP21_KEYWORDS,\n    contains: [\n      ST<PERSON>21_START,\n      STEP21_CLOSE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.COMMENT('/\\\\*\\\\*!', '\\\\*/'),\n      hljs.C_NUMBER_MODE,\n      hljs.inherit(hljs.APOS_STRING_MODE, {\n        illegal: null\n      }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        illegal: null\n      }),\n      {\n        className: 'string',\n        begin: \"'\",\n        end: \"'\"\n      },\n      {\n        className: 'symbol',\n        variants: [\n          {\n            begin: '#',\n            end: '\\\\d+',\n            illegal: '\\\\W'\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = step21;\n"], "names": ["module", "exports", "hljs", "name", "aliases", "case_insensitive", "keywords", "$pattern", "keyword", "contains", "className", "begin", "relevance", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "COMMENT", "C_NUMBER_MODE", "inherit", "APOS_STRING_MODE", "illegal", "QUOTE_STRING_MODE", "end", "variants"], "sourceRoot": ""}
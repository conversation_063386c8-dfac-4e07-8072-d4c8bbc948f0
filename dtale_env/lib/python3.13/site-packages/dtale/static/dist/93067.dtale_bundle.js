"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[93067],{31458:(e,d,t)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.default=void 0;var l,a=(l=t(19059))&&l.__esModule?l:{default:l};var u={date:(0,a.default)({formats:{full:"EEEE, dd MMMM yyyy",long:"dd MMMM yyyy",medium:"dd MMM yyyy",short:"dd/MM/yyyy"},defaultWidth:"full"}),time:(0,a.default)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"H:mm"},defaultWidth:"full"}),dateTime:(0,a.default)({formats:{any:"{{date}} {{time}}"},defaultWidth:"any"})};d.default=u,e.exports=d.default}}]);
//# sourceMappingURL=93067.dtale_bundle.js.map
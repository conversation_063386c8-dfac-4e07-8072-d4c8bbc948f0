"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[84628],{81713:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,a){(0,r.default)(2,arguments);var n=(0,u.default)(e,a),c=(0,u.default)(t,a);return n.getTime()===c.getTime()};var r=n(a(10427)),u=n(a(20930));function n(e){return e&&e.__esModule?e:{default:e}}e.exports=t.default},93889:(e,t,a)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,u=(r=a(81713))&&r.__esModule?r:{default:r};var n=["воскресенье","понедельник","вторник","среду","четверг","пятницу","субботу"];function c(e){var t=n[e];return 2===e?"'во "+t+" в' p":"'в "+t+" в' p"}var s={lastWeek:function(e,t,a){var r=e.getUTCDay();return(0,u.default)(e,t,a)?c(r):function(e){var t=n[e];switch(e){case 0:return"'в прошлое "+t+" в' p";case 1:case 2:case 4:return"'в прошлый "+t+" в' p";case 3:case 5:case 6:return"'в прошлую "+t+" в' p"}}(r)},yesterday:"'вчера в' p",today:"'сегодня в' p",tomorrow:"'завтра в' p",nextWeek:function(e,t,a){var r=e.getUTCDay();return(0,u.default)(e,t,a)?c(r):function(e){var t=n[e];switch(e){case 0:return"'в следующее "+t+" в' p";case 1:case 2:case 4:return"'в следующий "+t+" в' p";case 3:case 5:case 6:return"'в следующую "+t+" в' p"}}(r)},other:"P"},f=function(e,t,a,r){var u=s[e];return"function"==typeof u?u(t,a,r):u};t.default=f,e.exports=t.default}}]);
//# sourceMappingURL=84628.dtale_bundle.js.map
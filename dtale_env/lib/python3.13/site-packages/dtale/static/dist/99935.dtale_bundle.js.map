{"version": 3, "file": "99935.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IA4III,EA5BW,CACbC,cA3BkB,SAAuBC,EAAaC,GACtD,IAEIC,EAFAC,EAASC,OAAOJ,GAChBK,EAAOJ,aAAyC,EAASA,EAAQI,KAGrE,OAAQF,GACN,KAAK,EACHD,EAAU,KACV,MAEF,KAAK,EACHA,EAAU,KACV,MAEF,QACEA,EAAU,KAQd,MAJa,SAATG,GAA4B,SAATA,GAA4B,SAATA,GAA4B,WAATA,GAA8B,WAATA,IAChFH,GAAW,KAGNC,EAASD,CAClB,EAIEI,KAAK,EAAIX,EAAOE,SAAS,CACvBU,OAnHY,CACdC,OAAQ,CAAC,WAAY,aACrBC,YAAa,CAAC,WAAY,aAC1BC,KAAM,CAAC,oBAAqB,sBAiH1BC,aAAc,SAEhBC,SAAS,EAAIjB,EAAOE,SAAS,CAC3BU,OAlHgB,CAClBC,OAAQ,CAAC,KAAM,KAAM,KAAM,MAC3BC,YAAa,CAAC,YAAa,YAAa,YAAa,aACrDC,KAAM,CAAC,gBAAiB,gBAAiB,gBAAiB,kBAgHxDC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAInB,EAAOE,SAAS,CACzBU,OApHc,CAChBC,OAAQ,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC3EC,YAAa,CAAC,OAAQ,QAAS,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,QACrGC,KAAM,CAAC,SAAU,UAAW,OAAQ,QAAS,MAAO,OAAQ,SAAU,QAAS,WAAY,UAAW,WAAY,aAkHhHC,aAAc,SAEhBI,KAAK,EAAIpB,EAAOE,SAAS,CACvBU,OAnHY,CACdC,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACnDQ,MAAO,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAClDP,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACxDC,KAAM,CAAC,UAAW,SAAU,SAAU,WAAY,SAAU,YAAa,aAgHvEC,aAAc,SAEhBM,WAAW,EAAItB,EAAOE,SAAS,CAC7BU,OAjHkB,CACpBC,OAAQ,CACNU,GAAI,KACJC,GAAI,KACJC,SAAU,aACVC,KAAM,WACNC,QAAS,QACTC,UAAW,iBACXC,QAAS,SACTC,MAAO,SAEThB,YAAa,CACXS,GAAI,OACJC,GAAI,OACJC,SAAU,aACVC,KAAM,WACNC,QAAS,QACTC,UAAW,iBACXC,QAAS,SACTC,MAAO,SAETf,KAAM,CACJQ,GAAI,OACJC,GAAI,OACJC,SAAU,aACVC,KAAM,WACNC,QAAS,QACTC,UAAW,iBACXC,QAAS,SACTC,MAAO,UAqFPd,aAAc,OACde,iBAnF4B,CAC9BlB,OAAQ,CACNU,GAAI,KACJC,GAAI,KACJC,SAAU,aACVC,KAAM,WACNC,QAAS,YACTC,UAAW,sBACXC,QAAS,UACTC,MAAO,eAEThB,YAAa,CACXS,GAAI,KACJC,GAAI,KACJC,SAAU,aACVC,KAAM,WACNC,QAAS,YACTC,UAAW,sBACXC,QAAS,UACTC,MAAO,eAETf,KAAM,CACJQ,GAAI,gBACJC,GAAI,gBACJC,SAAU,aACVC,KAAM,WACNC,QAAS,YACTC,UAAW,sBACXC,QAAS,UACTC,MAAO,gBAuDPE,uBAAwB,UAI5BnC,EAAA,QAAkBM,EAClB8B,EAAOpC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/oc/_lib/localize/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['ab. J.C.', 'apr. J.C.'],\n  abbreviated: ['ab. J.C.', 'apr. J.C.'],\n  wide: ['abans <PERSON>-<PERSON>', 'après <PERSON>-C<PERSON>']\n};\nvar quarterValues = {\n  narrow: ['T1', 'T2', 'T3', 'T4'],\n  abbreviated: ['1èr trim.', '2nd trim.', '3en trim.', '4en trim.'],\n  wide: ['1èr trimèstre', '2nd trimèstre', '3en trimèstre', '4en trimèstre']\n};\nvar monthValues = {\n  narrow: ['GN', 'FB', 'MÇ', 'AB', 'MA', 'JN', 'JL', 'AG', 'ST', 'OC', 'NV', 'DC'],\n  abbreviated: ['gen.', 'febr.', 'març', 'abr.', 'mai', 'junh', 'jul.', 'ag.', 'set.', 'oct.', 'nov.', 'dec.'],\n  wide: ['genièr', 'febrièr', 'març', 'abril', 'mai', 'junh', 'julhet', 'agost', 'setembre', 'octòbre', 'novembre', 'decembre']\n};\nvar dayValues = {\n  narrow: ['dg.', 'dl.', 'dm.', 'dc.', 'dj.', 'dv.', 'ds.'],\n  short: ['dg.', 'dl.', 'dm.', 'dc.', 'dj.', 'dv.', 'ds.'],\n  abbreviated: ['dg.', 'dl.', 'dm.', 'dc.', 'dj.', 'dv.', 'ds.'],\n  wide: ['dimenge', 'diluns', 'dimars', 'dimècres', 'dijòus', 'divendres', 'dissabte']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'mièjanuèch',\n    noon: 'miègjorn',\n    morning: 'matin',\n    afternoon: 'aprèp-miègjorn',\n    evening: 'vèspre',\n    night: 'nuèch'\n  },\n  abbreviated: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'mièjanuèch',\n    noon: 'miègjorn',\n    morning: 'matin',\n    afternoon: 'aprèp-miègjorn',\n    evening: 'vèspre',\n    night: 'nuèch'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'mièjanuèch',\n    noon: 'miègjorn',\n    morning: 'matin',\n    afternoon: 'aprèp-miègjorn',\n    evening: 'vèspre',\n    night: 'nuèch'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'mièjanuèch',\n    noon: 'miègjorn',\n    morning: 'del matin',\n    afternoon: 'de l’aprèp-miègjorn',\n    evening: 'del ser',\n    night: 'de la nuèch'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'mièjanuèch',\n    noon: 'miègjorn',\n    morning: 'del matin',\n    afternoon: 'de l’aprèp-miègjorn',\n    evening: 'del ser',\n    night: 'de la nuèch'\n  },\n  wide: {\n    am: 'ante meridiem',\n    pm: 'post meridiem',\n    midnight: 'mièjanuèch',\n    noon: 'miègjorn',\n    morning: 'del matin',\n    afternoon: 'de l’aprèp-miègjorn',\n    evening: 'del ser',\n    night: 'de la nuèch'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  var ordinal;\n\n  switch (number) {\n    case 1:\n      ordinal = 'èr';\n      break;\n\n    case 2:\n      ordinal = 'nd';\n      break;\n\n    default:\n      ordinal = 'en';\n  } // feminine for year, week, hour, minute, second\n\n\n  if (unit === 'year' || unit === 'week' || unit === 'hour' || unit === 'minute' || unit === 'second') {\n    ordinal += 'a';\n  }\n\n  return number + ordinal;\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "options", "ordinal", "number", "Number", "unit", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingValues", "defaultFormattingWidth", "module"], "sourceRoot": ""}
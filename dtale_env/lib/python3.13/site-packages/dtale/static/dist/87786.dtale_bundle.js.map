{"version": 3, "file": "87786.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GA2CvF,SAASI,EAAiBC,EAAQC,EAAMC,EAAWC,EAAUC,GAC3D,IAAIC,EALN,SAAkBJ,GAChB,MAAgB,YAATA,CACT,CAGeK,CAASL,GAAQG,EAThC,SAAoBH,GAClB,MAAgB,SAATA,GAA4B,SAATA,GAA4B,WAATA,GAA8B,WAATA,CACpE,CAOyCM,CAAWN,GAAQE,EAAWD,EACrE,OAAOF,EAAS,IAAMK,CACxB,CAEA,IAyDIG,EA1BW,CACbC,cAhCkB,SAAuBC,EAAaC,GACtD,IAAIX,EAASY,OAAOF,GAChBT,EAAOU,aAAyC,EAASA,EAAQV,KAErE,GAAe,IAAXD,EACF,OAAOD,EAAiB,EAAGE,EAAM,KAAM,MAAO,OACzC,GAAID,EAAS,KAAS,EAC3B,OAAOD,EAAiBC,EAAQC,EAAM,KAAM,KAAM,MAC7C,GAAID,EAAS,KAAQ,EAC1B,OAAOD,EAAiBC,EAAQC,EAAM,MAAO,MAAO,OAGtD,IAAIY,EAASb,EAAS,IAEtB,GAAIa,EAAS,IAAMA,EAAS,GAC1B,OAAQA,EAAS,IACf,KAAK,EACH,OAAOd,EAAiBC,EAAQC,EAAM,KAAM,KAAM,MAEpD,KAAK,EACH,OAAOF,EAAiBC,EAAQC,EAAM,KAAM,KAAM,MAEpD,KAAK,EACL,KAAK,EACH,OAAOF,EAAiBC,EAAQC,EAAM,KAAM,KAAM,MAIxD,OAAOF,EAAiBC,EAAQC,EAAM,KAAM,KAAM,KACpD,EAIEa,KAAK,EAAIlB,EAAOE,SAAS,CACvBiB,OAhFY,CACdC,OAAQ,CAAC,UAAW,QACpBC,YAAa,CAAC,cAAe,SAC7BC,KAAM,CAAC,mBAAoB,eA8EzBC,aAAc,SAEhBC,SAAS,EAAIxB,EAAOE,SAAS,CAC3BiB,OA/EgB,CAClBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,eAAgB,eAAgB,eAAgB,gBAC9DC,KAAM,CAAC,kBAAmB,kBAAmB,kBAAmB,oBA6E9DC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAI1B,EAAOE,SAAS,CACzBiB,OAjFc,CAChBE,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAC3FC,KAAM,CAAC,SAAU,WAAY,OAAQ,QAAS,MAAO,MAAO,MAAO,SAAU,YAAa,WAAY,UAAW,aAgF/GC,aAAc,SAEhBI,KAAK,EAAI3B,EAAOE,SAAS,CACvBiB,OAjFY,CACdC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCQ,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CP,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACxDC,KAAM,CAAC,SAAU,aAAc,UAAW,QAAS,YAAa,QAAS,WA8EvEC,aAAc,SAEhBM,WAAW,EAAI7B,EAAOE,SAAS,CAC7BiB,OA/EkB,CACpBG,KAAM,CACJQ,GAAI,aACJC,GAAI,YACJC,SAAU,YACVC,KAAM,UACNC,QAAS,WACTC,UAAW,WACXC,QAAS,UACTC,MAAO,eAuEPd,aAAc,UAIlB1B,EAAA,QAAkBe,EAClB0B,EAAOzC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/bg/_lib/localize/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['пр.н.е.', 'н.е.'],\n  abbreviated: ['преди н. е.', 'н. е.'],\n  wide: ['преди новата ера', 'новата ера']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-во тримес.', '2-ро тримес.', '3-то тримес.', '4-то тримес.'],\n  wide: ['1-во тримесечие', '2-ро тримесечие', '3-то тримесечие', '4-то тримесечие']\n};\nvar monthValues = {\n  abbreviated: ['яну', 'фев', 'мар', 'апр', 'май', 'юни', 'юли', 'авг', 'сеп', 'окт', 'ное', 'дек'],\n  wide: ['януари', 'февруари', 'март', 'април', 'май', 'юни', 'юли', 'август', 'септември', 'октомври', 'ноември', 'декември']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],\n  abbreviated: ['нед', 'пон', 'вто', 'сря', 'чет', 'пет', 'съб'],\n  wide: ['неделя', 'понеделник', 'вторник', 'сряда', 'четвъртък', 'петък', 'събота']\n};\nvar dayPeriodValues = {\n  wide: {\n    am: 'преди обяд',\n    pm: 'след обяд',\n    midnight: 'в полунощ',\n    noon: 'на обяд',\n    morning: 'сутринта',\n    afternoon: 'следобед',\n    evening: 'вечерта',\n    night: 'през нощта'\n  }\n};\n\nfunction isFeminine(unit) {\n  return unit === 'year' || unit === 'week' || unit === 'minute' || unit === 'second';\n}\n\nfunction isNeuter(unit) {\n  return unit === 'quarter';\n}\n\nfunction numberWithSuffix(number, unit, masculine, feminine, neuter) {\n  var suffix = isNeuter(unit) ? neuter : isFeminine(unit) ? feminine : masculine;\n  return number + '-' + suffix;\n}\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n\n  if (number === 0) {\n    return numberWithSuffix(0, unit, 'ев', 'ева', 'ево');\n  } else if (number % 1000 === 0) {\n    return numberWithSuffix(number, unit, 'ен', 'на', 'но');\n  } else if (number % 100 === 0) {\n    return numberWithSuffix(number, unit, 'тен', 'тна', 'тно');\n  }\n\n  var rem100 = number % 100;\n\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return numberWithSuffix(number, unit, 'ви', 'ва', 'во');\n\n      case 2:\n        return numberWithSuffix(number, unit, 'ри', 'ра', 'ро');\n\n      case 7:\n      case 8:\n        return numberWithSuffix(number, unit, 'ми', 'ма', 'мо');\n    }\n  }\n\n  return numberWithSuffix(number, unit, 'ти', 'та', 'то');\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "numberWithSuffix", "number", "unit", "masculine", "feminine", "neuter", "suffix", "isNeuter", "isFeminine", "_default", "ordinalNumber", "dirtyNumber", "options", "Number", "rem100", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "module"], "sourceRoot": ""}
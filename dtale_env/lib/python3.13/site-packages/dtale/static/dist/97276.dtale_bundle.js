"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[97276],{93445:(t,e)=>{function n(t,e,n){var u=function(t,e){return 1===e&&t.one?t.one:e>=2&&e<=4&&t.twoFour?t.twoFour:t.other}(t,e);return u[n].replace("{{count}}",String(e))}function u(t){var e="";return"almost"===t&&(e="takmer"),"about"===t&&(e="približne"),e.length>0?e+" ":""}function o(t){var e="";return"lessThan"===t&&(e="menej než"),"over"===t&&(e="viac než"),e.length>0?e+" ":""}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r={xSeconds:{one:{present:"sekunda",past:"sekundou",future:"sekundu"},twoFour:{present:"{{count}} sekundy",past:"{{count}} sekundami",future:"{{count}} sekundy"},other:{present:"{{count}} sekúnd",past:"{{count}} sekundami",future:"{{count}} sekúnd"}},halfAMinute:{other:{present:"pol minúty",past:"pol minútou",future:"pol minúty"}},xMinutes:{one:{present:"minúta",past:"minútou",future:"minútu"},twoFour:{present:"{{count}} minúty",past:"{{count}} minútami",future:"{{count}} minúty"},other:{present:"{{count}} minút",past:"{{count}} minútami",future:"{{count}} minút"}},xHours:{one:{present:"hodina",past:"hodinou",future:"hodinu"},twoFour:{present:"{{count}} hodiny",past:"{{count}} hodinami",future:"{{count}} hodiny"},other:{present:"{{count}} hodín",past:"{{count}} hodinami",future:"{{count}} hodín"}},xDays:{one:{present:"deň",past:"dňom",future:"deň"},twoFour:{present:"{{count}} dni",past:"{{count}} dňami",future:"{{count}} dni"},other:{present:"{{count}} dní",past:"{{count}} dňami",future:"{{count}} dní"}},xWeeks:{one:{present:"týždeň",past:"týždňom",future:"týždeň"},twoFour:{present:"{{count}} týždne",past:"{{count}} týždňami",future:"{{count}} týždne"},other:{present:"{{count}} týždňov",past:"{{count}} týždňami",future:"{{count}} týždňov"}},xMonths:{one:{present:"mesiac",past:"mesiacom",future:"mesiac"},twoFour:{present:"{{count}} mesiace",past:"{{count}} mesiacmi",future:"{{count}} mesiace"},other:{present:"{{count}} mesiacov",past:"{{count}} mesiacmi",future:"{{count}} mesiacov"}},xYears:{one:{present:"rok",past:"rokom",future:"rok"},twoFour:{present:"{{count}} roky",past:"{{count}} rokmi",future:"{{count}} roky"},other:{present:"{{count}} rokov",past:"{{count}} rokmi",future:"{{count}} rokov"}}},s=function(t,e,s){var a,c=function(t){return["lessThan","about","over","almost"].filter(function(e){return!!t.match(new RegExp("^"+e))})[0]}(t)||"",i=(a=t.substring(c.length)).charAt(0).toLowerCase()+a.slice(1),p=r[i];return null!=s&&s.addSuffix?s.comparison&&s.comparison>0?u(c)+"o "+o(c)+n(p,e,"future"):u(c)+"pred "+o(c)+n(p,e,"past"):u(c)+o(c)+n(p,e,"present")};e.default=s,t.exports=e.default}}]);
//# sourceMappingURL=97276.dtale_bundle.js.map
{"version": 3, "file": "80392.dtale_bundle.js", "mappings": "8HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIE,EAASC,EAAuB,EAAQ,QAExCC,EAAUD,EAAuB,EAAQ,QAEzCE,EAAUF,EAAuB,EAAQ,QAEzCG,EAAUH,EAAuB,EAAQ,QAEzCI,EAAUJ,EAAuB,EAAQ,QAE7C,SAASA,EAAuBK,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAU9F,IAcIG,EAdS,CACXC,KAAM,KACNC,eAAgBX,EAAOQ,QACvBI,WAAYV,EAAQM,QACpBK,eAAgBV,EAAQK,QACxBM,SAAUV,EAAQI,QAClBO,MAAOV,EAAQG,QACfQ,QAAS,CACPC,aAAc,EAGdC,sBAAuB,IAI3BpB,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O,kBCzCzBZ,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCQ,EAF5BN,GAE4BM,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAgCIG,EAda,CACfW,MAAM,EAAIpB,EAAOQ,SAAS,CACxBa,QApBc,CAChBC,KAAM,oBACNC,KAAM,cACNC,OAAQ,aACRC,MAAO,cAiBLC,aAAc,SAEhBC,MAAM,EAAI3B,EAAOQ,SAAS,CACxBa,QAlBc,CAChBC,KAAM,iBACNC,KAAM,cACNC,OAAQ,YACRC,MAAO,UAeLC,aAAc,SAEhBE,UAAU,EAAI5B,EAAOQ,SAAS,CAC5Ba,QAhBkB,CACpBC,KAAM,yBACNC,KAAM,yBACNC,OAAQ,qBACRC,MAAO,sBAaLC,aAAc,UAIlB5B,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O,kBC3CzBZ,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIE,EAASC,EAAuB,EAAQ,QAI5C,SAASA,EAAuBK,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAE9F,IAiGIG,EA1CQ,CACVoB,eAAe,EA5DH5B,EAAuB,EAAQ,MA4DhBO,SAAS,CAClCsB,aAzD4B,qCA0D5BC,aAzD4B,OA0D5BC,cAAe,SAAuBjC,GACpC,OAAOkC,SAASlC,EAAO,GACzB,IAEFmC,KAAK,EAAIlC,EAAOQ,SAAS,CACvB2B,cA9DmB,CACrBC,OAAQ,UACRC,YAAa,8BACbC,KAAM,0CA4DJC,kBAAmB,OACnBC,cA3DmB,CACrBF,KAAM,CAAC,MAAO,+BACdG,IAAK,CAAC,MAAO,QA0DXC,kBAAmB,QAErBC,SAAS,EAAI3C,EAAOQ,SAAS,CAC3B2B,cA3DuB,CACzBC,OAAQ,WACRC,YAAa,aACbC,KAAM,+CAyDJC,kBAAmB,OACnBC,cAxDuB,CACzBC,IAAK,CAAC,KAAM,KAAM,KAAM,OAwDtBC,kBAAmB,MACnBV,cAAe,SAAuBY,GACpC,OAAOA,EAAQ,CACjB,IAEFC,OAAO,EAAI7C,EAAOQ,SAAS,CACzB2B,cA5DqB,CACvBC,OAAQ,0BACRC,YAAa,yDACbC,KAAM,8FA0DJC,kBAAmB,OACnBC,cAzDqB,CACvBJ,OAAQ,CAAC,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,QACvFK,IAAK,CAAC,OAAQ,OAAQ,QAAS,MAAO,QAAS,QAAS,MAAO,MAAO,QAAS,MAAO,MAAO,SAwD3FC,kBAAmB,QAErBI,KAAK,EAAI9C,EAAOQ,SAAS,CACvB2B,cAzDmB,CACrBC,OAAQ,iBACRX,MAAO,2BACPY,YAAa,mCACbC,KAAM,sDAsDJC,kBAAmB,OACnBC,cArDmB,CACrBJ,OAAQ,CAAC,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,OACpDE,KAAM,CAAC,YAAa,YAAa,YAAa,YAAa,WAAY,WAAY,aACnFG,IAAK,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,MAAO,SAmDlDC,kBAAmB,QAErBK,WAAW,EAAI/C,EAAOQ,SAAS,CAC7B2B,cApDyB,CAC3BC,OAAQ,4DACRK,IAAK,yFAmDHF,kBAAmB,MACnBC,cAlDyB,CAC3BC,IAAK,CACHO,GAAI,oBACJC,GAAI,8BACJC,SAAU,kBACVC,KAAM,mBACNC,QAAS,QACTC,UAAW,YACXC,QAAS,gBACTC,MAAO,UA0CPb,kBAAmB,SAIvB5C,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O,kBC9GzBZ,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCQ,EAF5BN,GAE4BM,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAoKIG,EA5BW,CACboB,cA/CkB,SAAuB2B,EAAaC,GACtD,IAAIC,EAASC,OAAOH,GAEpB,GAAIE,EAAS,GACX,OAAQA,GACN,KAAK,EAiBL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACH,OAAOA,EAAS,MArBlB,KAAK,EACH,OAAOA,EAAS,KAElB,KAAK,EACH,OAAOA,EAAS,MAElB,KAAK,EACL,KAAK,EACH,OAAOA,EAAS,MAElB,KAAK,EACL,KAAK,EACH,OAAOA,EAAS,KAWlB,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACH,OAAOA,EAAS,UAEf,GAAIA,GAAU,IAAMA,GAAU,IAAiB,KAAXA,GAAiBA,GAAU,IACpE,OAAOA,EAAS,MAGlB,OAAOA,EAAS,KAClB,EAIExB,KAAK,EAAIlC,EAAOQ,SAAS,CACvBoD,OA3IY,CACdxB,OAAQ,CAAC,IAAK,KACdC,YAAa,CAAC,KAAM,MACpBC,KAAM,CAAC,YAAa,gBAyIlBZ,aAAc,SAEhBiB,SAAS,EAAI3C,EAAOQ,SAAS,CAC3BoD,OA1IgB,CAClBxB,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,MAAO,MAAO,MAAO,OACnCC,KAAM,CAAC,eAAgB,gBAAiB,gBAAiB,kBAwIvDZ,aAAc,OACdmC,iBAAkB,SAA0BlB,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAI7C,EAAOQ,SAAS,CACzBoD,OAxIc,CAChBxB,OAAQ,CAAC,IAAK,KAAM,KAAM,IAAK,KAAM,KAAM,IAAK,IAAK,KAAM,IAAK,IAAK,MACrEC,YAAa,CAAC,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAAQ,QAC7FC,KAAM,CAAC,SAAU,WAAY,SAAU,SAAU,MAAO,UAAW,aAAc,OAAQ,OAAQ,SAAU,WAAY,YAsIrHZ,aAAc,SAEhBoB,KAAK,EAAI9C,EAAOQ,SAAS,CACvBoD,OAvIY,CACdxB,OAAQ,CAAC,IAAK,KAAM,IAAK,IAAK,IAAK,IAAK,KACxCX,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CY,YAAa,CAAC,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,OACzDC,KAAM,CAAC,WAAY,YAAa,cAAe,eAAgB,WAAY,cAAe,gBAoIxFZ,aAAc,SAEhBqB,WAAW,EAAI/C,EAAOQ,SAAS,CAC7BoD,OArIkB,CACpBxB,OAAQ,CACNY,GAAI,IACJC,GAAI,IACJC,SAAU,KACVC,KAAM,KACNC,QAAS,OACTC,UAAW,WACXC,QAAS,aACTC,MAAO,OAETlB,YAAa,CACXW,GAAI,KACJC,GAAI,KACJC,SAAU,aACVC,KAAM,cACNC,QAAS,OACTC,UAAW,WACXC,QAAS,aACTC,MAAO,OAETjB,KAAM,CACJU,GAAI,OACJC,GAAI,OACJC,SAAU,aACVC,KAAM,cACNC,QAAS,OACTC,UAAW,WACXC,QAAS,aACTC,MAAO,QAyGP7B,aAAc,OACdoC,iBAvG4B,CAC9B1B,OAAQ,CACNY,GAAI,IACJC,GAAI,IACJC,SAAU,KACVC,KAAM,KACNC,QAAS,YACTC,UAAW,gBACXC,QAAS,aACTC,MAAO,YAETlB,YAAa,CACXW,GAAI,KACJC,GAAI,KACJC,SAAU,aACVC,KAAM,cACNC,QAAS,YACTC,UAAW,gBACXC,QAAS,aACTC,MAAO,YAETjB,KAAM,CACJU,GAAI,OACJC,GAAI,OACJC,SAAU,aACVC,KAAM,cACNC,QAAS,YACTC,UAAW,gBACXC,QAAS,aACTC,MAAO,aA2EPQ,uBAAwB,UAI5BjE,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O,gBC/KzBZ,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIkE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,iBACLC,MAAO,4BAETC,SAAU,CACRF,IAAK,WACLC,MAAO,oBAETE,YAAa,eACbC,iBAAkB,CAChBJ,IAAK,gBACLK,IAAK,kBACLJ,MAAO,2BAETK,SAAU,CACRN,IAAK,UACLK,IAAK,UACLJ,MAAO,mBAETM,YAAa,CACXP,IAAK,YACLC,MAAO,qBAETO,OAAQ,CACNR,IAAK,QACLC,MAAO,iBAETQ,MAAO,CACLT,IAAK,YACLK,IAAK,aACLJ,MAAO,qBAETS,YAAa,CACXV,IAAK,gBACLK,IAAK,gBACLJ,MAAO,yBAETU,OAAQ,CACNX,IAAK,YACLK,IAAK,YACLJ,MAAO,qBAETW,aAAc,CACZZ,IAAK,YACLK,IAAK,YACLJ,MAAO,qBAETY,QAAS,CACPb,IAAK,QACLK,IAAK,QACLJ,MAAO,iBAETa,YAAa,CACXd,IAAK,iBACLK,IAAK,gBACLJ,MAAO,yBAETc,OAAQ,CACNf,IAAK,aACLK,IAAK,YACLJ,MAAO,qBAETe,WAAY,CACVhB,IAAK,kBACLK,IAAK,iBACLJ,MAAO,0BAETgB,aAAc,CACZjB,IAAK,kBACLK,IAAK,iBACLJ,MAAO,2BA6BP1D,EAzBiB,SAAwB2E,EAAOC,EAAOrE,GACzD,IAAIsE,EACAC,EAAavB,EAAqBoB,GAYtC,OATEE,EADwB,iBAAfC,EACAA,EACU,IAAVF,EACAE,EAAWrB,IACD,IAAVmB,GAAiBE,EAAWhB,IAC5BgB,EAAWhB,IAEXgB,EAAWpB,MAAMqB,QAAQ,YAAaC,OAAOJ,IAGpDrE,SAA0CA,EAAQ0E,UAChD1E,EAAQ2E,YAAc3E,EAAQ2E,WAAa,EACtC,QAAUL,EAEVA,EAAS,SAIbA,CACT,EAGAxF,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O,gBC1GzBZ,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAI8F,EAAuB,CACzBC,SAAU,uBACVC,UAAW,cACXC,MAAO,gBACPC,SAAU,eACVC,SAAU,cACV9B,MAAO,KAOL1D,EAJiB,SAAwB2E,EAAOc,EAAOC,EAAW1C,GACpE,OAAOmC,EAAqBR,EAC9B,EAGAtF,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/cy/index.js", "webpack://dtale/./node_modules/date-fns/locale/cy/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/cy/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/cy/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/cy/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/cy/_lib/formatRelative/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Welsh locale.\n * @language Welsh\n * @iso-639-2 cym\n * <AUTHOR> [@elmomalmo]{@link https://github.com/elmomalmo}\n */\nvar locale = {\n  code: 'cy',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 0\n    /* Sunday */\n    ,\n    firstWeekContainsDate: 1\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'EEEE, d MMMM yyyy',\n  long: 'd MMMM yyyy',\n  medium: 'd MMM yyyy',\n  short: 'dd/MM/yyyy'\n};\nvar timeFormats = {\n  full: 'h:mm:ss a zzzz',\n  long: 'h:mm:ss a z',\n  medium: 'h:mm:ss a',\n  short: 'h:mm a'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'am' {{time}}\",\n  long: \"{{date}} 'am' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)(af|ail|ydd|ed|fed|eg|ain)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(c|o)/i,\n  abbreviated: /^(c\\.?\\s?c\\.?|o\\.?\\s?c\\.?)/i,\n  wide: /^(cyn christ|ar ôl crist|ar ol crist)/i\n};\nvar parseEraPatterns = {\n  wide: [/^c/i, /^(ar ôl crist|ar ol crist)/i],\n  any: [/^c/i, /^o/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^ch[1234]/i,\n  wide: /^(chwarter 1af)|([234](ail|ydd)? chwarter)/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^(i|ch|m|e|g|a|h|t|rh)/i,\n  abbreviated: /^(ion|chwe|maw|ebr|mai|meh|gor|aws|med|hyd|tach|rhag)/i,\n  wide: /^(ionawr|chwefror|mawrth|ebrill|mai|mehefin|gorffennaf|awst|medi|hydref|tachwedd|rhagfyr)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^i/i, /^ch/i, /^m/i, /^e/i, /^m/i, /^m/i, /^g/i, /^a/i, /^m/i, /^h/i, /^t/i, /^rh/i],\n  any: [/^io/i, /^ch/i, /^maw/i, /^e/i, /^mai/i, /^meh/i, /^g/i, /^a/i, /^med/i, /^h/i, /^t/i, /^rh/i]\n};\nvar matchDayPatterns = {\n  narrow: /^(s|ll|m|i|g)/i,\n  short: /^(su|ll|ma|me|ia|gw|sa)/i,\n  abbreviated: /^(sul|llun|maw|mer|iau|gwe|sad)/i,\n  wide: /^dydd (sul|llun|mawrth|mercher|iau|gwener|sadwrn)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^s/i, /^ll/i, /^m/i, /^m/i, /^i/i, /^g/i, /^s/i],\n  wide: [/^dydd su/i, /^dydd ll/i, /^dydd ma/i, /^dydd me/i, /^dydd i/i, /^dydd g/i, /^dydd sa/i],\n  any: [/^su/i, /^ll/i, /^ma/i, /^me/i, /^i/i, /^g/i, /^sa/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(b|h|hn|hd|(yn y|y|yr|gyda'r) (bore|prynhawn|nos|hwyr))/i,\n  any: /^(y\\.?\\s?[bh]\\.?|hanner nos|hanner dydd|(yn y|y|yr|gyda'r) (bore|prynhawn|nos|hwyr))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^b|(y\\.?\\s?b\\.?)/i,\n    pm: /^h|(y\\.?\\s?h\\.?)|(yr hwyr)/i,\n    midnight: /^hn|hanner nos/i,\n    noon: /^hd|hanner dydd/i,\n    morning: /bore/i,\n    afternoon: /prynhawn/i,\n    evening: /^gyda'r nos$/i,\n    night: /blah/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['C', 'O'],\n  abbreviated: ['CC', 'OC'],\n  wide: ['Cyn Crist', '<PERSON>r ôl C<PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Ch1', 'Ch2', 'Ch3', 'Ch4'],\n  wide: ['Chwarter 1af', '2ail chwarter', '3ydd chwarter', '4ydd chwarter']\n}; // Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n\nvar monthValues = {\n  narrow: ['I', 'Ch', 'Ma', 'E', 'Mi', 'Me', 'G', 'A', 'Md', 'H', 'T', 'Rh'],\n  abbreviated: ['Ion', 'Chwe', 'Maw', 'Ebr', 'Mai', 'Meh', 'Gor', 'Aws', 'Med', 'Hyd', 'Tach', 'Rhag'],\n  wide: ['Ionawr', 'Chwefror', 'Mawrth', 'Ebrill', 'Mai', 'Mehefin', 'Gorffennaf', 'Awst', 'Medi', 'Hydref', 'Tachwedd', 'Rhagfyr']\n};\nvar dayValues = {\n  narrow: ['S', 'Ll', 'M', 'M', 'I', 'G', 'S'],\n  short: ['Su', 'Ll', 'Ma', 'Me', 'Ia', 'Gw', 'Sa'],\n  abbreviated: ['Sul', 'Llun', 'Maw', 'Mer', 'Iau', 'Gwe', 'Sad'],\n  wide: ['dydd Sul', 'dydd Llun', 'dydd Mawrth', 'dydd Mercher', 'dydd Iau', 'dydd Gwener', 'dydd Sadwrn']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'b',\n    pm: 'h',\n    midnight: 'hn',\n    noon: 'hd',\n    morning: 'bore',\n    afternoon: 'prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'nos'\n  },\n  abbreviated: {\n    am: 'yb',\n    pm: 'yh',\n    midnight: 'hanner nos',\n    noon: 'hanner dydd',\n    morning: 'bore',\n    afternoon: 'prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'nos'\n  },\n  wide: {\n    am: 'y.b.',\n    pm: 'y.h.',\n    midnight: 'hanner nos',\n    noon: 'hanner dydd',\n    morning: 'bore',\n    afternoon: 'prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'nos'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'b',\n    pm: 'h',\n    midnight: 'hn',\n    noon: 'hd',\n    morning: 'yn y bore',\n    afternoon: 'yn y prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'yn y nos'\n  },\n  abbreviated: {\n    am: 'yb',\n    pm: 'yh',\n    midnight: 'hanner nos',\n    noon: 'hanner dydd',\n    morning: 'yn y bore',\n    afternoon: 'yn y prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'yn y nos'\n  },\n  wide: {\n    am: 'y.b.',\n    pm: 'y.h.',\n    midnight: 'hanner nos',\n    noon: 'hanner dydd',\n    morning: 'yn y bore',\n    afternoon: 'yn y prynhawn',\n    evening: \"gyda'r nos\",\n    night: 'yn y nos'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n\n  if (number < 20) {\n    switch (number) {\n      case 0:\n        return number + 'fed';\n\n      case 1:\n        return number + 'af';\n\n      case 2:\n        return number + 'ail';\n\n      case 3:\n      case 4:\n        return number + 'ydd';\n\n      case 5:\n      case 6:\n        return number + 'ed';\n\n      case 7:\n      case 8:\n      case 9:\n      case 10:\n      case 12:\n      case 15:\n      case 18:\n        return number + 'fed';\n\n      case 11:\n      case 13:\n      case 14:\n      case 16:\n      case 17:\n      case 19:\n        return number + 'eg';\n    }\n  } else if (number >= 50 && number <= 60 || number === 80 || number >= 100) {\n    return number + 'fed';\n  }\n\n  return number + 'ain';\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'llai na eiliad',\n    other: 'llai na {{count}} eiliad'\n  },\n  xSeconds: {\n    one: '1 eiliad',\n    other: '{{count}} eiliad'\n  },\n  halfAMinute: 'hanner munud',\n  lessThanXMinutes: {\n    one: 'llai na munud',\n    two: 'llai na 2 funud',\n    other: 'llai na {{count}} munud'\n  },\n  xMinutes: {\n    one: '1 munud',\n    two: '2 funud',\n    other: '{{count}} munud'\n  },\n  aboutXHours: {\n    one: 'tua 1 awr',\n    other: 'tua {{count}} awr'\n  },\n  xHours: {\n    one: '1 awr',\n    other: '{{count}} awr'\n  },\n  xDays: {\n    one: '1 diwrnod',\n    two: '2 ddiwrnod',\n    other: '{{count}} diwrnod'\n  },\n  aboutXWeeks: {\n    one: 'tua 1 wythnos',\n    two: 'tua pythefnos',\n    other: 'tua {{count}} wythnos'\n  },\n  xWeeks: {\n    one: '1 wythnos',\n    two: 'pythefnos',\n    other: '{{count}} wythnos'\n  },\n  aboutXMonths: {\n    one: 'tua 1 mis',\n    two: 'tua 2 fis',\n    other: 'tua {{count}} mis'\n  },\n  xMonths: {\n    one: '1 mis',\n    two: '2 fis',\n    other: '{{count}} mis'\n  },\n  aboutXYears: {\n    one: 'tua 1 flwyddyn',\n    two: 'tua 2 flynedd',\n    other: 'tua {{count}} mlynedd'\n  },\n  xYears: {\n    one: '1 flwyddyn',\n    two: '2 flynedd',\n    other: '{{count}} mlynedd'\n  },\n  overXYears: {\n    one: 'dros 1 flwyddyn',\n    two: 'dros 2 flynedd',\n    other: 'dros {{count}} mlynedd'\n  },\n  almostXYears: {\n    one: 'bron 1 flwyddyn',\n    two: 'bron 2 flynedd',\n    other: 'bron {{count}} mlynedd'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2 && !!tokenValue.two) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'mewn ' + result;\n    } else {\n      return result + ' yn ôl';\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"eeee 'diwethaf am' p\",\n  yesterday: \"'ddoe am' p\",\n  today: \"'heddiw am' p\",\n  tomorrow: \"'yfory am' p\",\n  nextWeek: \"eeee 'am' p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "_index", "_interopRequireDefault", "_index2", "_index3", "_index4", "_index5", "obj", "__esModule", "default", "_default", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "options", "weekStartsOn", "firstWeekContainsDate", "module", "date", "formats", "full", "long", "medium", "short", "defaultWidth", "time", "dateTime", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "parseInt", "era", "matchPatterns", "narrow", "abbreviated", "wide", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "dirtyNumber", "_options", "number", "Number", "values", "argument<PERSON>allback", "formattingValues", "defaultFormattingWidth", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "two", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "token", "count", "result", "tokenValue", "replace", "String", "addSuffix", "comparison", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "_date", "_baseDate"], "sourceRoot": ""}
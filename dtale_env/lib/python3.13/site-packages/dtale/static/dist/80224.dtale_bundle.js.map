{"version": 3, "file": "80224.dtale_bundle.js", "mappings": "6HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAsHII,EA5BW,CACbC,cALkB,SAAuBC,EAAaC,GACtD,OAAOC,OAAOF,EAChB,EAIEG,KAAK,EAAIR,EAAOE,SAAS,CACvBO,OA7FY,CACdC,OAAQ,CAAC,IAAK,MACdC,YAAa,CAAC,KAAM,QACpBC,KAAM,CAAC,iBAAkB,iBA2FvBC,aAAc,SAEhBC,SAAS,EAAId,EAAOE,SAAS,CAC3BO,OA5FgB,CAClBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,KAAM,KAAM,KAAM,MAChCC,KAAM,CAAC,YAAa,eAAgB,eAAgB,iBA0FlDC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAIhB,EAAOE,SAAS,CACzBO,OAxFc,CAChBC,OAAQ,CAAC,OAAQ,OAAQ,QAAS,QAAS,OAAQ,QAAS,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QACpGC,YAAa,CAAC,OAAQ,OAAQ,QAAS,QAAS,OAAQ,QAAS,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QACzGC,KAAM,CAAC,SAAU,aAAc,SAAU,SAAU,UAAW,WAAY,UAAW,UAAW,UAAW,SAAU,YAAa,YAsFhIC,aAAc,SAEhBI,KAAK,EAAIjB,EAAOE,SAAS,CACvBO,OAlGY,CACdC,OAAQ,CAAC,MAAO,KAAM,KAAM,KAAM,MAAO,KAAM,MAC/CQ,MAAO,CAAC,MAAO,KAAM,KAAM,KAAM,MAAO,KAAM,MAC9CP,YAAa,CAAC,MAAO,KAAM,KAAM,KAAM,MAAO,KAAM,MACpDC,KAAM,CAAC,UAAW,SAAU,SAAU,MAAO,WAAY,QAAS,UA+FhEC,aAAc,SAEhBM,WAAW,EAAInB,EAAOE,SAAS,CAC7BO,OA3FkB,CACpBC,OAAQ,CACNU,GAAI,aACJC,GAAI,aACJC,SAAU,YACVC,KAAM,SACNC,QAAS,OACTC,UAAW,OACXC,QAAS,OACTC,MAAO,WAEThB,YAAa,CACXS,GAAI,aACJC,GAAI,aACJC,SAAU,YACVC,KAAM,SACNC,QAAS,OACTC,UAAW,OACXC,QAAS,OACTC,MAAO,WAETf,KAAM,CACJQ,GAAI,aACJC,GAAI,aACJC,SAAU,YACVC,KAAM,SACNC,QAAS,OACTC,UAAW,OACXC,QAAS,OACTC,MAAO,YA+DPd,aAAc,OACde,iBA7D4B,CAC9BlB,OAAQ,CACNU,GAAI,aACJC,GAAI,aACJC,SAAU,YACVC,KAAM,SACNC,QAAS,UACTC,UAAW,aACXC,QAAS,UACTC,MAAO,cAEThB,YAAa,CACXS,GAAI,aACJC,GAAI,aACJC,SAAU,YACVC,KAAM,SACNC,QAAS,UACTC,UAAW,aACXC,QAAS,UACTC,MAAO,cAETf,KAAM,CACJQ,GAAI,aACJC,GAAI,aACJC,SAAU,YACVC,KAAM,SACNC,QAAS,UACTC,UAAW,aACXC,QAAS,UACTC,MAAO,eAiCPE,uBAAwB,UAI5BhC,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,kBCjIzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAAS+B,EAAuB,EAAQ,QAExCC,EAAUD,EAAuB,EAAQ,QAEzCE,EAAUF,EAAuB,EAAQ,QAEzCG,EAAUH,EAAuB,EAAQ,QAEzCI,EAAUJ,EAAuB,EAAQ,QAE7C,SAASA,EAAuBhC,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAY9F,IAcII,EAdS,CACXiC,KAAM,KACNC,eAAgBrC,EAAOE,QACvBoC,WAAYN,EAAQ9B,QACpBqC,eAAgBN,EAAQ/B,QACxBsC,SAAUN,EAAQhC,QAClBuC,MAAON,EAAQjC,QACfwC,QAAS,CACPC,aAAc,EAGdC,sBAAuB,IAI3B/C,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,kBC3CzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAAS+B,EAAuB,EAAQ,QAI5C,SAASA,EAAuBhC,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAE9F,IA8FII,EA1CQ,CACVC,eAAe,EAzDH2B,EAAuB,EAAQ,MAyDhB7B,SAAS,CAClC2C,aAtD4B,QAuD5BC,aAtD4B,OAuD5BC,cAAe,SAAuBjD,GACpC,OAAOkD,SAASlD,EAAO,GACzB,IAEFU,KAAK,EAAIR,EAAOE,SAAS,CACvB+C,cA3DmB,CACrBvC,OAAQ,mBACRC,YAAa,4EACbC,KAAM,0CAyDJsC,kBAAmB,OACnBC,cAxDmB,CACrBC,IAAK,CAAC,SAAU,8CAwDdC,kBAAmB,QAErBvC,SAAS,EAAId,EAAOE,SAAS,CAC3B+C,cAzDuB,CACzBvC,OAAQ,WACRC,YAAa,YACbC,KAAM,0BAuDJsC,kBAAmB,OACnBC,cAtDuB,CACzBC,IAAK,CAAC,iBAAkB,WAAY,WAAY,aAsD9CC,kBAAmB,MACnBN,cAAe,SAAuBO,GACpC,OAAOA,EAAQ,CACjB,IAEFtC,OAAO,EAAIhB,EAAOE,SAAS,CACzB+C,cA1DqB,CACvBvC,OAAQ,qHACRC,YAAa,sHACbC,KAAM,yGAwDJsC,kBAAmB,OACnBC,cAvDqB,CACvBvC,KAAM,CAAC,OAAQ,QAAS,OAAQ,OAAQ,QAAS,OAAQ,QAAS,MAAO,QAAS,MAAO,QAAS,OAClGwC,IAAK,CAAC,aAAc,aAAc,cAAe,cAAe,aAAc,cAAe,aAAc,aAAc,aAAc,aAAc,aAAc,eAsDjKC,kBAAmB,QAErBpC,KAAK,EAAIjB,EAAOE,SAAS,CACvB+C,cAvDmB,CACrBvC,OAAQ,2CACRQ,MAAO,2CACPP,YAAa,2CACbC,KAAM,sDAoDJsC,kBAAmB,OACnBC,cAnDmB,CACrBvC,KAAM,CAAC,OAAQ,OAAQ,OAAQ,QAAS,OAAQ,MAAO,QACvDwC,IAAK,CAAC,OAAQ,MAAO,MAAO,WAAY,OAAQ,MAAO,QAkDrDC,kBAAmB,QAErBlC,WAAW,EAAInB,EAAOE,SAAS,CAC7B+C,cAnDyB,CAC3BG,IAAK,yFAmDHF,kBAAmB,MACnBC,cAlDyB,CAC3BC,IAAK,CACHhC,GAAI,eACJC,GAAI,eACJC,SAAU,cACVC,KAAM,WACNC,QAAS,QACTC,UAAW,QACXC,QAAS,QACTC,MAAO,aA0CP0B,kBAAmB,SAIvBxD,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,gBC3GzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAI0D,EAAuB,CACzBC,SAAU,sBACVC,UAAW,sBACXC,MAAO,iBACPC,SAAU,mBACVC,SAAU,gBACVC,MAAO,KAOL1D,EAJiB,SAAwB2D,EAAOC,EAAOC,EAAW1D,GACpE,OAAOiD,EAAqBO,EAC9B,EAGAjE,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,gBCnBzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIoE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,oBACLN,MAAO,6BAETO,SAAU,CACRD,IAAK,WACLN,MAAO,oBAETQ,YAAa,YACbC,iBAAkB,CAChBH,IAAK,kBACLN,MAAO,2BAETU,SAAU,CACRJ,IAAK,SACLN,MAAO,kBAETW,YAAa,CACXL,IAAK,mBACLN,MAAO,4BAETY,OAAQ,CACNN,IAAK,YACLN,MAAO,qBAETa,MAAO,CACLP,IAAK,QACLN,MAAO,iBAETc,YAAa,CACXR,IAAK,mBACLN,MAAO,4BAETe,OAAQ,CACNT,IAAK,YACLN,MAAO,qBAETgB,aAAc,CACZV,IAAK,iBACLN,MAAO,0BAETiB,QAAS,CACPX,IAAK,UACLN,MAAO,mBAETkB,YAAa,CACXZ,IAAK,cACLN,MAAO,uBAETmB,OAAQ,CACNb,IAAK,OACLN,MAAO,gBAEToB,WAAY,CACVd,IAAK,eACLN,MAAO,wBAETqB,aAAc,CACZf,IAAK,aACLN,MAAO,uBA+BP1D,EA3BiB,SAAwB2D,EAAOqB,EAAOzC,GACzD,IAAI0C,EACAC,EAAapB,EAAqBH,GAUtC,OAPEsB,EADwB,iBAAfC,EACAA,EACU,IAAVF,EACAE,EAAWlB,IAEXkB,EAAWxB,MAAMyB,QAAQ,YAAa/E,OAAO4E,IAGpDzC,SAA0CA,EAAQ6C,UAChD7C,EAAQ8C,YAAc9C,EAAQ8C,WAAa,EAC/B,gBAAV1B,EACK,KAAOsB,EAEP,MAAQA,EAGVA,EAAS,YAIbA,CACT,EAGAvF,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,kBCjGzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAgCII,EAda,CACfsF,MAAM,EAAIzF,EAAOE,SAAS,CACxBwF,QApBc,CAChBC,KAAM,uBACNC,KAAM,YACNC,OAAQ,UACR3E,MAAO,cAiBLL,aAAc,SAEhBiF,MAAM,EAAI9F,EAAOE,SAAS,CACxBwF,QAlBc,CAChBC,KAAM,kBACNC,KAAM,eACNC,OAAQ,aACR3E,MAAO,WAeLL,aAAc,WAEhBkF,UAAU,EAAI/F,EAAOE,SAAS,CAC5BwF,QAhBkB,CACpBC,KAAM,2BACNC,KAAM,2BACNC,OAAQ,qBACR3E,MAAO,sBAaLL,aAAc,UAIlBhB,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/th/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/th/index.js", "webpack://dtale/./node_modules/date-fns/locale/th/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/th/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/locale/th/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/th/_lib/formatLong/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['B', 'คศ'],\n  abbreviated: ['BC', 'ค.ศ.'],\n  wide: ['ปีก่อนคริสตกาล', 'คริสต์ศักราช']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['ไตรมาสแรก', 'ไตรมาสที่สอง', 'ไตรมาสที่สาม', 'ไตรมาสที่สี่']\n};\nvar dayValues = {\n  narrow: ['อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'],\n  short: ['อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'],\n  abbreviated: ['อา.', 'จ.', 'อ.', 'พ.', 'พฤ.', 'ศ.', 'ส.'],\n  wide: ['อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์']\n};\nvar monthValues = {\n  narrow: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'],\n  abbreviated: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'],\n  wide: ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'เช้า',\n    afternoon: 'บ่าย',\n    evening: 'เย็น',\n    night: 'กลางคืน'\n  },\n  abbreviated: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'เช้า',\n    afternoon: 'บ่าย',\n    evening: 'เย็น',\n    night: 'กลางคืน'\n  },\n  wide: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'เช้า',\n    afternoon: 'บ่าย',\n    evening: 'เย็น',\n    night: 'กลางคืน'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'ตอนเช้า',\n    afternoon: 'ตอนกลางวัน',\n    evening: 'ตอนเย็น',\n    night: 'ตอนกลางคืน'\n  },\n  abbreviated: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'ตอนเช้า',\n    afternoon: 'ตอนกลางวัน',\n    evening: 'ตอนเย็น',\n    night: 'ตอนกลางคืน'\n  },\n  wide: {\n    am: 'ก่อนเที่ยง',\n    pm: 'หลังเที่ยง',\n    midnight: 'เที่ยงคืน',\n    noon: 'เที่ยง',\n    morning: 'ตอนเช้า',\n    afternoon: 'ตอนกลางวัน',\n    evening: 'ตอนเย็น',\n    night: 'ตอนกลางคืน'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Thai locale.\n * @language Thai\n * @iso-639-2 tha\n * <AUTHOR> [@athivvat]{@link https://github.com/athivvat}\n * <AUTHOR> https://github.com/hawkup}\n * <AUTHOR> I. [@nodtem66]{@link https://github.com/nodtem66}\n */\nvar locale = {\n  code: 'th',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 0\n    /* Sunday */\n    ,\n    firstWeekContainsDate: 1\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^\\d+/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^([bB]|[aA]|คศ)/i,\n  abbreviated: /^([bB]\\.?\\s?[cC]\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?|ค\\.?ศ\\.?)/i,\n  wide: /^(ก่อนคริสตกาล|คริสต์ศักราช|คริสตกาล)/i\n};\nvar parseEraPatterns = {\n  any: [/^[bB]/i, /^(^[aA]|ค\\.?ศ\\.?|คริสตกาล|คริสต์ศักราช|)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^ไตรมาส(ที่)? ?[1234]/i\n};\nvar parseQuarterPatterns = {\n  any: [/(1|แรก|หนึ่ง)/i, /(2|สอง)/i, /(3|สาม)/i, /(4|สี่)/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^(ม\\.?ค\\.?|ก\\.?พ\\.?|มี\\.?ค\\.?|เม\\.?ย\\.?|พ\\.?ค\\.?|มิ\\.?ย\\.?|ก\\.?ค\\.?|ส\\.?ค\\.?|ก\\.?ย\\.?|ต\\.?ค\\.?|พ\\.?ย\\.?|ธ\\.?ค\\.?)/i,\n  abbreviated: /^(ม\\.?ค\\.?|ก\\.?พ\\.?|มี\\.?ค\\.?|เม\\.?ย\\.?|พ\\.?ค\\.?|มิ\\.?ย\\.?|ก\\.?ค\\.?|ส\\.?ค\\.?|ก\\.?ย\\.?|ต\\.?ค\\.?|พ\\.?ย\\.?|ธ\\.?ค\\.?')/i,\n  wide: /^(มกราคม|กุมภาพันธ์|มีนาคม|เมษายน|พฤษภาคม|มิถุนายน|กรกฎาคม|สิงหาคม|กันยายน|ตุลาคม|พฤศจิกายน|ธันวาคม)/i\n};\nvar parseMonthPatterns = {\n  wide: [/^มก/i, /^กุม/i, /^มี/i, /^เม/i, /^พฤษ/i, /^มิ/i, /^กรก/i, /^ส/i, /^กัน/i, /^ต/i, /^พฤศ/i, /^ธ/i],\n  any: [/^ม\\.?ค\\.?/i, /^ก\\.?พ\\.?/i, /^มี\\.?ค\\.?/i, /^เม\\.?ย\\.?/i, /^พ\\.?ค\\.?/i, /^มิ\\.?ย\\.?/i, /^ก\\.?ค\\.?/i, /^ส\\.?ค\\.?/i, /^ก\\.?ย\\.?/i, /^ต\\.?ค\\.?/i, /^พ\\.?ย\\.?/i, /^ธ\\.?ค\\.?/i]\n};\nvar matchDayPatterns = {\n  narrow: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n  short: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n  abbreviated: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n  wide: /^(อาทิตย์|จันทร์|อังคาร|พุธ|พฤหัสบดี|ศุกร์|เสาร์)/i\n};\nvar parseDayPatterns = {\n  wide: [/^อา/i, /^จั/i, /^อั/i, /^พุธ/i, /^พฤ/i, /^ศ/i, /^เส/i],\n  any: [/^อา/i, /^จ/i, /^อ/i, /^พ(?!ฤ)/i, /^พฤ/i, /^ศ/i, /^ส/i]\n};\nvar matchDayPeriodPatterns = {\n  any: /^(ก่อนเที่ยง|หลังเที่ยง|เที่ยงคืน|เที่ยง|(ตอน.*?)?.*(เที่ยง|เช้า|บ่าย|เย็น|กลางคืน))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^ก่อนเที่ยง/i,\n    pm: /^หลังเที่ยง/i,\n    midnight: /^เที่ยงคืน/i,\n    noon: /^เที่ยง/i,\n    morning: /เช้า/i,\n    afternoon: /บ่าย/i,\n    evening: /เย็น/i,\n    night: /กลางคืน/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"eeee'ที่แล้วเวลา' p\",\n  yesterday: \"'เมื่อวานนี้เวลา' p\",\n  today: \"'วันนี้เวลา' p\",\n  tomorrow: \"'พรุ่งนี้เวลา' p\",\n  nextWeek: \"eeee 'เวลา' p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'น้อยกว่า 1 วินาที',\n    other: 'น้อยกว่า {{count}} วินาที'\n  },\n  xSeconds: {\n    one: '1 วินาที',\n    other: '{{count}} วินาที'\n  },\n  halfAMinute: 'ครึ่งนาที',\n  lessThanXMinutes: {\n    one: 'น้อยกว่า 1 นาที',\n    other: 'น้อยกว่า {{count}} นาที'\n  },\n  xMinutes: {\n    one: '1 นาที',\n    other: '{{count}} นาที'\n  },\n  aboutXHours: {\n    one: 'ประมาณ 1 ชั่วโมง',\n    other: 'ประมาณ {{count}} ชั่วโมง'\n  },\n  xHours: {\n    one: '1 ชั่วโมง',\n    other: '{{count}} ชั่วโมง'\n  },\n  xDays: {\n    one: '1 วัน',\n    other: '{{count}} วัน'\n  },\n  aboutXWeeks: {\n    one: 'ประมาณ 1 สัปดาห์',\n    other: 'ประมาณ {{count}} สัปดาห์'\n  },\n  xWeeks: {\n    one: '1 สัปดาห์',\n    other: '{{count}} สัปดาห์'\n  },\n  aboutXMonths: {\n    one: 'ประมาณ 1 เดือน',\n    other: 'ประมาณ {{count}} เดือน'\n  },\n  xMonths: {\n    one: '1 เดือน',\n    other: '{{count}} เดือน'\n  },\n  aboutXYears: {\n    one: 'ประมาณ 1 ปี',\n    other: 'ประมาณ {{count}} ปี'\n  },\n  xYears: {\n    one: '1 ปี',\n    other: '{{count}} ปี'\n  },\n  overXYears: {\n    one: 'มากกว่า 1 ปี',\n    other: 'มากกว่า {{count}} ปี'\n  },\n  almostXYears: {\n    one: 'เกือบ 1 ปี',\n    other: 'เกือบ {{count}} ปี'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      if (token === 'halfAMinute') {\n        return 'ใน' + result;\n      } else {\n        return 'ใน ' + result;\n      }\n    } else {\n      return result + 'ที่ผ่านมา';\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'วันEEEEที่ do MMMM y',\n  long: 'do MMMM y',\n  medium: 'd MMM y',\n  short: 'dd/MM/yyyy'\n};\nvar timeFormats = {\n  full: 'H:mm:ss น. zzzz',\n  long: 'H:mm:ss น. z',\n  medium: 'H:mm:ss น.',\n  short: 'H:mm น.'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'เวลา' {{time}}\",\n  long: \"{{date}} 'เวลา' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'medium'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "_options", "String", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingValues", "defaultFormattingWidth", "module", "_interopRequireDefault", "_index2", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "options", "weekStartsOn", "firstWeekContainsDate", "matchPattern", "parsePattern", "valueCallback", "parseInt", "matchPatterns", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "index", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "token", "_date", "_baseDate", "formatDistanceLocale", "lessThanXSeconds", "one", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "count", "result", "tokenValue", "replace", "addSuffix", "comparison", "date", "formats", "full", "long", "medium", "time", "dateTime"], "sourceRoot": ""}
{"version": 3, "file": "74383.dtale_bundle.js", "mappings": "6EAiEAA,EAAOC,QAzDP,SAAkBC,GAwBhB,MAAO,CACLC,SAAU,WACVC,SAAU,CACR,CACEC,UAAW,UACXC,MAAO,YACPC,IAAK,KACLC,YAAY,EACZC,UAAW,IAEb,CACEJ,UAAW,WACXC,MAAO,qCACPC,IAAK,KACLC,YAAY,EACZE,QAAS,aACTD,UAAW,IAEb,CACEJ,UAAW,WACXC,MAAO,UACPC,IAAK,KACLC,YAAY,GA7CM,CACtBH,UAAW,SACXC,MAAO,IACPC,IAAK,KAEe,CACpBF,UAAW,SACXC,MAAO,IACPC,IAAK,KAEmB,CACxBF,UAAW,SACXC,MAAO,eACPC,IAAK,KACLE,UAAW,GAEc,CACzBJ,UAAW,SACXC,MAAO,cACPC,IAAK,SACLE,UAAW,GA+BTP,EAAKS,mBAGX,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/dsconfig.js"], "sourcesContent": ["/*\n Language: dsconfig\n Description: dsconfig batch configuration language for LDAP directory servers\n Contributors: <PERSON> <<EMAIL>>\n Category: enterprise, config\n */\n\n /** @type LanguageFn */\nfunction dsconfig(hljs) {\n  const QUOTED_PROPERTY = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/\n  };\n  const APOS_PROPERTY = {\n    className: 'string',\n    begin: /'/,\n    end: /'/\n  };\n  const UNQUOTED_PROPERTY = {\n    className: 'string',\n    begin: /[\\w\\-?]+:\\w+/,\n    end: /\\W/,\n    relevance: 0\n  };\n  const VALUELESS_PROPERTY = {\n    className: 'string',\n    begin: /\\w+(\\-\\w+)*/,\n    end: /(?=\\W)/,\n    relevance: 0\n  };\n\n  return {\n    keywords: 'dsconfig',\n    contains: [\n      {\n        className: 'keyword',\n        begin: '^dsconfig',\n        end: /\\s/,\n        excludeEnd: true,\n        relevance: 10\n      },\n      {\n        className: 'built_in',\n        begin: /(list|create|get|set|delete)-(\\w+)/,\n        end: /\\s/,\n        excludeEnd: true,\n        illegal: '!@#$%^&*()',\n        relevance: 10\n      },\n      {\n        className: 'built_in',\n        begin: /--(\\w+)/,\n        end: /\\s/,\n        excludeEnd: true\n      },\n      QUOTED_PROPERTY,\n      APOS_PROPERTY,\n      UNQUOTED_PROPERTY,\n      VALUELESS_PROPERTY,\n      hljs.HASH_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = dsconfig;\n"], "names": ["module", "exports", "hljs", "keywords", "contains", "className", "begin", "end", "excludeEnd", "relevance", "illegal", "HASH_COMMENT_MODE"], "sourceRoot": ""}
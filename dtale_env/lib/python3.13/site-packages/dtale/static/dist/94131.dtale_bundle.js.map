{"version": 3, "file": "94131.dtale_bundle.js", "mappings": "6FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,gCACLC,MAAO,uCAETC,SAAU,CACRF,IAAK,iBACLC,MAAO,0BAETE,YAAa,aACbC,iBAAkB,CAChBJ,IAAK,yBACLC,MAAO,gCAETI,SAAU,CACRL,IAAK,UACLC,MAAO,mBAETK,YAAa,CACXN,IAAK,gBACLC,MAAO,0BAETM,OAAQ,CACNP,IAAK,QACLC,MAAO,kBAETO,MAAO,CACLR,IAAK,UACLC,MAAO,oBAETQ,YAAa,CACXT,IAAK,qBACLC,MAAO,+BAETS,OAAQ,CACNV,IAAK,aACLC,MAAO,uBAETU,aAAc,CACZX,IAAK,kBACLC,MAAO,2BAETW,QAAS,CACPZ,IAAK,UACLC,MAAO,mBAETY,YAAa,CACXb,IAAK,kBACLC,MAAO,4BAETa,OAAQ,CACNd,IAAK,UACLC,MAAO,oBAETc,WAAY,CACVf,IAAK,mBACLC,MAAO,6BAETe,aAAc,CACZhB,IAAK,kBACLC,MAAO,6BA2BPgB,EAvBiB,SAAwBC,EAAOC,EAAOC,GACzD,IAAIC,EACAC,EAAaxB,EAAqBoB,GAUtC,OAPEG,EADwB,iBAAfC,EACAA,EACU,IAAVH,EACAG,EAAWtB,IAEXsB,EAAWrB,MAAMsB,QAAQ,YAAaC,OAAOL,IAGpDC,SAA0CA,EAAQK,UAChDL,EAAQM,YAAcN,EAAQM,WAAa,EACtC,MAAQL,EAERA,EAAS,QAIbA,CACT,EAGAzB,EAAA,QAAkBqB,EAClBU,EAAO/B,QAAUA,EAAQgC,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/el/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'λιγότερο από ένα δευτερόλεπτο',\n    other: 'λιγότερο από {{count}} δευτερόλεπτα'\n  },\n  xSeconds: {\n    one: '1 δευτερόλεπτο',\n    other: '{{count}} δευτερόλεπτα'\n  },\n  halfAMinute: 'μισό λεπτό',\n  lessThanXMinutes: {\n    one: 'λιγότερο από ένα λεπτό',\n    other: 'λιγότερο από {{count}} λεπτά'\n  },\n  xMinutes: {\n    one: '1 λεπτό',\n    other: '{{count}} λεπτά'\n  },\n  aboutXHours: {\n    one: 'περίπου 1 ώρα',\n    other: 'περίπου {{count}} ώρες'\n  },\n  xHours: {\n    one: '1 ώρα',\n    other: '{{count}} ώρες'\n  },\n  xDays: {\n    one: '1 ημέρα',\n    other: '{{count}} ημέρες'\n  },\n  aboutXWeeks: {\n    one: 'περίπου 1 εβδομάδα',\n    other: 'περίπου {{count}} εβδομάδες'\n  },\n  xWeeks: {\n    one: '1 εβδομάδα',\n    other: '{{count}} εβδομάδες'\n  },\n  aboutXMonths: {\n    one: 'περίπου 1 μήνας',\n    other: 'περίπου {{count}} μήνες'\n  },\n  xMonths: {\n    one: '1 μήνας',\n    other: '{{count}} μήνες'\n  },\n  aboutXYears: {\n    one: 'περίπου 1 χρόνο',\n    other: 'περίπου {{count}} χρόνια'\n  },\n  xYears: {\n    one: '1 χρόνο',\n    other: '{{count}} χρόνια'\n  },\n  overXYears: {\n    one: 'πάνω από 1 χρόνο',\n    other: 'πάνω από {{count}} χρόνια'\n  },\n  almostXYears: {\n    one: 'περίπου 1 χρόνο',\n    other: 'περίπου {{count}} χρόνια'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'σε ' + result;\n    } else {\n      return result + ' πριν';\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_default", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison", "module", "default"], "sourceRoot": ""}
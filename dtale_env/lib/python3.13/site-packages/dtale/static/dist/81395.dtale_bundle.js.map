{"version": 3, "file": "81395.dtale_bundle.js", "mappings": "0HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAqB,CAAC,WAAY,SAAU,SAAU,UAAW,cAAe,WAAY,aAEhG,SAASC,EAAKC,GACZ,OAAO,SAAUC,GACf,IAAIC,EAAUJ,EAAmBG,EAAKE,aAEtC,MAAO,GAAGC,OADGJ,EAAW,GAAK,UACJ,KAAKI,OAAOF,EAAS,YAChD,CACF,CAEA,IAAIG,EAAuB,CACzBC,SAAUP,GAAK,GACfQ,UAAW,mBACXC,MAAO,eACPC,SAAU,mBACVC,SAAUX,GAAK,GACfY,MAAO,KAaLC,EAViB,SAAwBC,EAAOZ,GAClD,IAAIa,EAAST,EAAqBQ,GAElC,MAAsB,mBAAXC,EACFA,EAAOb,GAGTa,CACT,EAGAlB,EAAA,QAAkBgB,EAClBG,EAAOnB,QAAUA,EAAQoB,O,kBCnCzBtB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIqB,EAASC,EAAuB,EAAQ,QAExCC,EAAUD,EAAuB,EAAQ,QAEzCE,EAAUF,EAAuB,EAAQ,OAEzCG,EAAUH,EAAuB,EAAQ,QAEzCI,EAAUJ,EAAuB,EAAQ,QAE7C,SAASA,EAAuBK,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEP,QAASO,EAAO,CAY9F,IAcIX,EAdS,CACXa,KAAM,KACNC,eAAgBT,EAAOD,QACvBW,WAAYR,EAAQH,QACpBY,eAAgBR,EAAQJ,QACxBa,SAAUR,EAAQL,QAClBc,MAAOR,EAAQN,QACfe,QAAS,CACPC,aAAc,EAGdC,sBAAuB,IAI3BrC,EAAA,QAAkBgB,EAClBG,EAAOnB,QAAUA,EAAQoB,O,kBC3CzBtB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgC2B,EAF5BN,GAE4BM,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEP,QAASO,GAEvF,IAgCIX,EAda,CACfX,MAAM,EAAIgB,EAAOD,SAAS,CACxBkB,QApBc,CAChBC,KAAM,mBACNC,KAAM,aACNC,OAAQ,YACRC,MAAO,cAiBLC,aAAc,SAEhBC,MAAM,EAAIvB,EAAOD,SAAS,CACxBkB,QAlBc,CAChBC,KAAM,eACNC,KAAM,YACNC,OAAQ,UACRC,MAAO,QAeLC,aAAc,SAEhBE,UAAU,EAAIxB,EAAOD,SAAS,CAC5BkB,QAhBkB,CACpBC,KAAM,oBACNC,KAAM,oBACNC,OAAQ,oBACRC,MAAO,qBAaLC,aAAc,UAIlB3C,EAAA,QAAkBgB,EAClBG,EAAOnB,QAAUA,EAAQoB,O,kBC3CzBtB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIqB,EAASC,EAAuB,EAAQ,QAI5C,SAASA,EAAuBK,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEP,QAASO,EAAO,CAE9F,IAgGIX,EA1CQ,CACV8B,eAAe,EA3DHxB,EAAuB,EAAQ,MA2DhBF,SAAS,CAClC2B,aAxD4B,aAyD5BC,aAxD4B,OAyD5BC,cAAe,SAAuBhD,GACpC,OAAOiD,SAASjD,EAAO,GACzB,IAEFkD,KAAK,EAAI9B,EAAOD,SAAS,CACvBgC,cA7DmB,CACrBC,OAAQ,iBACRC,YAAa,wCACbC,KAAM,2EA2DJC,kBAAmB,OACnBC,cA1DmB,CACrBJ,OAAQ,CAAC,MAAO,QAChBC,YAAa,CAAC,yBAA0B,0BACxCI,IAAK,CAAC,SAAU,sBAwDdC,kBAAmB,QAErBC,SAAS,EAAIvC,EAAOD,SAAS,CAC3BgC,cAzDuB,CACzBC,OAAQ,cACRC,YAAa,uBACbC,KAAM,yCAuDJC,kBAAmB,OACnBC,cAtDuB,CACzBC,IAAK,CAAC,QAAS,SAAU,SAAU,UAsDjCC,kBAAmB,MACnBV,cAAe,SAAuBY,GACpC,OAAOA,EAAQ,CACjB,IAEFC,OAAO,EAAIzC,EAAOD,SAAS,CACzBgC,cA1DqB,CACvBC,OAAQ,mBACRC,YAAa,8FACbC,KAAM,yGAwDJC,kBAAmB,OACnBC,cAvDqB,CACvBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,QAAS,MAAO,MAAO,MAAO,MAAO,SAAU,MAAO,MAAO,OAC3FK,IAAK,CAAC,OAAQ,MAAO,QAAS,OAAQ,QAAS,QAAS,QAAS,OAAQ,MAAO,MAAO,MAAO,QAsD5FC,kBAAmB,QAErBI,KAAK,EAAI1C,EAAOD,SAAS,CACvBgC,cAvDmB,CACrBC,OAAQ,uBACRX,MAAO,wBACPY,YAAa,wBACbC,KAAM,2DAoDJC,kBAAmB,OACnBC,cAnDmB,CACrBJ,OAAQ,CAAC,MAAO,MAAO,MAAO,OAAQ,MAAO,MAAO,QACpDK,IAAK,CAAC,MAAO,MAAO,MAAO,QAAS,MAAO,MAAO,UAkDhDC,kBAAmB,QAErBK,WAAW,EAAI3C,EAAOD,SAAS,CAC7BgC,cAnDyB,CAC3BM,IAAK,sDAmDHF,kBAAmB,MACnBC,cAlDyB,CAC3BC,IAAK,CACHO,GAAI,UACJC,GAAI,UACJC,SAAU,QACVC,KAAM,OACNC,QAAS,OACTC,UAAW,YACXC,QAAS,MACTC,MAAO,SA0CPb,kBAAmB,SAIvB3D,EAAA,QAAkBgB,EAClBG,EAAOnB,QAAUA,EAAQoB,O,kBC7GzBtB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgC2B,EAF5BN,GAE4BM,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEP,QAASO,GAEvF,IA4FIX,EA5BW,CACb8B,cANkB,SAAuB2B,EAAaC,GAEtD,OADaC,OAAOF,GACJ,GAClB,EAIEtB,KAAK,EAAI9B,EAAOD,SAAS,CACvBwD,OAnEY,CACdvB,OAAQ,CAAC,MAAO,QAChBC,YAAa,CAAC,QAAS,UACvBC,KAAM,CAAC,iBAAkB,2BAiEvBZ,aAAc,SAEhBiB,SAAS,EAAIvC,EAAOD,SAAS,CAC3BwD,OAlEgB,CAClBvB,OAAQ,CAAC,KAAM,KAAM,KAAM,MAC3BC,YAAa,CAAC,UAAW,UAAW,UAAW,WAC/CC,KAAM,CAAC,cAAe,cAAe,cAAe,gBAgElDZ,aAAc,OACdkC,iBAAkB,SAA0BjB,GAC1C,OAAOA,EAAU,CACnB,EACAkB,iBAlE0B,CAC5BzB,OAAQ,CAAC,KAAM,MAAO,OAAQ,OAC9BC,YAAa,CAAC,UAAW,WAAY,YAAa,YAClDC,KAAM,CAAC,cAAe,eAAgB,gBAAiB,iBAgErDwB,uBAAwB,SAE1BjB,OAAO,EAAIzC,EAAOD,SAAS,CACzBwD,OAjEc,CAChBvB,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,IAAK,IAAK,KACjEC,YAAa,CAAC,OAAQ,QAAS,QAAS,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,SAAU,OAAQ,OAAQ,QAC1GC,KAAM,CAAC,SAAU,UAAW,UAAW,UAAW,QAAS,SAAU,SAAU,YAAa,aAAc,UAAW,WAAY,aA+D/HZ,aAAc,SAEhBoB,KAAK,EAAI1C,EAAOD,SAAS,CACvBwD,OAhEY,CACdvB,OAAQ,CAAC,IAAK,IAAK,IAAK,KAAM,KAAM,IAAK,MACzCX,MAAO,CAAC,IAAK,IAAK,IAAK,MAAO,KAAM,IAAK,OACzCY,YAAa,CAAC,IAAK,IAAK,IAAK,MAAO,KAAM,IAAK,OAC/CC,KAAM,CAAC,WAAY,QAAS,OAAQ,SAAU,YAAa,SAAU,YA6DnEZ,aAAc,SAEhBqB,WAAW,EAAI3C,EAAOD,SAAS,CAC7BwD,OA9DkB,CACpBvB,OAAQ,CACNY,GAAI,MACJC,GAAI,MACJC,SAAU,QACVC,KAAM,MACNC,QAAS,SACTC,UAAW,MACXC,QAAS,OACTC,MAAO,SAETlB,YAAa,CACXW,GAAI,MACJC,GAAI,MACJC,SAAU,QACVC,KAAM,MACNC,QAAS,SACTC,UAAW,MACXC,QAAS,OACTC,MAAO,SAETjB,KAAM,CACJU,GAAI,MACJC,GAAI,MACJC,SAAU,QACVC,KAAM,MACNC,QAAS,SACTC,UAAW,UACXC,QAAS,OACTC,MAAO,UAkCP7B,aAAc,UAIlB3C,EAAA,QAAkBgB,EAClBG,EAAOnB,QAAUA,EAAQoB,O,gBCvGzBtB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIgF,EAAe,CACjBC,MAAO,aACPC,KAAM,YACNC,OAAQ,UACRC,SAAU,iBAERC,EAAkB,CACpBC,SAAU,aACVC,YAAa,WACbC,SAAU,QACVC,OAAQ,OACRC,MAAO,OACPC,OAAQ,OACRC,QAAS,SACTC,OAAQ,OAENC,EAAe,CACjBR,SAAU,CACR,KAAM,wBACN,EAAK,mBACL,EAAK,eAEPC,YAAa,CACX,KAAM,sBACN,EAAK,iBACL,EAAK,aAEPC,SAAU,CACR,KAAM,mBACN,EAAK,cACL,EAAK,UAEPC,OAAQ,CACN,KAAM,kBACN,EAAK,aACL,EAAK,UAEPC,MAAO,CACL,KAAM,kBACN,EAAK,aACL,EAAK,UAEPC,OAAQ,CACN,KAAM,kBACN,EAAK,aACL,EAAK,SAEPC,QAAS,CACP,KAAM,oBACN,EAAK,eACL,EAAK,YAEPC,OAAQ,CACN,KAAM,iBACN,EAAK,YACL,EAAK,SAqBL7E,EAjBiB,SAAwBC,EAAO8E,EAAO5D,GACzD,IAAI6D,EAAS/E,EAAMiB,MAAM,+BACrB+D,EAAOD,EAAS/E,EAAMiF,QAAQF,EAAO,GAAI,IAAM/E,EAC/CkF,GAAsF,KAAzEhE,aAAyC,EAASA,EAAQgE,WACvEC,EAAMH,EAAKI,cACXC,GAAcnE,aAAyC,EAASA,EAAQmE,aAAe,EACvFC,EAAaJ,EAAYL,EAAaM,GAAKE,GAAcjB,EAAgBe,GACzEI,EAAiB,gBAARJ,EAAwBG,EAAaR,EAAQQ,EAE1D,GAAIP,EAAQ,CACV,IAAIS,EAAMT,EAAO,GAAGK,cACpBG,EAASxB,EAAayB,GAAO,IAAMD,CACrC,CAEA,OAAOA,CACT,EAGAxG,EAAA,QAAkBgB,EAClBG,EAAOnB,QAAUA,EAAQoB,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/hu/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/locale/hu/index.js", "webpack://dtale/./node_modules/date-fns/locale/hu/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/hu/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/hu/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/hu/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar accusativeWeekdays = ['vas<PERSON>rnap', 'hétfőn', 'kedden', 'szerd<PERSON>', 'cs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'p<PERSON><PERSON><PERSON>', 'szombaton'];\n\nfunction week(isFuture) {\n  return function (date) {\n    var weekday = accusativeWeekdays[date.getUTCDay()];\n    var prefix = isFuture ? '' : \"'múlt' \";\n    return \"\".concat(prefix, \"'\").concat(weekday, \"' p'-kor'\");\n  };\n}\n\nvar formatRelativeLocale = {\n  lastWeek: week(false),\n  yesterday: \"'tegnap' p'-kor'\",\n  today: \"'ma' p'-kor'\",\n  tomorrow: \"'holnap' p'-kor'\",\n  nextWeek: week(true),\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, date) {\n  var format = formatRelativeLocale[token];\n\n  if (typeof format === 'function') {\n    return format(date);\n  }\n\n  return format;\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Hungarian locale.\n * @language Hungarian\n * @iso-639-2 hun\n * <AUTHOR> [@pshpak]{@link https://github.com/pshpak}\n * <AUTHOR> [@eduardopsll]{@link https://github.com/eduardopsll}\n * <AUTHOR> [@twodcube]{@link https://github.com/twodcube}\n */\nvar locale = {\n  code: 'hu',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 1\n    /* Monday */\n    ,\n    firstWeekContainsDate: 4\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'y. MMMM d., EEEE',\n  long: 'y. MMMM d.',\n  medium: 'y. MMM d.',\n  short: 'y. MM. dd.'\n};\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  full: '{{date}} {{time}}',\n  long: '{{date}} {{time}}',\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)\\.?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(ie\\.|isz\\.)/i,\n  abbreviated: /^(i\\.\\s?e\\.?|b?\\s?c\\s?e|i\\.\\s?sz\\.?)/i,\n  wide: /^(<PERSON><PERSON><PERSON>|időszámításunk előtt|időszámításunk szerint|i\\. sz\\.)/i\n};\nvar parseEraPatterns = {\n  narrow: [/ie/i, /isz/i],\n  abbreviated: [/^(i\\.?\\s?e\\.?|b\\s?ce)/i, /^(i\\.?\\s?sz\\.?|c\\s?e)/i],\n  any: [/előtt/i, /(szerint|i. sz.)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]\\.?/i,\n  abbreviated: /^[1234]?\\.?\\s?n\\.év/i,\n  wide: /^([1234]|I|II|III|IV)?\\.?\\s?negyedév/i\n};\nvar parseQuarterPatterns = {\n  any: [/1|I$/i, /2|II$/i, /3|III/i, /4|IV/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[jfmaásond]|sz/i,\n  abbreviated: /^(jan\\.?|febr\\.?|márc\\.?|ápr\\.?|máj\\.?|jún\\.?|júl\\.?|aug\\.?|szept\\.?|okt\\.?|nov\\.?|dec\\.?)/i,\n  wide: /^(január|február|március|április|május|június|július|augusztus|szeptember|október|november|december)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a|á/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s|sz/i, /^o/i, /^n/i, /^d/i],\n  any: [/^ja/i, /^f/i, /^már/i, /^áp/i, /^máj/i, /^jún/i, /^júl/i, /^au/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns = {\n  narrow: /^([vhkpc]|sz|cs|sz)/i,\n  short: /^([vhkp]|sze|cs|szo)/i,\n  abbreviated: /^([vhkp]|sze|cs|szo)/i,\n  wide: /^(vasárnap|hétfő|kedd|szerda|csütörtök|péntek|szombat)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^v/i, /^h/i, /^k/i, /^sz/i, /^c/i, /^p/i, /^sz/i],\n  any: [/^v/i, /^h/i, /^k/i, /^sze/i, /^c/i, /^p/i, /^szo/i]\n};\nvar matchDayPeriodPatterns = {\n  any: /^((de|du)\\.?|éjfél|délután|dél|reggel|este|éjjel)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^de\\.?/i,\n    pm: /^du\\.?/i,\n    midnight: /^éjf/i,\n    noon: /^dé/i,\n    morning: /reg/i,\n    afternoon: /^délu\\.?/i,\n    evening: /es/i,\n    night: /éjj/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['ie.', 'isz.'],\n  abbreviated: ['i. e.', 'i. sz.'],\n  wide: ['<PERSON><PERSON><PERSON> el<PERSON>', 'időszámításunk szerint']\n};\nvar quarterValues = {\n  narrow: ['1.', '2.', '3.', '4.'],\n  abbreviated: ['1. n.év', '2. n.év', '3. n.év', '4. n.év'],\n  wide: ['1. negyedév', '2. negyedév', '3. negyedév', '4. negyedév']\n};\nvar formattingQuarterValues = {\n  narrow: ['I.', 'II.', 'III.', 'IV.'],\n  abbreviated: ['I. n.év', 'II. n.év', 'III. n.év', 'IV. n.év'],\n  wide: ['I. negyedév', 'II. negyedév', 'III. negyedév', 'IV. negyedév']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'Á', 'M', 'J', 'J', 'A', 'Sz', 'O', 'N', 'D'],\n  abbreviated: ['jan.', 'febr.', 'márc.', 'ápr.', 'máj.', 'jún.', 'júl.', 'aug.', 'szept.', 'okt.', 'nov.', 'dec.'],\n  wide: ['január', 'február', 'március', 'április', 'május', 'június', 'július', 'augusztus', 'szeptember', 'október', 'november', 'december']\n};\nvar dayValues = {\n  narrow: ['V', 'H', 'K', 'Sz', 'Cs', 'P', 'Sz'],\n  short: ['V', 'H', 'K', 'Sze', 'Cs', 'P', 'Szo'],\n  abbreviated: ['V', 'H', 'K', 'Sze', 'Cs', 'P', 'Szo'],\n  wide: ['vasárnap', 'hétfő', 'kedd', 'szerda', 'csütörtök', 'péntek', 'szombat']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'de.',\n    pm: 'du.',\n    midnight: 'éjfél',\n    noon: 'dél',\n    morning: 'reggel',\n    afternoon: 'du.',\n    evening: 'este',\n    night: 'éjjel'\n  },\n  abbreviated: {\n    am: 'de.',\n    pm: 'du.',\n    midnight: 'éjfél',\n    noon: 'dél',\n    morning: 'reggel',\n    afternoon: 'du.',\n    evening: 'este',\n    night: 'éjjel'\n  },\n  wide: {\n    am: 'de.',\n    pm: 'du.',\n    midnight: 'éjfél',\n    noon: 'dél',\n    morning: 'reggel',\n    afternoon: 'délután',\n    evening: 'este',\n    night: 'éjjel'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    },\n    formattingValues: formattingQuarterValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar translations = {\n  about: 'kör<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  over: 'több mint',\n  almost: 'majdnem',\n  lessthan: 'kevesebb mint'\n};\nvar withoutSuffixes = {\n  xseconds: ' másodperc',\n  halfaminute: 'fél perc',\n  xminutes: ' perc',\n  xhours: ' óra',\n  xdays: ' nap',\n  xweeks: ' hét',\n  xmonths: ' hónap',\n  xyears: ' év'\n};\nvar withSuffixes = {\n  xseconds: {\n    '-1': ' másodperccel ezelőtt',\n    '1': ' másodperc múlva',\n    '0': ' másodperce'\n  },\n  halfaminute: {\n    '-1': 'fél perccel ezelőtt',\n    '1': 'fél perc múlva',\n    '0': 'fél perce'\n  },\n  xminutes: {\n    '-1': ' perccel ezelőtt',\n    '1': ' perc múlva',\n    '0': ' perce'\n  },\n  xhours: {\n    '-1': ' ór<PERSON>val e<PERSON>',\n    '1': ' óra múlva',\n    '0': ' órája'\n  },\n  xdays: {\n    '-1': ' nappal ezelőtt',\n    '1': ' nap múlva',\n    '0': ' napja'\n  },\n  xweeks: {\n    '-1': ' héttel ezelőtt',\n    '1': ' hét múlva',\n    '0': ' hete'\n  },\n  xmonths: {\n    '-1': ' hónappal ezelőtt',\n    '1': ' hónap múlva',\n    '0': ' hónapja'\n  },\n  xyears: {\n    '-1': ' évvel ezelőtt',\n    '1': ' év múlva',\n    '0': ' éve'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var adverb = token.match(/about|over|almost|lessthan/i);\n  var unit = adverb ? token.replace(adverb[0], '') : token;\n  var addSuffix = (options === null || options === void 0 ? void 0 : options.addSuffix) === true;\n  var key = unit.toLowerCase();\n  var comparison = (options === null || options === void 0 ? void 0 : options.comparison) || 0;\n  var translated = addSuffix ? withSuffixes[key][comparison] : withoutSuffixes[key];\n  var result = key === 'halfaminute' ? translated : count + translated;\n\n  if (adverb) {\n    var adv = adverb[0].toLowerCase();\n    result = translations[adv] + ' ' + result;\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "accusativeWeekdays", "week", "isFuture", "date", "weekday", "getUTCDay", "concat", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "_default", "token", "format", "module", "default", "_index", "_interopRequireDefault", "_index2", "_index3", "_index4", "_index5", "obj", "__esModule", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "options", "weekStartsOn", "firstWeekContainsDate", "formats", "full", "long", "medium", "short", "defaultWidth", "time", "dateTime", "ordinalNumber", "matchPattern", "parsePattern", "valueCallback", "parseInt", "era", "matchPatterns", "narrow", "abbreviated", "wide", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "quarter", "index", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "dirtyNumber", "_options", "Number", "values", "argument<PERSON>allback", "formattingValues", "defaultFormattingWidth", "translations", "about", "over", "almost", "lessthan", "withoutSuffixes", "xseconds", "halfaminute", "xminutes", "xhours", "xdays", "xweeks", "xmonths", "xyears", "withSuffixes", "count", "adverb", "unit", "replace", "addSuffix", "key", "toLowerCase", "comparison", "translated", "result", "adv"], "sourceRoot": ""}
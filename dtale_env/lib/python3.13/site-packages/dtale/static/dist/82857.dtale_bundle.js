"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[82857],{65014:(e,t,d)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l,a=(l=d(19059))&&l.__esModule?l:{default:l};var u={date:(0,a.default)({formats:{full:"EEEE d. MMMM y",long:"d. MMMM y",medium:"d. M. y",short:"d. M. y"},defaultWidth:"full"}),time:(0,a.default)({formats:{full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},defaultWidth:"full"}),dateTime:(0,a.default)({formats:{full:"{{date}}, {{time}}",long:"{{date}}, {{time}}",medium:"{{date}}, {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};t.default=u,e.exports=t.default}}]);
//# sourceMappingURL=82857.dtale_bundle.js.map
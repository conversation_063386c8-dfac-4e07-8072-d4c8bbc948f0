{"version": 3, "file": "98260.dtale_bundle.js", "mappings": "2HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,eACLC,IAAK,iBACLC,WAAY,yBACZC,MAAO,0BAETC,SAAU,CACRJ,IAAK,QACLC,IAAK,UACLC,WAAY,kBACZC,MAAO,mBAETE,YAAa,WACbC,iBAAkB,CAChBN,IAAK,eACLC,IAAK,iBACLC,WAAY,yBACZC,MAAO,0BAETI,SAAU,CACRP,IAAK,QACLC,IAAK,UACLC,WAAY,kBACZC,MAAO,mBAETK,YAAa,CACXR,IAAK,aACLC,IAAK,eACLC,WAAY,wBACZC,MAAO,wBAETM,OAAQ,CACNT,IAAK,OACLC,IAAK,SACLC,WAAY,kBACZC,MAAO,kBAETO,MAAO,CACLV,IAAK,MACLC,IAAK,QACLC,WAAY,iBACZC,MAAO,iBAETQ,YAAa,CACXX,IAAK,cACLC,IAAK,gBACLC,WAAY,yBACZC,MAAO,yBAETS,OAAQ,CACNZ,IAAK,QACLC,IAAK,UACLC,WAAY,mBACZC,MAAO,mBAETU,aAAc,CACZb,IAAK,YACLC,IAAK,cACLC,WAAY,uBACZC,MAAO,uBAETW,QAAS,CACPd,IAAK,MACLC,IAAK,QACLC,WAAY,iBACZC,MAAO,iBAETY,YAAa,CACXf,IAAK,YACLC,IAAK,cACLC,WAAY,uBACZC,MAAO,uBAETa,OAAQ,CACNhB,IAAK,MACLC,IAAK,QACLC,WAAY,kBACZC,MAAO,iBAETc,WAAY,CACVjB,IAAK,cACLC,IAAK,gBACLC,WAAY,yBACZC,MAAO,yBAETe,aAAc,CACZlB,IAAK,cACLC,IAAK,gBACLC,WAAY,0BACZC,MAAO,0BA+BPgB,EA3BiB,SAAwBC,EAAOC,EAAOC,GACzD,IAAIC,EACAC,EAAa1B,EAAqBsB,GActC,OAXEG,EADwB,iBAAfC,EACAA,EACU,IAAVH,EACAG,EAAWxB,IACD,IAAVqB,EACAG,EAAWvB,IACXoB,GAAS,GACTG,EAAWtB,WAAWuB,QAAQ,YAAaC,OAAOL,IAElDG,EAAWrB,MAAMsB,QAAQ,YAAaC,OAAOL,IAGpDC,SAA0CA,EAAQK,UAChDL,EAAQM,YAAcN,EAAQM,WAAa,EACtC,WAAyCC,OAAON,GAEhD,OAAsBM,OAAON,GAIjCA,CACT,EAGA3B,EAAA,QAAkBuB,EAClBW,EAAOlC,QAAUA,EAAQmC,O,kBC/HzBrC,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCoC,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAED,QAASC,GAEvF,IAsHIb,EA5BW,CACbgB,cALkB,SAAuBC,EAAaC,GACtD,OAAOX,OAAOU,EAChB,EAIEE,KAAK,EAAIL,EAAOF,SAAS,CACvBQ,OA7FY,CACdC,OAAQ,CAAC,IAAK,KACdC,YAAa,CAAC,MAAO,OACrBC,KAAM,CAAC,cAAe,gBA2FpBC,aAAc,SAEhBC,SAAS,EAAIX,EAAOF,SAAS,CAC3BQ,OA5FgB,CAClBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,KAAM,KAAM,KAAM,MAChCC,KAAM,CAAC,cAAe,eAAgB,eAAgB,iBA0FpDC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAIb,EAAOF,SAAS,CACzBQ,OA9Fc,CAChBC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,MAAO,MAAO,OAAQ,QAAS,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QACrGC,KAAM,CAAC,QAAS,SAAU,OAAQ,QAAS,OAAQ,QAAS,QAAS,QAAS,SAAU,SAAU,SAAU,WA4F1GC,aAAc,SAEhBI,KAAK,EAAId,EAAOF,SAAS,CACvBQ,OA7FY,CACdC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCQ,MAAO,CAAC,MAAO,QAAS,SAAU,SAAU,OAAQ,OAAQ,OAC5DP,YAAa,CAAC,MAAO,QAAS,SAAU,SAAU,OAAQ,OAAQ,OAClEC,KAAM,CAAC,QAAS,UAAW,WAAY,WAAY,SAAU,SAAU,UA0FrEC,aAAc,SAEhBM,WAAW,EAAIhB,EAAOF,SAAS,CAC7BQ,OA3FkB,CACpBC,OAAQ,CACNU,GAAI,IACJC,GAAI,IACJC,SAAU,IACVC,KAAM,IACNC,QAAS,SACTC,UAAW,YACXC,QAAS,QACTC,MAAO,SAEThB,YAAa,CACXS,GAAI,IACJC,GAAI,IACJC,SAAU,YACVC,KAAM,QACNC,QAAS,SACTC,UAAW,YACXC,QAAS,QACTC,MAAO,SAETf,KAAM,CACJQ,GAAI,IACJC,GAAI,IACJC,SAAU,YACVC,KAAM,QACNC,QAAS,SACTC,UAAW,YACXC,QAAS,QACTC,MAAO,UA+DPd,aAAc,OACde,iBA7D4B,CAC9BlB,OAAQ,CACNU,GAAI,IACJC,GAAI,IACJC,SAAU,IACVC,KAAM,IACNC,QAAS,YACTC,UAAW,YACXC,QAAS,YACTC,MAAO,YAEThB,YAAa,CACXS,GAAI,IACJC,GAAI,IACJC,SAAU,YACVC,KAAM,QACNC,QAAS,YACTC,UAAW,YACXC,QAAS,YACTC,MAAO,YAETf,KAAM,CACJQ,GAAI,IACJC,GAAI,IACJC,SAAU,YACVE,QAAS,YACTD,KAAM,QACNE,UAAW,YACXC,QAAS,YACTC,MAAO,aAiCPE,uBAAwB,UAI5B/D,EAAA,QAAkBuB,EAClBW,EAAOlC,QAAUA,EAAQmC,O,kBCjIzBrC,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCoC,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAED,QAASC,GAEvF,IAgCIb,EAda,CACfyC,MAAM,EAAI3B,EAAOF,SAAS,CACxB8B,QApBc,CAChBC,KAAM,kBACNC,KAAM,YACNC,OAAQ,WACRhB,MAAO,UAiBLL,aAAc,SAEhBsB,MAAM,EAAIhC,EAAOF,SAAS,CACxB8B,QAlBc,CAChBC,KAAM,iBACNC,KAAM,cACNC,OAAQ,YACRhB,MAAO,UAeLL,aAAc,SAEhBuB,UAAU,EAAIjC,EAAOF,SAAS,CAC5B8B,QAhBkB,CACpBC,KAAM,6BACNC,KAAM,6BACNC,OAAQ,qBACRhB,MAAO,sBAaLL,aAAc,UAIlB/C,EAAA,QAAkBuB,EAClBW,EAAOlC,QAAUA,EAAQmC,O,gBC3CzBrC,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIuE,EAAuB,CACzBC,SAAU,2BACVC,UAAW,oBACXC,MAAO,sBACPC,SAAU,kBACVC,SAAU,kBACVrE,MAAO,KAOLgB,EAJiB,SAAwBC,EAAOqD,EAAOC,EAAWrC,GACpE,OAAO8B,EAAqB/C,EAC9B,EAGAxB,EAAA,QAAkBuB,EAClBW,EAAOlC,QAAUA,EAAQmC,O,kBCnBzBrC,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIqC,EAAS0C,EAAuB,EAAQ,OAExCC,EAAUD,EAAuB,EAAQ,QAEzCE,EAAUF,EAAuB,EAAQ,QAEzCG,EAAUH,EAAuB,EAAQ,QAEzCI,EAAUJ,EAAuB,EAAQ,QAE7C,SAASA,EAAuB3C,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAED,QAASC,EAAO,CAU9F,IAcIb,EAdS,CACX6D,KAAM,QACNC,eAAgBhD,EAAOF,QACvBmD,WAAYN,EAAQ7C,QACpBoD,eAAgBN,EAAQ9C,QACxBqD,SAAUN,EAAQ/C,QAClBsD,MAAON,EAAQhD,QACfT,QAAS,CACPgE,aAAc,EAGdC,sBAAuB,IAI3B3F,EAAA,QAAkBuB,EAClBW,EAAOlC,QAAUA,EAAQmC,O,kBCzCzBrC,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIqC,EAAS0C,EAAuB,EAAQ,QAI5C,SAASA,EAAuB3C,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAED,QAASC,EAAO,CAE9F,IAkGIb,EA1CQ,CACVgB,eAAe,EA7DHwC,EAAuB,EAAQ,MA6DhB5C,SAAS,CAClCyD,aA1D4B,SA2D5BC,aA1D4B,OA2D5BC,cAAe,SAAuB7F,GACpC,OAAO8F,SAAS9F,EAAO,GACzB,IAEFyC,KAAK,EAAIL,EAAOF,SAAS,CACvB6D,cA/DmB,CACrBpD,OAAQ,UACRC,YAAa,cACbC,KAAM,+BA6DJmD,kBAAmB,OACnBC,cA5DmB,CACrBC,IAAK,CAAC,MAAO,QA4DXC,kBAAmB,QAErBpD,SAAS,EAAIX,EAAOF,SAAS,CAC3B6D,cA7DuB,CACzBpD,OAAQ,UACRC,YAAa,WACbC,KAAM,uCA2DJmD,kBAAmB,OACnBC,cA1DuB,CACzBpD,KAAM,CAAC,cAAe,eAAgB,eAAgB,gBACtDqD,IAAK,CAAC,IAAK,IAAK,IAAK,MAyDnBC,kBAAmB,MACnBN,cAAe,SAAuBO,GACpC,OAAOA,EAAQ,CACjB,IAEFnD,OAAO,EAAIb,EAAOF,SAAS,CACzB6D,cA7DqB,CACvBpD,OAAQ,mBACRC,YAAa,gEACbC,KAAM,iFA2DJmD,kBAAmB,OACnBC,cA1DqB,CACvBtD,OAAQ,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC3EuD,IAAK,CAAC,OAAQ,OAAQ,QAAS,SAAU,QAAS,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,SAyDhGC,kBAAmB,QAErBjD,KAAK,EAAId,EAAOF,SAAS,CACvB6D,cA1DmB,CACrBpD,OAAQ,mBACRQ,MAAO,2CACPP,YAAa,2CACbC,KAAM,0DAuDJmD,kBAAmB,OACnBC,cAtDmB,CACrBtD,OAAQ,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC7CuD,IAAK,CAAC,MAAO,QAAS,SAAU,SAAU,OAAQ,OAAQ,QAqDxDC,kBAAmB,QAErB/C,WAAW,EAAIhB,EAAOF,SAAS,CAC7B6D,cAtDyB,CAC3BpD,OAAQ,oDACRC,YAAa,gEACbC,KAAM,gEACNqD,IAAK,4BAmDHF,kBAAmB,MACnBC,cAlDyB,CAC3BC,IAAK,CACH7C,GAAI,KACJC,GAAI,KACJC,SAAU,KACVC,KAAM,KACNC,QAAS,KACTC,UAAW,OACXC,QAAS,KACTC,MAAO,OA0CPuC,kBAAmB,SAIvBpG,EAAA,QAAkBuB,EAClBW,EAAOlC,QAAUA,EAAQmC,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/ar-EG/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/ar-EG/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/ar-EG/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/ar-EG/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/locale/ar-EG/index.js", "webpack://dtale/./node_modules/date-fns/locale/ar-EG/_lib/match/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'أقل من ثانية',\n    two: 'أقل من ثانيتين',\n    threeToTen: 'أقل من {{count}} ثواني',\n    other: 'أقل من {{count}} ثانية'\n  },\n  xSeconds: {\n    one: 'ثانية',\n    two: 'ثانيتين',\n    threeToTen: '{{count}} ثواني',\n    other: '{{count}} ثانية'\n  },\n  halfAMinute: 'نص دقيقة',\n  lessThanXMinutes: {\n    one: 'أقل من دقيقة',\n    two: 'أقل من دقيقتين',\n    threeToTen: 'أقل من {{count}} دقايق',\n    other: 'أقل من {{count}} دقيقة'\n  },\n  xMinutes: {\n    one: 'دقيقة',\n    two: 'دقيقتين',\n    threeToTen: '{{count}} دقايق',\n    other: '{{count}} دقيقة'\n  },\n  aboutXHours: {\n    one: 'حوالي ساعة',\n    two: 'حوالي ساعتين',\n    threeToTen: 'حوالي {{count}} ساعات',\n    other: 'حوالي {{count}} ساعة'\n  },\n  xHours: {\n    one: 'ساعة',\n    two: 'ساعتين',\n    threeToTen: '{{count}} ساعات',\n    other: '{{count}} ساعة'\n  },\n  xDays: {\n    one: 'يوم',\n    two: 'يومين',\n    threeToTen: '{{count}} أيام',\n    other: '{{count}} يوم'\n  },\n  aboutXWeeks: {\n    one: 'حوالي أسبوع',\n    two: 'حوالي أسبوعين',\n    threeToTen: 'حوالي {{count}} أسابيع',\n    other: 'حوالي {{count}} أسبوع'\n  },\n  xWeeks: {\n    one: 'أسبوع',\n    two: 'أسبوعين',\n    threeToTen: '{{count}} أسابيع',\n    other: '{{count}} أسبوع'\n  },\n  aboutXMonths: {\n    one: 'حوالي شهر',\n    two: 'حوالي شهرين',\n    threeToTen: 'حوالي {{count}} أشهر',\n    other: 'حوالي {{count}} شهر'\n  },\n  xMonths: {\n    one: 'شهر',\n    two: 'شهرين',\n    threeToTen: '{{count}} أشهر',\n    other: '{{count}} شهر'\n  },\n  aboutXYears: {\n    one: 'حوالي سنة',\n    two: 'حوالي سنتين',\n    threeToTen: 'حوالي {{count}} سنين',\n    other: 'حوالي {{count}} سنة'\n  },\n  xYears: {\n    one: 'عام',\n    two: 'عامين',\n    threeToTen: '{{count}} أعوام',\n    other: '{{count}} عام'\n  },\n  overXYears: {\n    one: 'أكثر من سنة',\n    two: 'أكثر من سنتين',\n    threeToTen: 'أكثر من {{count}} سنين',\n    other: 'أكثر من {{count}} سنة'\n  },\n  almostXYears: {\n    one: 'عام تقريبًا',\n    two: 'عامين تقريبًا',\n    threeToTen: '{{count}} أعوام تقريبًا',\n    other: '{{count}} عام تقريبًا'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2) {\n    result = tokenValue.two;\n  } else if (count <= 10) {\n    result = tokenValue.threeToTen.replace('{{count}}', String(count));\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"\\u0641\\u064A \\u062E\\u0644\\u0627\\u0644 \".concat(result);\n    } else {\n      return \"\\u0645\\u0646\\u0630 \".concat(result);\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['ق', 'ب'],\n  abbreviated: ['ق.م', 'ب.م'],\n  wide: ['قبل الميلاد', 'بعد الميلاد']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['ر1', 'ر2', 'ر3', 'ر4'],\n  wide: ['الربع الأول', 'الربع الثاني', 'الربع الثالث', 'الربع الرابع']\n};\nvar monthValues = {\n  narrow: ['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'],\n  abbreviated: ['ينا', 'فبر', 'مارس', 'أبريل', 'مايو', 'يونـ', 'يولـ', 'أغسـ', 'سبتـ', 'أكتـ', 'نوفـ', 'ديسـ'],\n  wide: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر']\n};\nvar dayValues = {\n  narrow: ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],\n  short: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],\n  abbreviated: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],\n  wide: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'ن',\n    noon: 'ظ',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءً',\n    night: 'ليلاً'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهراً',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءً',\n    night: 'ليلاً'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهراً',\n    morning: 'صباحاً',\n    afternoon: 'بعد الظهر',\n    evening: 'مساءً',\n    night: 'ليلاً'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'ن',\n    noon: 'ظ',\n    morning: 'في الصباح',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  },\n  abbreviated: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    noon: 'ظهراً',\n    morning: 'في الصباح',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  },\n  wide: {\n    am: 'ص',\n    pm: 'م',\n    midnight: 'نصف الليل',\n    morning: 'في الصباح',\n    noon: 'ظهراً',\n    afternoon: 'بعد الظهر',\n    evening: 'في المساء',\n    night: 'في الليل'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'EEEE، do MMMM y',\n  long: 'do MMMM y',\n  medium: 'dd/MMM/y',\n  short: 'd/MM/y'\n};\nvar timeFormats = {\n  full: 'h:mm:ss a zzzz',\n  long: 'h:mm:ss a z',\n  medium: 'h:mm:ss a',\n  short: 'h:mm a'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'الساعة' {{time}}\",\n  long: \"{{date}} 'الساعة' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"eeee 'اللي جاي الساعة' p\",\n  yesterday: \"'إمبارح الساعة' p\",\n  today: \"'النهاردة الساعة' p\",\n  tomorrow: \"'بكرة الساعة' p\",\n  nextWeek: \"eeee 'الساعة' p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Arabic locale (Egypt).\n * @language Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@AbdAllahAbdElFattah13]{@link https://github.com/AbdAllahAbdElFattah13}\n */\nvar locale = {\n  code: 'ar-EG',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 0\n    /* Sunday */\n    ,\n    firstWeekContainsDate: 1\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)/;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(ق|ب)/g,\n  abbreviated: /^(ق.م|ب.م)/g,\n  wide: /^(قبل الميلاد|بعد الميلاد)/g\n};\nvar parseEraPatterns = {\n  any: [/^ق/g, /^ب/g]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/,\n  abbreviated: /^ر[1234]/,\n  wide: /^الربع (الأول|الثاني|الثالث|الرابع)/\n};\nvar parseQuarterPatterns = {\n  wide: [/الربع الأول/, /الربع الثاني/, /الربع الثالث/, /الربع الرابع/],\n  any: [/1/, /2/, /3/, /4/]\n};\nvar matchMonthPatterns = {\n  narrow: /^(ي|ف|م|أ|س|ن|د)/,\n  abbreviated: /^(ينا|فبر|مارس|أبريل|مايو|يونـ|يولـ|أغسـ|سبتـ|أكتـ|نوفـ|ديسـ)/,\n  wide: /^(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)/\n};\nvar parseMonthPatterns = {\n  narrow: [/^ي/, /^ف/, /^م/, /^أ/, /^م/, /^ي/, /^ي/, /^أ/, /^س/, /^أ/, /^ن/, /^د/],\n  any: [/^ينا/, /^فبر/, /^مارس/, /^أبريل/, /^مايو/, /^يون/, /^يول/, /^أغس/, /^سبت/, /^أكت/, /^نوف/, /^ديس/]\n};\nvar matchDayPatterns = {\n  narrow: /^(ح|ن|ث|ر|خ|ج|س)/,\n  short: /^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/,\n  abbreviated: /^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/,\n  wide: /^(الأحد|الاثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)/\n};\nvar parseDayPatterns = {\n  narrow: [/^ح/, /^ن/, /^ث/, /^ر/, /^خ/, /^ج/, /^س/],\n  any: [/أحد/, /اثنين/, /ثلاثاء/, /أربعاء/, /خميس/, /جمعة/, /سبت/]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(ص|م|ن|ظ|في الصباح|بعد الظهر|في المساء|في الليل)/,\n  abbreviated: /^(ص|م|نصف الليل|ظهراً|في الصباح|بعد الظهر|في المساء|في الليل)/,\n  wide: /^(ص|م|نصف الليل|في الصباح|ظهراً|بعد الظهر|في المساء|في الليل)/,\n  any: /^(ص|م|صباح|ظهر|مساء|ليل)/\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^ص/,\n    pm: /^م/,\n    midnight: /^ن/,\n    noon: /^ظ/,\n    morning: /^ص/,\n    afternoon: /^بعد/,\n    evening: /^م/,\n    night: /^ل/\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "one", "two", "threeToTen", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_default", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison", "concat", "module", "default", "obj", "_index", "__esModule", "ordinalNumber", "dirtyNumber", "_options", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingValues", "defaultFormattingWidth", "date", "formats", "full", "long", "medium", "time", "dateTime", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "_date", "_baseDate", "_interopRequireDefault", "_index2", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "weekStartsOn", "firstWeekContainsDate", "matchPattern", "parsePattern", "valueCallback", "parseInt", "matchPatterns", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "index"], "sourceRoot": ""}
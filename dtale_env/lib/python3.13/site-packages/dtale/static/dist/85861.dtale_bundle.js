"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[85861],{45636:(e,t,d)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l,a=(l=d(19059))&&l.__esModule?l:{default:l};var u={date:(0,a.default)({formats:{full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd.MM.y"},defaultWidth:"full"}),time:(0,a.default)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,a.default)({formats:{full:"{{date}} 'à' {{time}}",long:"{{date}} 'à' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})};t.default=u,e.exports=t.default}}]);
//# sourceMappingURL=85861.dtale_bundle.js.map
{"version": 3, "file": "82857.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAGvF,IAkCII,EAda,CACfC,MAAM,EAAIJ,EAAOE,SAAS,CACxBG,QAtBc,CAChBC,KAAM,iBACNC,KAAM,YACNC,OAAQ,UACRC,MAAO,WAmBLC,aAAc,SAEhBC,MAAM,EAAIX,EAAOE,SAAS,CACxBG,QAnBc,CAChBC,KAAM,eACNC,KAAM,YACNC,OAAQ,UACRC,MAAO,QAgBLC,aAAc,SAEhBE,UAAU,EAAIZ,EAAOE,SAAS,CAC5BG,QAhBkB,CACpBC,KAAM,qBACNC,KAAM,qBACNC,OAAQ,qBACRC,MAAO,qBAaLC,aAAc,UAIlBb,EAAA,QAAkBM,EAClBU,EAAOhB,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/sk/_lib/formatLong/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1986\nvar dateFormats = {\n  full: 'EEEE d. MMMM y',\n  long: 'd. MMMM y',\n  medium: 'd. M. y',\n  short: 'd. M. y'\n}; // https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#2149\n\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n}; // https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1994\n\nvar dateTimeFormats = {\n  full: '{{date}}, {{time}}',\n  long: '{{date}}, {{time}}',\n  medium: '{{date}}, {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "date", "formats", "full", "long", "medium", "short", "defaultWidth", "time", "dateTime", "module"], "sourceRoot": ""}
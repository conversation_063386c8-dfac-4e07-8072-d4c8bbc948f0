"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[77666],{72007:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d,a=(d=l(19059))&&d.__esModule?d:{default:d};var u={date:(0,a.default)({formats:{full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"y-MM-dd"},defaultWidth:"full"}),time:(0,a.default)({formats:{full:"'kl'. HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,a.default)({formats:{full:"{{date}} 'kl.' {{time}}",long:"{{date}} 'kl.' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};t.default=u,e.exports=t.default}}]);
//# sourceMappingURL=77666.dtale_bundle.js.map
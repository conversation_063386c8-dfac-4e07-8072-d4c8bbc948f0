{"version": 3, "file": "93919.dtale_bundle.js", "mappings": "8FAOA,SAASA,EAAWC,EAAQC,GAE1B,QAAmBC,IAAfF,EAAOG,KAA+B,IAAVF,EAC9B,OAAOD,EAAOG,IAGhB,IAAIC,EAAQH,EAAQ,GAChBI,EAASJ,EAAQ,IAErB,OAAc,IAAVG,GAA0B,KAAXC,EACVL,EAAOM,mBAAmBC,QAAQ,YAAaC,OAAOP,IACpDG,GAAS,GAAKA,GAAS,IAAMC,EAAS,IAAMA,EAAS,IACvDL,EAAOS,iBAAiBF,QAAQ,YAAaC,OAAOP,IAEpDD,EAAOU,eAAeH,QAAQ,YAAaC,OAAOP,GAE7D,CAEA,SAASU,EAAqBX,GAC5B,OAAO,SAAUC,EAAOW,GACtB,OAAIA,SAA0CA,EAAQC,UAChDD,EAAQE,YAAcF,EAAQE,WAAa,EACzCd,EAAOe,OACFhB,EAAWC,EAAOe,OAAQd,GAE1B,SAAWF,EAAWC,EAAOgB,QAASf,GAG3CD,EAAOiB,KACFlB,EAAWC,EAAOiB,KAAMhB,GAExBF,EAAWC,EAAOgB,QAASf,GAAS,SAIxCF,EAAWC,EAAOgB,QAASf,EAEtC,CACF,CA3CAiB,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EA0ClB,IAAIE,EAAuB,CACzBC,iBAAkBZ,EAAqB,CACrCK,QAAS,CACPb,IAAK,iBACLG,mBAAoB,2BACpBG,iBAAkB,0BAClBC,eAAgB,2BAElBK,OAAQ,CACNZ,IAAK,4BACLG,mBAAoB,sCACpBG,iBAAkB,sCAClBC,eAAgB,wCAGpBc,SAAUb,EAAqB,CAC7BK,QAAS,CACPV,mBAAoB,oBACpBG,iBAAkB,oBAClBC,eAAgB,oBAElBO,KAAM,CACJX,mBAAoB,0BACpBG,iBAAkB,0BAClBC,eAAgB,0BAElBK,OAAQ,CACNT,mBAAoB,0BACpBG,iBAAkB,0BAClBC,eAAgB,4BAGpBe,YAAa,SAAqBC,EAAQd,GACxC,OAAIA,SAA0CA,EAAQC,UAChDD,EAAQE,YAAcF,EAAQE,WAAa,EACtC,kBAEA,kBAIJ,WACT,EACAa,iBAAkBhB,EAAqB,CACrCK,QAAS,CACPb,IAAK,gBACLG,mBAAoB,0BACpBG,iBAAkB,yBAClBC,eAAgB,0BAElBK,OAAQ,CACNZ,IAAK,2BACLG,mBAAoB,qCACpBG,iBAAkB,qCAClBC,eAAgB,uCAGpBkB,SAAUjB,EAAqB,CAC7BK,QAAS,CACPV,mBAAoB,mBACpBG,iBAAkB,mBAClBC,eAAgB,mBAElBO,KAAM,CACJX,mBAAoB,yBACpBG,iBAAkB,yBAClBC,eAAgB,yBAElBK,OAAQ,CACNT,mBAAoB,yBACpBG,iBAAkB,yBAClBC,eAAgB,2BAGpBmB,YAAalB,EAAqB,CAChCK,QAAS,CACPV,mBAAoB,uBACpBG,iBAAkB,wBAClBC,eAAgB,yBAElBK,OAAQ,CACNT,mBAAoB,qCACpBG,iBAAkB,sCAClBC,eAAgB,0CAGpBoB,OAAQnB,EAAqB,CAC3BK,QAAS,CACPV,mBAAoB,gBACpBG,iBAAkB,iBAClBC,eAAgB,qBAGpBqB,MAAOpB,EAAqB,CAC1BK,QAAS,CACPV,mBAAoB,iBACpBG,iBAAkB,gBAClBC,eAAgB,oBAGpBsB,YAAarB,EAAqB,CAChCK,QAAS,CACPV,mBAAoB,yBACpBG,iBAAkB,yBAClBC,eAAgB,0BAElBK,OAAQ,CACNT,mBAAoB,wCACpBG,iBAAkB,wCAClBC,eAAgB,2CAGpBuB,OAAQtB,EAAqB,CAC3BK,QAAS,CACPV,mBAAoB,mBACpBG,iBAAkB,mBAClBC,eAAgB,sBAGpBwB,aAAcvB,EAAqB,CACjCK,QAAS,CACPV,mBAAoB,yBACpBG,iBAAkB,0BAClBC,eAAgB,2BAElBK,OAAQ,CACNT,mBAAoB,uCACpBG,iBAAkB,wCAClBC,eAAgB,4CAGpByB,QAASxB,EAAqB,CAC5BK,QAAS,CACPV,mBAAoB,kBACpBG,iBAAkB,mBAClBC,eAAgB,uBAGpB0B,YAAazB,EAAqB,CAChCK,QAAS,CACPV,mBAAoB,uBACpBG,iBAAkB,sBAClBC,eAAgB,uBAElBK,OAAQ,CACNT,mBAAoB,qCACpBG,iBAAkB,sCAClBC,eAAgB,wCAGpB2B,OAAQ1B,EAAqB,CAC3BK,QAAS,CACPV,mBAAoB,gBACpBG,iBAAkB,iBAClBC,eAAgB,mBAGpB4B,WAAY3B,EAAqB,CAC/BK,QAAS,CACPV,mBAAoB,wBACpBG,iBAAkB,uBAClBC,eAAgB,wBAElBK,OAAQ,CACNT,mBAAoB,kCACpBG,iBAAkB,mCAClBC,eAAgB,qCAGpB6B,aAAc5B,EAAqB,CACjCK,QAAS,CACPV,mBAAoB,sBACpBG,iBAAkB,uBAClBC,eAAgB,uBAElBK,OAAQ,CACNT,mBAAoB,4BACpBG,iBAAkB,6BAClBC,eAAgB,gCASlB8B,EAJiB,SAAwBC,EAAOxC,EAAOW,GACzD,OAAOU,EAAqBmB,GAAOxC,EAAOW,EAC5C,EAGAQ,EAAA,QAAkBoB,EAClBE,EAAOtB,QAAUA,EAAQuB,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/ru/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nfunction declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one !== undefined && count === 1) {\n    return scheme.one;\n  }\n\n  var rem10 = count % 10;\n  var rem100 = count % 100; // 1, 21, 31, ...\n\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace('{{count}}', String(count)); // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace('{{count}}', String(count)); // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace('{{count}}', String(count));\n  }\n}\n\nfunction buildLocalizeTokenFn(scheme) {\n  return function (count, options) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        if (scheme.future) {\n          return declension(scheme.future, count);\n        } else {\n          return 'через ' + declension(scheme.regular, count);\n        }\n      } else {\n        if (scheme.past) {\n          return declension(scheme.past, count);\n        } else {\n          return declension(scheme.regular, count) + ' назад';\n        }\n      }\n    } else {\n      return declension(scheme.regular, count);\n    }\n  };\n}\n\nvar formatDistanceLocale = {\n  lessThanXSeconds: buildLocalizeTokenFn({\n    regular: {\n      one: 'меньше секунды',\n      singularNominative: 'меньше {{count}} секунды',\n      singularGenitive: 'меньше {{count}} секунд',\n      pluralGenitive: 'меньше {{count}} секунд'\n    },\n    future: {\n      one: 'меньше, чем через секунду',\n      singularNominative: 'меньше, чем через {{count}} секунду',\n      singularGenitive: 'меньше, чем через {{count}} секунды',\n      pluralGenitive: 'меньше, чем через {{count}} секунд'\n    }\n  }),\n  xSeconds: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} секунда',\n      singularGenitive: '{{count}} секунды',\n      pluralGenitive: '{{count}} секунд'\n    },\n    past: {\n      singularNominative: '{{count}} секунду назад',\n      singularGenitive: '{{count}} секунды назад',\n      pluralGenitive: '{{count}} секунд назад'\n    },\n    future: {\n      singularNominative: 'через {{count}} секунду',\n      singularGenitive: 'через {{count}} секунды',\n      pluralGenitive: 'через {{count}} секунд'\n    }\n  }),\n  halfAMinute: function halfAMinute(_count, options) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        return 'через полминуты';\n      } else {\n        return 'полминуты назад';\n      }\n    }\n\n    return 'полминуты';\n  },\n  lessThanXMinutes: buildLocalizeTokenFn({\n    regular: {\n      one: 'меньше минуты',\n      singularNominative: 'меньше {{count}} минуты',\n      singularGenitive: 'меньше {{count}} минут',\n      pluralGenitive: 'меньше {{count}} минут'\n    },\n    future: {\n      one: 'меньше, чем через минуту',\n      singularNominative: 'меньше, чем через {{count}} минуту',\n      singularGenitive: 'меньше, чем через {{count}} минуты',\n      pluralGenitive: 'меньше, чем через {{count}} минут'\n    }\n  }),\n  xMinutes: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} минута',\n      singularGenitive: '{{count}} минуты',\n      pluralGenitive: '{{count}} минут'\n    },\n    past: {\n      singularNominative: '{{count}} минуту назад',\n      singularGenitive: '{{count}} минуты назад',\n      pluralGenitive: '{{count}} минут назад'\n    },\n    future: {\n      singularNominative: 'через {{count}} минуту',\n      singularGenitive: 'через {{count}} минуты',\n      pluralGenitive: 'через {{count}} минут'\n    }\n  }),\n  aboutXHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} часа',\n      singularGenitive: 'около {{count}} часов',\n      pluralGenitive: 'около {{count}} часов'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} час',\n      singularGenitive: 'приблизительно через {{count}} часа',\n      pluralGenitive: 'приблизительно через {{count}} часов'\n    }\n  }),\n  xHours: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} час',\n      singularGenitive: '{{count}} часа',\n      pluralGenitive: '{{count}} часов'\n    }\n  }),\n  xDays: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} день',\n      singularGenitive: '{{count}} дня',\n      pluralGenitive: '{{count}} дней'\n    }\n  }),\n  aboutXWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} недели',\n      singularGenitive: 'около {{count}} недель',\n      pluralGenitive: 'около {{count}} недель'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} неделю',\n      singularGenitive: 'приблизительно через {{count}} недели',\n      pluralGenitive: 'приблизительно через {{count}} недель'\n    }\n  }),\n  xWeeks: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} неделя',\n      singularGenitive: '{{count}} недели',\n      pluralGenitive: '{{count}} недель'\n    }\n  }),\n  aboutXMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} месяца',\n      singularGenitive: 'около {{count}} месяцев',\n      pluralGenitive: 'около {{count}} месяцев'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} месяц',\n      singularGenitive: 'приблизительно через {{count}} месяца',\n      pluralGenitive: 'приблизительно через {{count}} месяцев'\n    }\n  }),\n  xMonths: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} месяц',\n      singularGenitive: '{{count}} месяца',\n      pluralGenitive: '{{count}} месяцев'\n    }\n  }),\n  aboutXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'около {{count}} года',\n      singularGenitive: 'около {{count}} лет',\n      pluralGenitive: 'около {{count}} лет'\n    },\n    future: {\n      singularNominative: 'приблизительно через {{count}} год',\n      singularGenitive: 'приблизительно через {{count}} года',\n      pluralGenitive: 'приблизительно через {{count}} лет'\n    }\n  }),\n  xYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: '{{count}} год',\n      singularGenitive: '{{count}} года',\n      pluralGenitive: '{{count}} лет'\n    }\n  }),\n  overXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'больше {{count}} года',\n      singularGenitive: 'больше {{count}} лет',\n      pluralGenitive: 'больше {{count}} лет'\n    },\n    future: {\n      singularNominative: 'больше, чем через {{count}} год',\n      singularGenitive: 'больше, чем через {{count}} года',\n      pluralGenitive: 'больше, чем через {{count}} лет'\n    }\n  }),\n  almostXYears: buildLocalizeTokenFn({\n    regular: {\n      singularNominative: 'почти {{count}} год',\n      singularGenitive: 'почти {{count}} года',\n      pluralGenitive: 'почти {{count}} лет'\n    },\n    future: {\n      singularNominative: 'почти через {{count}} год',\n      singularGenitive: 'почти через {{count}} года',\n      pluralGenitive: 'почти через {{count}} лет'\n    }\n  })\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  return formatDistanceLocale[token](count, options);\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["declension", "scheme", "count", "undefined", "one", "rem10", "rem100", "singularNominative", "replace", "String", "singularGenitive", "pluralGenitive", "buildLocalizeTokenFn", "options", "addSuffix", "comparison", "future", "regular", "past", "Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "xSeconds", "halfAMinute", "_count", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_default", "token", "module", "default"], "sourceRoot": ""}
{"version": 3, "file": "95051.dtale_bundle.js", "mappings": "6EAAA,MAAMA,EAAW,CACf,KACA,KACA,KACA,KACA,MACA,QACA,UACA,MACA,MACA,WACA,KACA,SACA,OACA,OACA,QACA,QACA,aACA,OACA,QACA,OACA,UACA,MACA,SACA,WACA,SACA,SACA,MACA,QACA,QACA,QAIA,WACA,QACA,QACA,SACA,SACA,OACA,SACA,WAEIC,EAAW,CACf,OACA,QACA,OACA,YACA,MACA,YAoFIC,EAAY,GAAGC,OAlCI,CACvB,cACA,aACA,gBACA,eAEA,UACA,UAEA,OACA,WACA,QACA,aACA,WACA,YACA,qBACA,YACA,qBACA,SACA,YAGyB,CACzB,YACA,OACA,QACA,UACA,SACA,WACA,eACA,SACA,UA9EY,CACZ,OACA,WACA,SACA,OACA,OACA,SACA,SACA,SACA,WACA,UACA,QACA,SACA,MACA,MACA,UACA,UACA,QACA,UACA,OACA,UACA,eACA,aACA,aACA,YACA,cACA,cACA,eACA,QACA,aACA,oBACA,cACA,gBACA,iBACA,UAGkB,CAClB,YACA,gBACA,aACA,iBACA,cACA,YACA,aAmQFC,EAAOC,QA7MP,SAAsBC,GACpB,MAgCMC,EAAa,CACjBC,QAASR,EAASG,OAvBI,CACtB,OACA,SACA,QACA,OACA,KACA,OACA,MACA,KACA,KACA,OACA,QAY0CM,QAHzBC,EAPQ,CACzB,MACA,QACA,MACA,WACA,UAGCC,IAAQD,EAAKE,SAASD,KAGvBE,QAASZ,EAASE,OA9BI,CACtB,MACA,KACA,KACA,QA2BAW,SAAUZ,EAAUC,OAnCG,CACvB,MACA,WA4BgB,IAACO,EAOnB,MAAMK,EAAc,2BACdC,EAAQ,CACZC,UAAW,QACXC,MAAO,MACPC,IAAK,KACLC,SAAUb,GAENc,EAAc,CAClBf,EAAKgB,mBACLhB,EAAKiB,QAAQjB,EAAKkB,cAAe,CAC/BC,OAAQ,CACNN,IAAK,WACLO,UAAW,KAGf,CACET,UAAW,SACXU,SAAU,CACR,CACET,MAAO,MACPC,IAAK,MACLS,SAAU,CAACtB,EAAKuB,mBAElB,CACEX,MAAO,IACPC,IAAK,IACLS,SAAU,CAACtB,EAAKuB,mBAElB,CACEX,MAAO,MACPC,IAAK,MACLS,SAAU,CACRtB,EAAKuB,iBACLb,IAGJ,CACEE,MAAO,IACPC,IAAK,IACLS,SAAU,CACRtB,EAAKuB,iBACLb,MAKR,CACEC,UAAW,SACXU,SAAU,CACR,CACET,MAAO,MACPC,IAAK,MACLS,SAAU,CACRZ,EACAV,EAAKwB,oBAGT,CACEZ,MAAO,sBACPQ,UAAW,GAEb,CAGER,MAAO,8CAIb,CACEA,MAAO,IAAMH,GAEf,CACEgB,YAAa,aACbC,cAAc,EACdC,YAAY,EACZN,SAAU,CACR,CACET,MAAO,MACPC,IAAK,OAEP,CACED,MAAO,IACPC,IAAK,QAKbH,EAAMY,SAAWP,EAEjB,MAAMa,EAAQ5B,EAAKiB,QAAQjB,EAAK6B,WAAY,CAC1CjB,MAAOH,IAEHqB,EAAqB,0BACrBC,EAAS,CACbpB,UAAW,SACXC,MAAO,YACPoB,aAAa,EAGbV,SAAU,CAAC,CACTV,MAAO,KACPC,IAAK,KACLC,SAAUb,EACVqB,SAAU,CAAC,QAAQzB,OAAOkB,MAI9B,MAAO,CACLkB,KAAM,eACNC,QAAS,CACP,SACA,OACA,QAEFpB,SAAUb,EACVkC,QAAS,OACTb,SAAUP,EAAYlB,OAAO,CAC3BG,EAAKoC,QAAQ,MAAO,OACpBpC,EAAKwB,kBACL,CACEb,UAAW,WACXC,MAAO,QAAUH,EAAc,YAAcqB,EAC7CjB,IAAK,QACLmB,aAAa,EACbV,SAAU,CACRM,EACAG,IAGJ,CAEEnB,MAAO,aACPQ,UAAW,EACXE,SAAU,CAAC,CACTX,UAAW,WACXC,MAAOkB,EACPjB,IAAK,QACLmB,aAAa,EACbV,SAAU,CAACS,MAGf,CACEpB,UAAW,QACX0B,cAAe,QACfxB,IAAK,IACLsB,QAAS,YACTb,SAAU,CACR,CACEe,cAAe,UACfC,gBAAgB,EAChBH,QAAS,YACTb,SAAU,CAACM,IAEbA,IAGJ,CACEhB,MAAOH,EAAc,IACrBI,IAAK,IACLmB,aAAa,EACbO,WAAW,EACXnB,UAAW,KAInB,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/coffeescript.js"], "sourcesContent": ["const KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // JS handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\nconst TYPES = [\n  \"Intl\",\n  \"DataView\",\n  \"Number\",\n  \"Math\",\n  \"Date\",\n  \"String\",\n  \"RegExp\",\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Error\",\n  \"Symbol\",\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  \"Proxy\",\n  \"Reflect\",\n  \"JSON\",\n  \"Promise\",\n  \"Float64Array\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Int8Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"Float32Array\",\n  \"Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"ArrayBuffer\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  \"BigInt\"\n];\n\nconst ERROR_TYPES = [\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  BUILT_IN_VARIABLES,\n  TYPES,\n  ERROR_TYPES\n);\n\n/*\nLanguage: CoffeeScript\nAuthor: Dmytrii Nagirniak <<EMAIL>>\nContributors: Oleg Efimov <<EMAIL>>, Cédric Néhémie <<EMAIL>>\nDescription: CoffeeScript is a programming language that transcompiles to JavaScript. For info about language see http://coffeescript.org/\nCategory: common, scripting\nWebsite: https://coffeescript.org\n*/\n\n/** @type LanguageFn */\nfunction coffeescript(hljs) {\n  const COFFEE_BUILT_INS = [\n    'npm',\n    'print'\n  ];\n  const COFFEE_LITERALS = [\n    'yes',\n    'no',\n    'on',\n    'off'\n  ];\n  const COFFEE_KEYWORDS = [\n    'then',\n    'unless',\n    'until',\n    'loop',\n    'by',\n    'when',\n    'and',\n    'or',\n    'is',\n    'isnt',\n    'not'\n  ];\n  const NOT_VALID_KEYWORDS = [\n    \"var\",\n    \"const\",\n    \"let\",\n    \"function\",\n    \"static\"\n  ];\n  const excluding = (list) =>\n    (kw) => !list.includes(kw);\n  const KEYWORDS$1 = {\n    keyword: KEYWORDS.concat(COFFEE_KEYWORDS).filter(excluding(NOT_VALID_KEYWORDS)),\n    literal: LITERALS.concat(COFFEE_LITERALS),\n    built_in: BUILT_INS.concat(COFFEE_BUILT_INS)\n  };\n  const JS_IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS$1\n  };\n  const EXPRESSIONS = [\n    hljs.BINARY_NUMBER_MODE,\n    hljs.inherit(hljs.C_NUMBER_MODE, {\n      starts: {\n        end: '(\\\\s*/)?',\n        relevance: 0\n      }\n    }), // a number tries to eat the following slash to prevent treating it as a regexp\n    {\n      className: 'string',\n      variants: [\n        {\n          begin: /'''/,\n          end: /'''/,\n          contains: [hljs.BACKSLASH_ESCAPE]\n        },\n        {\n          begin: /'/,\n          end: /'/,\n          contains: [hljs.BACKSLASH_ESCAPE]\n        },\n        {\n          begin: /\"\"\"/,\n          end: /\"\"\"/,\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ]\n        },\n        {\n          begin: /\"/,\n          end: /\"/,\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ]\n        }\n      ]\n    },\n    {\n      className: 'regexp',\n      variants: [\n        {\n          begin: '///',\n          end: '///',\n          contains: [\n            SUBST,\n            hljs.HASH_COMMENT_MODE\n          ]\n        },\n        {\n          begin: '//[gim]{0,3}(?=\\\\W)',\n          relevance: 0\n        },\n        {\n          // regex can't start with space to parse x / 2 / 3 as two divisions\n          // regex can't start with *, and it supports an \"illegal\" in the main mode\n          begin: /\\/(?![ *]).*?(?![\\\\]).\\/[gim]{0,3}(?=\\W)/\n        }\n      ]\n    },\n    {\n      begin: '@' + JS_IDENT_RE // relevance booster\n    },\n    {\n      subLanguage: 'javascript',\n      excludeBegin: true,\n      excludeEnd: true,\n      variants: [\n        {\n          begin: '```',\n          end: '```'\n        },\n        {\n          begin: '`',\n          end: '`'\n        }\n      ]\n    }\n  ];\n  SUBST.contains = EXPRESSIONS;\n\n  const TITLE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: JS_IDENT_RE\n  });\n  const POSSIBLE_PARAMS_RE = '(\\\\(.*\\\\)\\\\s*)?\\\\B[-=]>';\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\([^\\\\(]',\n    returnBegin: true,\n    /* We need another contained nameless mode to not have every nested\n    pair of parens to be called \"params\" */\n    contains: [{\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS$1,\n      contains: ['self'].concat(EXPRESSIONS)\n    }]\n  };\n\n  return {\n    name: 'CoffeeScript',\n    aliases: [\n      'coffee',\n      'cson',\n      'iced'\n    ],\n    keywords: KEYWORDS$1,\n    illegal: /\\/\\*/,\n    contains: EXPRESSIONS.concat([\n      hljs.COMMENT('###', '###'),\n      hljs.HASH_COMMENT_MODE,\n      {\n        className: 'function',\n        begin: '^\\\\s*' + JS_IDENT_RE + '\\\\s*=\\\\s*' + POSSIBLE_PARAMS_RE,\n        end: '[-=]>',\n        returnBegin: true,\n        contains: [\n          TITLE,\n          PARAMS\n        ]\n      },\n      {\n        // anonymous function start\n        begin: /[:\\(,=]\\s*/,\n        relevance: 0,\n        contains: [{\n          className: 'function',\n          begin: POSSIBLE_PARAMS_RE,\n          end: '[-=]>',\n          returnBegin: true,\n          contains: [PARAMS]\n        }]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class',\n        end: '$',\n        illegal: /[:=\"\\[\\]]/,\n        contains: [\n          {\n            beginKeywords: 'extends',\n            endsWithParent: true,\n            illegal: /[:=\"\\[\\]]/,\n            contains: [TITLE]\n          },\n          TITLE\n        ]\n      },\n      {\n        begin: JS_IDENT_RE + ':',\n        end: ':',\n        returnBegin: true,\n        returnEnd: true,\n        relevance: 0\n      }\n    ])\n  };\n}\n\nmodule.exports = coffeescript;\n"], "names": ["KEYWORDS", "LITERALS", "BUILT_INS", "concat", "module", "exports", "hljs", "KEYWORDS$1", "keyword", "filter", "list", "kw", "includes", "literal", "built_in", "JS_IDENT_RE", "SUBST", "className", "begin", "end", "keywords", "EXPRESSIONS", "BINARY_NUMBER_MODE", "inherit", "C_NUMBER_MODE", "starts", "relevance", "variants", "contains", "BACKSLASH_ESCAPE", "HASH_COMMENT_MODE", "subLanguage", "excludeBegin", "excludeEnd", "TITLE", "TITLE_MODE", "POSSIBLE_PARAMS_RE", "PARAMS", "returnBegin", "name", "aliases", "illegal", "COMMENT", "beginKeywords", "endsWithParent", "returnEnd"], "sourceRoot": ""}
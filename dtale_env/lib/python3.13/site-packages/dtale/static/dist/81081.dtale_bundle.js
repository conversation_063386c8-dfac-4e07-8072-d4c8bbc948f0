"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[81081],{44724:(e,o)=>{Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var n={lessThanXSeconds:{one:"menos dun segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"medio minuto",lessThanXMinutes:{one:"menos dun minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"arredor dunha hora",other:"arredor de {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 día",other:"{{count}} días"},aboutXWeeks:{one:"arredor dunha semana",other:"arredor de {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"arredor de 1 mes",other:"arredor de {{count}} meses"},xMonths:{one:"1 mes",other:"{{count}} meses"},aboutXYears:{one:"arredor dun ano",other:"arredor de {{count}} anos"},xYears:{one:"1 ano",other:"{{count}} anos"},overXYears:{one:"máis dun ano",other:"máis de {{count}} anos"},almostXYears:{one:"case un ano",other:"case {{count}} anos"}},s=function(e,o,s){var r,a=n[e];return r="string"==typeof a?a:1===o?a.one:a.other.replace("{{count}}",String(o)),null!=s&&s.addSuffix?s.comparison&&s.comparison>0?"en "+r:"hai "+r:r};o.default=s,e.exports=o.default}}]);
//# sourceMappingURL=81081.dtale_bundle.js.map
{"version": 3, "file": "88030.dtale_bundle.js", "mappings": "6EASA,SAASA,EAAOC,GACd,OAAKA,EACa,iBAAPA,EAAwBA,EAE5BA,EAAGD,OAHM,IAIlB,CAsBA,SAASE,KAAUC,GAEjB,OADeA,EAAKC,IAAKC,GAAML,EAAOK,IAAIC,KAAK,GAEjD,CA4RAC,EAAOC,QArQP,SAAoBC,GAClB,MAAMC,EAAY,CAChB,eAAgB,CACd,SACA,WACA,aACA,YACA,SACA,WACA,OACA,UACA,MACA,OACA,KACA,KACA,QACA,UACA,MACA,MACA,SACA,MACA,SACA,UACA,eACA,SACA,WACA,WACA,UACA,SACA,OACA,OACA,UAmBEC,EAA0B,kBAC1BC,EAAiB,wCAEjBC,EAnER,YAAmBV,GAEjB,MADe,IAAMA,EAAKC,IAAKC,GAAML,EAAOK,IAAIC,KAAK,KAAO,GAE9D,CAgEiBQ,CALgB,aACA,aAO7BH,EACAC,GAGIG,EAAmBb,EA7FlBA,EAAO,IA8FH,aA9FY,MA+FrBW,EAxGJ,SAA0BZ,GACxB,OAAOC,EAAO,IAAKD,EAAI,KACzB,CAuGIe,CAAiBd,EAXU,UAazBW,KAKEI,EAAmBf,EACvB,IACAS,EAAyB,IACzBC,EACA,UAGIM,EAAiC,CACrCC,MAAOJ,EACPK,QAAS,YAGLC,EAAmBZ,EAAKa,QAAQJ,EAAgC,CACpEK,SAhDe,CACfC,QAAS,CACP,OACA,QACA,YACA,WA8CEC,EAAiB,CACrBN,MAAO,KACPO,IAAK,MAIDC,EAAO,CAEXC,UAAW,OACXT,MAAOF,EACPY,UAAW,EACXC,OAAQ,CACNX,MAAO,IACPO,IAAK,IACLI,OAAQ,CACNC,SAAU,CACRtB,EAAKuB,YACLvB,EAAKwB,kBACLxB,EAAKyB,iBACLb,EACAI,MAqBFU,EAAoB,CACxBJ,SAAU,CACRtB,EAAKuB,YACLvB,EAAKwB,kBACLxB,EAAKyB,iBAnBY,CAEnBf,MAAO,UACPI,SAAU,CACRa,QAAS,MAEXV,IAAK,KACLK,SAAU,CACR,CAEEZ,MAAO,SAWTQ,EACAN,EACAI,GAEFY,WAAW,GAMPC,EAA0B7B,EAAKa,QAAQJ,EAAgC,CAC3EU,UAAW,OACXL,SAAUb,EACVoB,OAAQrB,EAAKa,QAAQa,EAAmB,CACtCT,IAAK,SAITD,EAAeM,SAAW,CAACO,GAE3B,MAAMC,EAAkC9B,EAAKa,QAAQJ,EAAgC,CACnFK,SAAUb,EACVkB,UAAW,OACXE,OAAQrB,EAAKa,QAAQa,EAAmB,CACtCT,IAAK,WAIHc,EAAkC/B,EAAKa,QAAQJ,EAAgC,CACnFK,SAAUb,EACVkB,UAAW,SAGPa,EAA0BhC,EAAKa,QAAQJ,EAAgC,CAC3EU,UAAW,OACXL,SAAUb,EACVoB,OAAQrB,EAAKa,QAAQa,EAAmB,CACtCT,IAAK,WAaT,MAAO,CACLgB,KAAM,aACNC,QAAS,CACP,MACA,WACA,kBACA,YAEFC,kBAAkB,EAClBC,YAAa,MACbd,SAAU,CAnBsC,CAChDZ,MAAO,SACP2B,MAAM,GAEiD,CACvD3B,MAAO,eACP2B,MAAM,GAgBJrC,EAAKsC,QAAQ,UAAW,UACxBtC,EAAKsC,QAAQ,QAAS,QACtB,CAEEnB,UAAW,eACXT,MAAO,iBACPO,IAAK,WACLK,SAAU,CAACQ,GACXT,OAAQ,CACNJ,IAAK,aACLW,WAAW,EACXQ,YAAa,QAGjB,CAEEjB,UAAW,eACXT,MAAO,aACPO,IAAK,WACLK,SAAU,CAACS,IAEb,CAEEZ,UAAW,eACXT,MAAO,QACPO,IAAK,OACLK,SAAU,CAACQ,IAEb,CACEX,UAAW,eACXT,MAAO,mBACPO,IAAK,OACLH,SAAU,QAEZ,CACEK,UAAW,eACXT,MAAO,kBACPO,IAAK,OACLH,SAAU,WAEZ,CAEEK,UAAW,eACXT,MAAO,SACPO,IAAK,OACLK,SAAU,CAACS,IAEb,CAEEZ,UAAW,oBACXT,MAAO,SACPO,IAAK,SACLK,SAAU,CAACU,IAEb,CAEEb,UAAW,oBACXT,MAAO,OACPO,IAAK,OACLK,SAAU,CAACU,KAInB,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/handlebars.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(', re, ')*');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: Handlebars\nRequires: xml.js\nAuthor: <PERSON> <PERSON> <<EMAIL>>\nDescription: Matcher for Handlebars as well as EmberJS additions.\nWebsite: https://handlebarsjs.com\nCategory: template\n*/\n\nfunction handlebars(hljs) {\n  const BUILT_INS = {\n    'builtin-name': [\n      'action',\n      'bindattr',\n      'collection',\n      'component',\n      'concat',\n      'debugger',\n      'each',\n      'each-in',\n      'get',\n      'hash',\n      'if',\n      'in',\n      'input',\n      'link-to',\n      'loc',\n      'log',\n      'lookup',\n      'mut',\n      'outlet',\n      'partial',\n      'query-params',\n      'render',\n      'template',\n      'textarea',\n      'unbound',\n      'unless',\n      'view',\n      'with',\n      'yield'\n    ]\n  };\n\n  const LITERALS = {\n    literal: [\n      'true',\n      'false',\n      'undefined',\n      'null'\n    ]\n  };\n\n  // as defined in https://handlebarsjs.com/guide/expressions.html#literal-segments\n  // this regex matches literal segments like ' abc ' or [ abc ] as well as helpers and paths\n  // like a/b, ./abc/cde, and abc.bcd\n\n  const DOUBLE_QUOTED_ID_REGEX = /\"\"|\"[^\"]+\"/;\n  const SINGLE_QUOTED_ID_REGEX = /''|'[^']+'/;\n  const BRACKET_QUOTED_ID_REGEX = /\\[\\]|\\[[^\\]]+\\]/;\n  const PLAIN_ID_REGEX = /[^\\s!\"#%&'()*+,.\\/;<=>@\\[\\\\\\]^`{|}~]+/;\n  const PATH_DELIMITER_REGEX = /(\\.|\\/)/;\n  const ANY_ID = either(\n    DOUBLE_QUOTED_ID_REGEX,\n    SINGLE_QUOTED_ID_REGEX,\n    BRACKET_QUOTED_ID_REGEX,\n    PLAIN_ID_REGEX\n    );\n\n  const IDENTIFIER_REGEX = concat(\n    optional(/\\.|\\.\\/|\\//), // relative or absolute path\n    ANY_ID,\n    anyNumberOfTimes(concat(\n      PATH_DELIMITER_REGEX,\n      ANY_ID\n    ))\n  );\n\n  // identifier followed by a equal-sign (without the equal sign)\n  const HASH_PARAM_REGEX = concat(\n    '(',\n    BRACKET_QUOTED_ID_REGEX, '|',\n    PLAIN_ID_REGEX,\n    ')(?==)'\n  );\n\n  const HELPER_NAME_OR_PATH_EXPRESSION = {\n    begin: IDENTIFIER_REGEX,\n    lexemes: /[\\w.\\/]+/\n  };\n\n  const HELPER_PARAMETER = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    keywords: LITERALS\n  });\n\n  const SUB_EXPRESSION = {\n    begin: /\\(/,\n    end: /\\)/\n    // the \"contains\" is added below when all necessary sub-modes are defined\n  };\n\n  const HASH = {\n    // fka \"attribute-assignment\", parameters of the form 'key=value'\n    className: 'attr',\n    begin: HASH_PARAM_REGEX,\n    relevance: 0,\n    starts: {\n      begin: /=/,\n      end: /=/,\n      starts: {\n        contains: [\n          hljs.NUMBER_MODE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          HELPER_PARAMETER,\n          SUB_EXPRESSION\n        ]\n      }\n    }\n  };\n\n  const BLOCK_PARAMS = {\n    // parameters of the form '{{#with x as | y |}}...{{/with}}'\n    begin: /as\\s+\\|/,\n    keywords: {\n      keyword: 'as'\n    },\n    end: /\\|/,\n    contains: [\n      {\n        // define sub-mode in order to prevent highlighting of block-parameter named \"as\"\n        begin: /\\w+/\n      }\n    ]\n  };\n\n  const HELPER_PARAMETERS = {\n    contains: [\n      hljs.NUMBER_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      BLOCK_PARAMS,\n      HASH,\n      HELPER_PARAMETER,\n      SUB_EXPRESSION\n    ],\n    returnEnd: true\n    // the property \"end\" is defined through inheritance when the mode is used. If depends\n    // on the surrounding mode, but \"endsWithParent\" does not work here (i.e. it includes the\n    // end-token of the surrounding mode)\n  };\n\n  const SUB_EXPRESSION_CONTENTS = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    className: 'name',\n    keywords: BUILT_INS,\n    starts: hljs.inherit(HELPER_PARAMETERS, {\n      end: /\\)/\n    })\n  });\n\n  SUB_EXPRESSION.contains = [SUB_EXPRESSION_CONTENTS];\n\n  const OPENING_BLOCK_MUSTACHE_CONTENTS = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    keywords: BUILT_INS,\n    className: 'name',\n    starts: hljs.inherit(HELPER_PARAMETERS, {\n      end: /\\}\\}/\n    })\n  });\n\n  const CLOSING_BLOCK_MUSTACHE_CONTENTS = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    keywords: BUILT_INS,\n    className: 'name'\n  });\n\n  const BASIC_MUSTACHE_CONTENTS = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    className: 'name',\n    keywords: BUILT_INS,\n    starts: hljs.inherit(HELPER_PARAMETERS, {\n      end: /\\}\\}/\n    })\n  });\n\n  const ESCAPE_MUSTACHE_WITH_PRECEEDING_BACKSLASH = {\n    begin: /\\\\\\{\\{/,\n    skip: true\n  };\n  const PREVENT_ESCAPE_WITH_ANOTHER_PRECEEDING_BACKSLASH = {\n    begin: /\\\\\\\\(?=\\{\\{)/,\n    skip: true\n  };\n\n  return {\n    name: 'Handlebars',\n    aliases: [\n      'hbs',\n      'html.hbs',\n      'html.handlebars',\n      'htmlbars'\n    ],\n    case_insensitive: true,\n    subLanguage: 'xml',\n    contains: [\n      ESCAPE_MUSTACHE_WITH_PRECEEDING_BACKSLASH,\n      PREVENT_ESCAPE_WITH_ANOTHER_PRECEEDING_BACKSLASH,\n      hljs.COMMENT(/\\{\\{!--/, /--\\}\\}/),\n      hljs.COMMENT(/\\{\\{!/, /\\}\\}/),\n      {\n        // open raw block \"{{{{raw}}}} content not evaluated {{{{/raw}}}}\"\n        className: 'template-tag',\n        begin: /\\{\\{\\{\\{(?!\\/)/,\n        end: /\\}\\}\\}\\}/,\n        contains: [OPENING_BLOCK_MUSTACHE_CONTENTS],\n        starts: {\n          end: /\\{\\{\\{\\{\\//,\n          returnEnd: true,\n          subLanguage: 'xml'\n        }\n      },\n      {\n        // close raw block\n        className: 'template-tag',\n        begin: /\\{\\{\\{\\{\\//,\n        end: /\\}\\}\\}\\}/,\n        contains: [CLOSING_BLOCK_MUSTACHE_CONTENTS]\n      },\n      {\n        // open block statement\n        className: 'template-tag',\n        begin: /\\{\\{#/,\n        end: /\\}\\}/,\n        contains: [OPENING_BLOCK_MUSTACHE_CONTENTS]\n      },\n      {\n        className: 'template-tag',\n        begin: /\\{\\{(?=else\\}\\})/,\n        end: /\\}\\}/,\n        keywords: 'else'\n      },\n      {\n        className: 'template-tag',\n        begin: /\\{\\{(?=else if)/,\n        end: /\\}\\}/,\n        keywords: 'else if'\n      },\n      {\n        // closing block statement\n        className: 'template-tag',\n        begin: /\\{\\{\\//,\n        end: /\\}\\}/,\n        contains: [CLOSING_BLOCK_MUSTACHE_CONTENTS]\n      },\n      {\n        // template variable or helper-call that is NOT html-escaped\n        className: 'template-variable',\n        begin: /\\{\\{\\{/,\n        end: /\\}\\}\\}/,\n        contains: [BASIC_MUSTACHE_CONTENTS]\n      },\n      {\n        // template variable or helper-call that is html-escaped\n        className: 'template-variable',\n        begin: /\\{\\{/,\n        end: /\\}\\}/,\n        contains: [BASIC_MUSTACHE_CONTENTS]\n      }\n    ]\n  };\n}\n\nmodule.exports = handlebars;\n"], "names": ["source", "re", "concat", "args", "map", "x", "join", "module", "exports", "hljs", "BUILT_INS", "BRACKET_QUOTED_ID_REGEX", "PLAIN_ID_REGEX", "ANY_ID", "either", "IDENTIFIER_REGEX", "anyNumberOfTimes", "HASH_PARAM_REGEX", "HELPER_NAME_OR_PATH_EXPRESSION", "begin", "lexemes", "HELPER_PARAMETER", "inherit", "keywords", "literal", "SUB_EXPRESSION", "end", "HASH", "className", "relevance", "starts", "contains", "NUMBER_MODE", "QUOTE_STRING_MODE", "APOS_STRING_MODE", "HELPER_PARAMETERS", "keyword", "returnEnd", "SUB_EXPRESSION_CONTENTS", "OPENING_BLOCK_MUSTACHE_CONTENTS", "CLOSING_BLOCK_MUSTACHE_CONTENTS", "BASIC_MUSTACHE_CONTENTS", "name", "aliases", "case_insensitive", "subLanguage", "skip", "COMMENT"], "sourceRoot": ""}
{"version": 3, "file": "79181.dtale_bundle.js", "mappings": "6FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,eACLC,IAAK,iBACLC,WAAY,yBACZC,MAAO,0BAETC,SAAU,CACRJ,IAAK,cACLC,IAAK,UACLC,WAAY,kBACZC,MAAO,mBAETE,YAAa,YACbC,iBAAkB,CAChBN,IAAK,eACLC,IAAK,iBACLC,WAAY,yBACZC,MAAO,0BAETI,SAAU,CACRP,IAAK,cACLC,IAAK,UACLC,WAAY,kBACZC,MAAO,mBAETK,YAAa,CACXR,IAAK,qBACLC,IAAK,gBACLC,WAAY,0BACZC,MAAO,0BAETM,OAAQ,CACNT,IAAK,aACLC,IAAK,SACLC,WAAY,kBACZC,MAAO,kBAETO,MAAO,CACLV,IAAK,WACLC,IAAK,QACLC,WAAY,iBACZC,MAAO,iBAETQ,YAAa,CACXX,IAAK,oBACLC,IAAK,iBACLC,WAAY,0BACZC,MAAO,2BAETS,OAAQ,CACNZ,IAAK,aACLC,IAAK,UACLC,WAAY,mBACZC,MAAO,oBAETU,aAAc,CACZb,IAAK,mBACLC,IAAK,eACLC,WAAY,wBACZC,MAAO,0BAETW,QAAS,CACPd,IAAK,WACLC,IAAK,QACLC,WAAY,iBACZC,MAAO,kBAETY,YAAa,CACXf,IAAK,oBACLC,IAAK,eACLC,WAAY,0BACZC,MAAO,yBAETa,OAAQ,CACNhB,IAAK,WACLC,IAAK,QACLC,WAAY,kBACZC,MAAO,iBAETc,WAAY,CACVjB,IAAK,cACLC,IAAK,gBACLC,WAAY,0BACZC,MAAO,yBAETe,aAAc,CACZlB,IAAK,qBACLC,IAAK,iBACLC,WAAY,2BACZC,MAAO,2BA+BPgB,EA3BiB,SAAwBC,EAAOC,EAAOC,GACzD,IACIC,EADAC,EAAa1B,EAAqBsB,GAetC,OAXEG,EADwB,iBAAfC,EACAA,EACU,IAAVH,EACAG,EAAWxB,IACD,IAAVqB,EACAG,EAAWvB,IACXoB,GAAS,GACTG,EAAWtB,WAAWuB,QAAQ,YAAaC,OAAOL,IAElDG,EAAWrB,MAAMsB,QAAQ,YAAaC,OAAOL,IAGpDC,SAA0CA,EAAQK,UAChDL,EAAQM,YAAcN,EAAQM,WAAa,EACtC,QAAUL,EAEV,OAASA,EAIbA,CACT,EAGA3B,EAAA,QAAkBuB,EAClBU,EAAOjC,QAAUA,EAAQkC,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/ar/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'أقل من ثانية',\n    two: 'أقل من ثانيتين',\n    threeToTen: 'أقل من {{count}} ثواني',\n    other: 'أقل من {{count}} ثانية'\n  },\n  xSeconds: {\n    one: 'ثانية واحدة',\n    two: 'ثانيتان',\n    threeToTen: '{{count}} ثواني',\n    other: '{{count}} ثانية'\n  },\n  halfAMinute: 'نصف دقيقة',\n  lessThanXMinutes: {\n    one: 'أقل من دقيقة',\n    two: 'أقل من دقيقتين',\n    threeToTen: 'أقل من {{count}} دقائق',\n    other: 'أقل من {{count}} دقيقة'\n  },\n  xMinutes: {\n    one: 'دقيقة واحدة',\n    two: 'دقيقتان',\n    threeToTen: '{{count}} دقائق',\n    other: '{{count}} دقيقة'\n  },\n  aboutXHours: {\n    one: 'ساعة واحدة تقريباً',\n    two: 'ساعتين تقريبا',\n    threeToTen: '{{count}} ساعات تقريباً',\n    other: '{{count}} ساعة تقريباً'\n  },\n  xHours: {\n    one: 'ساعة واحدة',\n    two: 'ساعتان',\n    threeToTen: '{{count}} ساعات',\n    other: '{{count}} ساعة'\n  },\n  xDays: {\n    one: 'يوم واحد',\n    two: 'يومان',\n    threeToTen: '{{count}} أيام',\n    other: '{{count}} يوم'\n  },\n  aboutXWeeks: {\n    one: 'أسبوع واحد تقريبا',\n    two: 'أسبوعين تقريبا',\n    threeToTen: '{{count}} أسابيع تقريبا',\n    other: '{{count}} أسبوعا تقريبا'\n  },\n  xWeeks: {\n    one: 'أسبوع واحد',\n    two: 'أسبوعان',\n    threeToTen: '{{count}} أسابيع',\n    other: '{{count}} أسبوعا'\n  },\n  aboutXMonths: {\n    one: 'شهر واحد تقريباً',\n    two: 'شهرين تقريبا',\n    threeToTen: '{{count}} أشهر تقريبا',\n    other: '{{count}} شهرا تقريباً'\n  },\n  xMonths: {\n    one: 'شهر واحد',\n    two: 'شهران',\n    threeToTen: '{{count}} أشهر',\n    other: '{{count}} شهرا'\n  },\n  aboutXYears: {\n    one: 'سنة واحدة تقريباً',\n    two: 'سنتين تقريبا',\n    threeToTen: '{{count}} سنوات تقريباً',\n    other: '{{count}} سنة تقريباً'\n  },\n  xYears: {\n    one: 'سنة واحد',\n    two: 'سنتان',\n    threeToTen: '{{count}} سنوات',\n    other: '{{count}} سنة'\n  },\n  overXYears: {\n    one: 'أكثر من سنة',\n    two: 'أكثر من سنتين',\n    threeToTen: 'أكثر من {{count}} سنوات',\n    other: 'أكثر من {{count}} سنة'\n  },\n  almostXYears: {\n    one: 'ما يقارب سنة واحدة',\n    two: 'ما يقارب سنتين',\n    threeToTen: 'ما يقارب {{count}} سنوات',\n    other: 'ما يقارب {{count}} سنة'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var usageGroup = formatDistanceLocale[token];\n  var result;\n\n  if (typeof usageGroup === 'string') {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else if (count === 2) {\n    result = usageGroup.two;\n  } else if (count <= 10) {\n    result = usageGroup.threeToTen.replace('{{count}}', String(count));\n  } else {\n    result = usageGroup.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'خلال ' + result;\n    } else {\n      return 'منذ ' + result;\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "formatDistanceLocale", "lessThanXSeconds", "one", "two", "threeToTen", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "_default", "token", "count", "options", "result", "usageGroup", "replace", "String", "addSuffix", "comparison", "module", "default"], "sourceRoot": ""}
{"version": 3, "file": "86986.dtale_bundle.js", "mappings": "2HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIE,EAASC,EAAuB,EAAQ,QAExCC,EAAUD,EAAuB,EAAQ,QAEzCE,EAAUF,EAAuB,EAAQ,QAEzCG,EAAUH,EAAuB,EAAQ,QAEzCI,EAAUJ,EAAuB,EAAQ,QAE7C,SAASA,EAAuBK,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAe9F,IAcIG,EAdS,CACXC,KAAM,KACNC,eAAgBX,EAAOQ,QACvBI,WAAYV,EAAQM,QACpBK,eAAgBV,EAAQK,QACxBM,SAAUV,EAAQI,QAClBO,MAAOV,EAAQG,QACfQ,QAAS,CACPC,aAAc,EAGdC,sBAAuB,IAI3BpB,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O,kBC9CzBZ,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCQ,EAF5BN,GAE4BM,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAqFIG,EA1BW,CACbW,cANkB,SAAuBC,EAAaC,GAEtD,OADaC,OAAOF,GACJ,GAClB,EAIEG,KAAK,EAAIxB,EAAOQ,SAAS,CACvBiB,OA9DY,CACdC,OAAQ,CAAC,OAAQ,QACjBC,YAAa,CAAC,SAAU,UACxBC,KAAM,CAAC,gBAAiB,gBA4DtBC,aAAc,SAEhBC,SAAS,EAAI9B,EAAOQ,SAAS,CAC3BiB,OA7DgB,CAClBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,KAAM,KAAM,KAAM,MAChCC,KAAM,CAAC,cAAe,cAAe,cAAe,gBA2DlDC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAIhC,EAAOQ,SAAS,CACzBiB,OA/Dc,CAChBC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QACrGC,KAAM,CAAC,UAAW,WAAY,QAAS,QAAS,MAAO,OAAQ,OAAQ,WAAY,YAAa,UAAW,WAAY,aA6DrHC,aAAc,SAEhBI,KAAK,EAAIjC,EAAOQ,SAAS,CACvBiB,OA9DY,CACdC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCQ,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CP,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACxDC,KAAM,CAAC,SAAU,UAAW,UAAW,WAAY,YAAa,UAAW,aA2DzEC,aAAc,SAEhBM,WAAW,EAAInC,EAAOQ,SAAS,CAC7BiB,OA5DkB,CACpBC,OAAQ,CACNU,GAAI,KACJC,GAAI,KACJC,SAAU,cACVC,KAAM,gBACNC,QAAS,cACTC,UAAW,aACXC,QAAS,YACTC,MAAO,aAEThB,YAAa,CACXS,GAAI,KACJC,GAAI,KACJC,SAAU,cACVC,KAAM,gBACNC,QAAS,cACTC,UAAW,aACXC,QAAS,YACTC,MAAO,aAETf,KAAM,CACJQ,GAAI,KACJC,GAAI,KACJC,SAAU,cACVC,KAAM,gBACNC,QAAS,cACTC,UAAW,aACXC,QAAS,YACTC,MAAO,cAgCPd,aAAc,UAIlB/B,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O,kBChGzBZ,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIE,EAASC,EAAuB,EAAQ,QAI5C,SAASA,EAAuBK,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAE9F,IA8FIG,EA1CQ,CACVW,eAAe,EAzDHnB,EAAuB,EAAQ,MAyDhBO,SAAS,CAClCoC,aAtD4B,YAuD5BC,aAtD4B,OAuD5BC,cAAe,SAAuB/C,GACpC,OAAOgD,SAAShD,EAAO,GACzB,IAEFyB,KAAK,EAAIxB,EAAOQ,SAAS,CACvBwC,cA3DmB,CACrBtB,OAAQ,mBACRC,YAAa,oBACbC,KAAM,yBAyDJqB,kBAAmB,OACnBC,cAxDmB,CACrBC,IAAK,CAAC,KAAM,OAwDVC,kBAAmB,QAErBtB,SAAS,EAAI9B,EAAOQ,SAAS,CAC3BwC,cAzDuB,CACzBtB,OAAQ,WACRC,YAAa,YACbC,KAAM,sBAuDJqB,kBAAmB,OACnBC,cAtDuB,CACzBC,IAAK,CAAC,KAAM,KAAM,KAAM,OAsDtBC,kBAAmB,MACnBN,cAAe,SAAuBO,GACpC,OAAOA,EAAQ,CACjB,IAEFrB,OAAO,EAAIhC,EAAOQ,SAAS,CACzBwC,cA1DqB,CACvBtB,OAAQ,eACRC,YAAa,iEACbC,KAAM,+FAwDJqB,kBAAmB,OACnBC,cAvDqB,CACvBxB,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACtFyB,IAAK,CAAC,QAAS,QAAS,WAAY,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,UAsD1GC,kBAAmB,QAErBnB,KAAK,EAAIjC,EAAOQ,SAAS,CACvBwC,cAvDmB,CACrBtB,OAAQ,YACRQ,MAAO,2BACPP,YAAa,kCACbC,KAAM,kEAoDJqB,kBAAmB,OACnBC,cAnDmB,CACrBxB,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACnDyB,IAAK,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,SAkDpDC,kBAAmB,QAErBjB,WAAW,EAAInC,EAAOQ,SAAS,CAC7BwC,cAnDyB,CAC3BG,IAAK,2EAmDHF,kBAAmB,MACnBC,cAlDyB,CAC3BC,IAAK,CACHf,GAAI,OACJC,GAAI,OACJC,SAAU,gBACVC,KAAM,kBACNC,QAAS,WACTC,UAAW,UACXC,QAAS,SACTC,MAAO,WA0CPS,kBAAmB,SAIvBtD,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O,kBC3GzBZ,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCQ,EAF5BN,GAE4BM,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAgCIG,EAda,CACf6C,MAAM,EAAItD,EAAOQ,SAAS,CACxB+C,QApBc,CAChBC,KAAM,gBACNC,KAAM,WACNC,OAAQ,UACRxB,MAAO,WAiBLL,aAAc,SAEhB8B,MAAM,EAAI3D,EAAOQ,SAAS,CACxB+C,QAlBc,CAChBC,KAAM,gBACNC,KAAM,aACNC,OAAQ,WACRxB,MAAO,SAeLL,aAAc,SAEhB+B,UAAU,EAAI5D,EAAOQ,SAAS,CAC5B+C,QAhBkB,CACpBC,KAAM,yBACNC,KAAM,yBACNC,OAAQ,qBACRxB,MAAO,sBAaLL,aAAc,UAIlB/B,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O,gBC3CzBZ,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAI+D,EAAuB,CACzBC,SAAU,0BACVC,UAAW,kBACXC,MAAO,iBACPC,SAAU,gBACVC,SAAU,cACVC,MAAO,KAOL1D,EAJiB,SAAwB2D,EAAOC,EAAOC,EAAWhD,GACpE,OAAOuC,EAAqBO,EAC9B,EAGAtE,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O,gBCnBzBZ,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIyE,EAAuB,CACzBC,iBAAkB,CAChBC,IAAK,yBACLN,MAAO,iCAETO,SAAU,CACRD,IAAK,YACLN,MAAO,sBAETQ,YAAa,mBACbC,iBAAkB,CAChBH,IAAK,wBACLN,MAAO,gCAETU,SAAU,CACRJ,IAAK,aACLN,MAAO,qBAETW,YAAa,CACXL,IAAK,iBACLN,MAAO,0BAETY,OAAQ,CACNN,IAAK,QACLN,MAAO,iBAETa,MAAO,CACLP,IAAK,QACLN,MAAO,mBAETc,YAAa,CACXR,IAAK,kBACLN,MAAO,4BAETe,OAAQ,CACNT,IAAK,SACLN,MAAO,mBAETgB,aAAc,CACZV,IAAK,mBACLN,MAAO,8BAETiB,QAAS,CACPX,IAAK,UACLN,MAAO,qBAETkB,YAAa,CACXZ,IAAK,kBACLN,MAAO,2BAETmB,OAAQ,CACNb,IAAK,SACLN,MAAO,kBAEToB,WAAY,CACVd,IAAK,kBACLN,MAAO,2BAETqB,aAAc,CACZf,IAAK,eACLN,MAAO,yBA2BP1D,EAvBiB,SAAwB2D,EAAOqB,EAAOzE,GACzD,IAAI0E,EACAC,EAAapB,EAAqBH,GAUtC,OAPEsB,EADwB,iBAAfC,EACAA,EACU,IAAVF,EACAE,EAAWlB,IAEXkB,EAAWxB,MAAMyB,QAAQ,YAAaC,OAAOJ,IAGpDzE,SAA0CA,EAAQ8E,UAChD9E,EAAQ+E,YAAc/E,EAAQ+E,WAAa,EACtC,QAAUL,EAEVA,EAAS,WAIbA,CACT,EAGA5F,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/nl/index.js", "webpack://dtale/./node_modules/date-fns/locale/nl/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/nl/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/nl/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/nl/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/locale/nl/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Dutch locale.\n * @language Dutch\n * @iso-639-2 nld\n * <AUTHOR> [@jtangelder]{@link https://github.com/jtangelder}\n * <AUTHOR> [@rubenstolk]{@link https://github.com/rubenstolk}\n * <AUTHOR> [@bitcrumb]{@link https://github.com/bitcrumb}\n * <AUTHOR> Rivai [@edorivai]{@link https://github.com/edorivai}\n * <AUTHOR> Keurentjes [@curry684]{@link https://github.com/curry684}\n * <AUTHOR> Vermaas [@stefanvermaas]{@link https://github.com/stefanvermaas}\n */\nvar locale = {\n  code: 'nl',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 1\n    /* Monday */\n    ,\n    firstWeekContainsDate: 4\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['v.C.', 'n.C.'],\n  abbreviated: ['v.Chr.', 'n.Chr.'],\n  wide: ['voor Christus', 'na <PERSON><PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['K1', 'K2', 'K3', 'K4'],\n  wide: ['1e kwartaal', '2e kwartaal', '3e kwartaal', '4e kwartaal']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['jan.', 'feb.', 'mrt.', 'apr.', 'mei', 'jun.', 'jul.', 'aug.', 'sep.', 'okt.', 'nov.', 'dec.'],\n  wide: ['januari', 'februari', 'maart', 'april', 'mei', 'juni', 'juli', 'augustus', 'september', 'oktober', 'november', 'december']\n};\nvar dayValues = {\n  narrow: ['Z', 'M', 'D', 'W', 'D', 'V', 'Z'],\n  short: ['zo', 'ma', 'di', 'wo', 'do', 'vr', 'za'],\n  abbreviated: ['zon', 'maa', 'din', 'woe', 'don', 'vri', 'zat'],\n  wide: ['zondag', 'maandag', 'dinsdag', 'woensdag', 'donderdag', 'vrijdag', 'zaterdag']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'het middaguur',\n    morning: \"'s ochtends\",\n    afternoon: \"'s middags\",\n    evening: \"'s avonds\",\n    night: \"'s nachts\"\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'het middaguur',\n    morning: \"'s ochtends\",\n    afternoon: \"'s middags\",\n    evening: \"'s avonds\",\n    night: \"'s nachts\"\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'middernacht',\n    noon: 'het middaguur',\n    morning: \"'s ochtends\",\n    afternoon: \"'s middags\",\n    evening: \"'s avonds\",\n    night: \"'s nachts\"\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + 'e';\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)e?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^([vn]\\.? ?C\\.?)/,\n  abbreviated: /^([vn]\\. ?Chr\\.?)/,\n  wide: /^((voor|na) Christus)/\n};\nvar parseEraPatterns = {\n  any: [/^v/, /^n/]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^K[1234]/i,\n  wide: /^[1234]e kwartaal/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan.|feb.|mrt.|apr.|mei|jun.|jul.|aug.|sep.|okt.|nov.|dec.)/i,\n  wide: /^(januari|februari|maart|april|mei|juni|juli|augustus|september|oktober|november|december)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  any: [/^jan/i, /^feb/i, /^m(r|a)/i, /^apr/i, /^mei/i, /^jun/i, /^jul/i, /^aug/i, /^sep/i, /^okt/i, /^nov/i, /^dec/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[zmdwv]/i,\n  short: /^(zo|ma|di|wo|do|vr|za)/i,\n  abbreviated: /^(zon|maa|din|woe|don|vri|zat)/i,\n  wide: /^(zondag|maandag|dinsdag|woensdag|donderdag|vrijdag|zaterdag)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^z/i, /^m/i, /^d/i, /^w/i, /^d/i, /^v/i, /^z/i],\n  any: [/^zo/i, /^ma/i, /^di/i, /^wo/i, /^do/i, /^vr/i, /^za/i]\n};\nvar matchDayPeriodPatterns = {\n  any: /^(am|pm|middernacht|het middaguur|'s (ochtends|middags|avonds|nachts))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^am/i,\n    pm: /^pm/i,\n    midnight: /^middernacht/i,\n    noon: /^het middaguur/i,\n    morning: /ochtend/i,\n    afternoon: /middag/i,\n    evening: /avond/i,\n    night: /nacht/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'any'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'EEEE d MMMM y',\n  long: 'd MMMM y',\n  medium: 'd MMM y',\n  short: 'dd-MM-y'\n};\nvar timeFormats = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'om' {{time}}\",\n  long: \"{{date}} 'om' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: \"'afgelopen' eeee 'om' p\",\n  yesterday: \"'gisteren om' p\",\n  today: \"'vandaag om' p\",\n  tomorrow: \"'morgen om' p\",\n  nextWeek: \"eeee 'om' p\",\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'minder dan een seconde',\n    other: 'minder dan {{count}} seconden'\n  },\n  xSeconds: {\n    one: '1 seconde',\n    other: '{{count}} seconden'\n  },\n  halfAMinute: 'een halve minuut',\n  lessThanXMinutes: {\n    one: 'minder dan een minuut',\n    other: 'minder dan {{count}} minuten'\n  },\n  xMinutes: {\n    one: 'een minuut',\n    other: '{{count}} minuten'\n  },\n  aboutXHours: {\n    one: 'ongeveer 1 uur',\n    other: 'ongeveer {{count}} uur'\n  },\n  xHours: {\n    one: '1 uur',\n    other: '{{count}} uur'\n  },\n  xDays: {\n    one: '1 dag',\n    other: '{{count}} dagen'\n  },\n  aboutXWeeks: {\n    one: 'ongeveer 1 week',\n    other: 'ongeveer {{count}} weken'\n  },\n  xWeeks: {\n    one: '1 week',\n    other: '{{count}} weken'\n  },\n  aboutXMonths: {\n    one: 'ongeveer 1 maand',\n    other: 'ongeveer {{count}} maanden'\n  },\n  xMonths: {\n    one: '1 maand',\n    other: '{{count}} maanden'\n  },\n  aboutXYears: {\n    one: 'ongeveer 1 jaar',\n    other: 'ongeveer {{count}} jaar'\n  },\n  xYears: {\n    one: '1 jaar',\n    other: '{{count}} jaar'\n  },\n  overXYears: {\n    one: 'meer dan 1 jaar',\n    other: 'meer dan {{count}} jaar'\n  },\n  almostXYears: {\n    one: 'bijna 1 jaar',\n    other: 'bijna {{count}} jaar'\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'over ' + result;\n    } else {\n      return result + ' geleden';\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "_index", "_interopRequireDefault", "_index2", "_index3", "_index4", "_index5", "obj", "__esModule", "default", "_default", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "options", "weekStartsOn", "firstWeekContainsDate", "module", "ordinalNumber", "dirtyNumber", "_options", "Number", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "matchPattern", "parsePattern", "valueCallback", "parseInt", "matchPatterns", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "index", "date", "formats", "full", "long", "medium", "time", "dateTime", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "token", "_date", "_baseDate", "formatDistanceLocale", "lessThanXSeconds", "one", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "count", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sourceRoot": ""}
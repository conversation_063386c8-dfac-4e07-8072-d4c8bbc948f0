{"version": 3, "file": "85969.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IA6BII,EAda,CACfC,MAAM,EAAIJ,EAAOE,SAAS,CACxBG,QAjBc,CAChBC,KAAM,uBACNC,KAAM,iBACNC,OAAQ,eACRC,MAAO,cAcLC,aAAc,SAEhBC,MAAM,EAAIX,EAAOE,SAAS,CACxBG,QAfc,CAChBC,KAAM,eACNC,KAAM,YACNC,OAAQ,UACRC,MAAO,QAYLC,aAAc,SAEhBE,UAAU,EAAIZ,EAAOE,SAAS,CAC5BG,QAbkB,CACpBQ,IAAK,sBAaHH,aAAc,SAIlBb,EAAA,QAAkBM,EAClBW,EAAOjB,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/kk/_lib/formatLong/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: \"EEEE, do MMMM y 'ж.'\",\n  long: \"do MMMM y 'ж.'\",\n  medium: \"d MMM y 'ж.'\",\n  short: 'dd.MM.yyyy'\n};\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  any: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'any'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "date", "formats", "full", "long", "medium", "short", "defaultWidth", "time", "dateTime", "any", "module"], "sourceRoot": ""}
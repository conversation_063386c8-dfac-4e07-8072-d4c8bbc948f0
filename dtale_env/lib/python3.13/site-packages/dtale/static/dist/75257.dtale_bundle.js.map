{"version": 3, "file": "75257.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IA8III,EA9BW,CACbC,cArBkB,SAAuBC,EAAaC,GACtD,IAAIC,EAAOC,OAAOF,aAAyC,EAASA,EAAQC,MACxEE,EAASC,OAAOL,GAepB,OAAOI,GAZM,SAATF,EACa,IAAXE,GAA2B,KAAXA,EACT,KAEA,KAEO,WAATF,GAA8B,WAATA,GAA8B,SAATA,EAC1C,KAEA,KAIb,EAIEI,KAAK,EAAIX,EAAOE,SAAS,CACvBU,OAnHY,CACdC,OAAQ,CAAC,UAAW,QACpBC,YAAa,CAAC,WAAY,SAC1BC,KAAM,CAAC,eAAgB,cAiHrBC,aAAc,SAEhBC,SAAS,EAAIjB,EAAOE,SAAS,CAC3BU,OAlHgB,CAClBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,UAAW,UAAW,UAAW,WAC/CC,KAAM,CAAC,cAAe,cAAe,cAAe,gBAgHlDC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAInB,EAAOE,SAAS,CACzBU,OApHc,CAEhBC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,OAAQ,OAAQ,SAAU,QAAS,QAAS,QAAS,OAAQ,QAAS,SAAU,QAAS,UAAW,SAClHC,KAAM,CAAC,SAAU,QAAS,WAAY,UAAW,UAAW,UAAW,SAAU,UAAW,WAAY,UAAW,WAAY,YAiH7HC,aAAc,OACdI,iBAhHwB,CAC1BP,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,OAAQ,OAAQ,SAAU,QAAS,QAAS,QAAS,OAAQ,QAAS,SAAU,QAAS,UAAW,SAClHC,KAAM,CAAC,QAAS,SAAU,UAAW,SAAU,SAAU,SAAU,QAAS,SAAU,UAAW,SAAU,YAAa,WA8GtHM,uBAAwB,SAE1BC,KAAK,EAAItB,EAAOE,SAAS,CACvBU,OA/GY,CACdC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCU,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAC5CT,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACxDC,KAAM,CAAC,SAAU,YAAa,WAAY,SAAU,SAAU,WAAY,WA4GxEC,aAAc,SAEhBQ,WAAW,EAAIxB,EAAOE,SAAS,CAC7BU,OA7GkB,CACpBC,OAAQ,CACNY,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,OACNC,QAAS,QACTC,UAAW,OACXC,QAAS,OACTC,MAAO,OAETlB,YAAa,CACXW,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,OACNC,QAAS,QACTC,UAAW,OACXC,QAAS,OACTC,MAAO,OAETjB,KAAM,CACJU,GAAI,KACJC,GAAI,KACJC,SAAU,SACVC,KAAM,WACNC,QAAS,QACTC,UAAW,OACXC,QAAS,QACTC,MAAO,QAiFPhB,aAAc,MACdI,iBA/E4B,CAC9BP,OAAQ,CACNY,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,OACNC,QAAS,QACTC,UAAW,MACXC,QAAS,OACTC,MAAO,QAETlB,YAAa,CACXW,GAAI,KACJC,GAAI,KACJC,SAAU,QACVC,KAAM,OACNC,QAAS,QACTC,UAAW,MACXC,QAAS,OACTC,MAAO,QAETjB,KAAM,CACJU,GAAI,KACJC,GAAI,KACJC,SAAU,SACVC,KAAM,WACNC,QAAS,QACTC,UAAW,MACXC,QAAS,OACTC,MAAO,SAmDPX,uBAAwB,UAI5BxB,EAAA,QAAkBM,EAClB8B,EAAOpC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/uk/_lib/localize/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['до н.е.', 'н.е.'],\n  abbreviated: ['до н. е.', 'н. е.'],\n  wide: ['до нашої ери', 'нашої ери']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1-й кв.', '2-й кв.', '3-й кв.', '4-й кв.'],\n  wide: ['1-й квартал', '2-й квартал', '3-й квартал', '4-й квартал']\n};\nvar monthValues = {\n  // ДСТУ 3582:2013\n  narrow: ['С', 'Л', 'Б', 'К', 'Т', 'Ч', 'Л', 'С', 'В', 'Ж', 'Л', 'Г'],\n  abbreviated: ['січ.', 'лют.', 'берез.', 'квіт.', 'трав.', 'черв.', 'лип.', 'серп.', 'верес.', 'жовт.', 'листоп.', 'груд.'],\n  wide: ['січень', 'лютий', 'березень', 'квітень', 'травень', 'червень', 'липень', 'серпень', 'вересень', 'жовтень', 'листопад', 'грудень']\n};\nvar formattingMonthValues = {\n  narrow: ['С', 'Л', 'Б', 'К', 'Т', 'Ч', 'Л', 'С', 'В', 'Ж', 'Л', 'Г'],\n  abbreviated: ['січ.', 'лют.', 'берез.', 'квіт.', 'трав.', 'черв.', 'лип.', 'серп.', 'верес.', 'жовт.', 'листоп.', 'груд.'],\n  wide: ['січня', 'лютого', 'березня', 'квітня', 'травня', 'червня', 'липня', 'серпня', 'вересня', 'жовтня', 'листопада', 'грудня']\n};\nvar dayValues = {\n  narrow: ['Н', 'П', 'В', 'С', 'Ч', 'П', 'С'],\n  short: ['нд', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],\n  abbreviated: ['нед', 'пон', 'вів', 'сер', 'чтв', 'птн', 'суб'],\n  wide: ['неділя', 'понеділок', 'вівторок', 'середа', 'четвер', 'п’ятниця', 'субота']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ніч'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'веч.',\n    night: 'ніч'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'північ',\n    noon: 'полудень',\n    morning: 'ранок',\n    afternoon: 'день',\n    evening: 'вечір',\n    night: 'ніч'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  },\n  abbreviated: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'півн.',\n    noon: 'пол.',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  },\n  wide: {\n    am: 'ДП',\n    pm: 'ПП',\n    midnight: 'північ',\n    noon: 'полудень',\n    morning: 'ранку',\n    afternoon: 'дня',\n    evening: 'веч.',\n    night: 'ночі'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var unit = String(options === null || options === void 0 ? void 0 : options.unit);\n  var number = Number(dirtyNumber);\n  var suffix;\n\n  if (unit === 'date') {\n    if (number === 3 || number === 23) {\n      suffix = '-є';\n    } else {\n      suffix = '-е';\n    }\n  } else if (unit === 'minute' || unit === 'second' || unit === 'hour') {\n    suffix = '-а';\n  } else {\n    suffix = '-й';\n  }\n\n  return number + suffix;\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'any',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "options", "unit", "String", "number", "Number", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "module"], "sourceRoot": ""}
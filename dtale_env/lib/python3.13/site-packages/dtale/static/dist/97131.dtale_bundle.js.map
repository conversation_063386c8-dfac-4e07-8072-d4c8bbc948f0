{"version": 3, "file": "97131.dtale_bundle.js", "mappings": "4EA8CAA,EAAOC,QAtCP,SAAkBC,GAChB,MAAO,CACLC,KAAM,mBACNC,SAAU,CACRC,QAAS,+DACTC,SAAU,2GAEVC,QAAS,cAEXC,SAAU,CACRN,EAAKO,kBACLP,EAAKQ,YACLR,EAAKS,oBACLT,EAAKU,qBACL,CACEC,UAAW,QACXC,cAAe,uBAAwBC,IAAK,KAC5CC,QAAS,KACTR,SAAU,CACRN,EAAKe,QAAQf,EAAKgB,WAAY,CAC5BC,OAAQ,CAACC,gBAAgB,EAAMC,YAAY,OAIjD,CACER,UAAW,WACXC,cAAe,MACfC,IAAK,OAAQM,YAAY,EACzBjB,SAAU,eAEZ,CAEEkB,MAAO,gCAIf,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/protobuf.js"], "sourcesContent": ["/*\nLanguage: Protocol Buffers\nAuthor: <PERSON> <<EMAIL>>\nDescription: Protocol buffer message definition format\nWebsite: https://developers.google.com/protocol-buffers/docs/proto3\nCategory: protocols\n*/\n\nfunction protobuf(hljs) {\n  return {\n    name: 'Protocol Buffers',\n    keywords: {\n      keyword: 'package import option optional required repeated group oneof',\n      built_in: 'double float int32 int64 uint32 uint64 sint32 sint64 ' +\n        'fixed32 fixed64 sfixed32 sfixed64 bool string bytes',\n      literal: 'true false'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'message enum service', end: /\\{/,\n        illegal: /\\n/,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            starts: {endsWithParent: true, excludeEnd: true} // hack: eating everything after the first title\n          })\n        ]\n      },\n      {\n        className: 'function',\n        beginKeywords: 'rpc',\n        end: /[{;]/, excludeEnd: true,\n        keywords: 'rpc returns'\n      },\n      { // match enum items (relevance)\n        // BLAH = ...;\n        begin: /^\\s*[A-Z_]+(?=\\s*=[^\\n]+;$)/\n      }\n    ]\n  };\n}\n\nmodule.exports = protobuf;\n"], "names": ["module", "exports", "hljs", "name", "keywords", "keyword", "built_in", "literal", "contains", "QUOTE_STRING_MODE", "NUMBER_MODE", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "className", "beginKeywords", "end", "illegal", "inherit", "TITLE_MODE", "starts", "endsWithParent", "excludeEnd", "begin"], "sourceRoot": ""}
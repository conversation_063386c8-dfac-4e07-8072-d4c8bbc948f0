"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[85713],{37200:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={lastWeek:"'წინა' eeee p'-ზე'",yesterday:"'გუშინ' p'-ზე'",today:"'დღეს' p'-ზე'",tomorrow:"'ხვალ' p'-ზე'",nextWeek:"'შემდეგი' eeee p'-ზე'",other:"P"},r=function(e,t,r,u){return a[e]};t.default=r,e.exports=t.default}}]);
//# sourceMappingURL=85713.dtale_bundle.js.map
{"version": 3, "file": "97254.dtale_bundle.js", "mappings": "6EAkDAA,EAAOC,QA5CP,SAAiBC,GAgCf,MAAO,CACLC,KAAM,UACNC,kBAAkB,EAClBC,SAAU,CAlCI,CACdC,UAAW,SACXC,MAAO,oBACPC,IAAK,SAEM,CACXF,UAAW,SACXC,MAAO,wDAEa,CACpBD,UAAW,SACXC,MAAO,eAEQ,CACfD,UAAW,UACXG,UAAW,GACXC,SAAU,CACR,CACEH,MAAO,wFAET,CACEA,MAAO,mCAET,CACEA,MAAO,UAET,CACEA,MAAO,aAcf,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/subunit.js"], "sourcesContent": ["/*\nLanguage: SubUnit\nAuthor: <PERSON> <serge<PERSON><EMAIL>>\nWebsite: https://pypi.org/project/python-subunit/\n*/\n\nfunction subunit(hljs) {\n  const DETAILS = {\n    className: 'string',\n    begin: '\\\\[\\n(multipart)?',\n    end: '\\\\]\\n'\n  };\n  const TIME = {\n    className: 'string',\n    begin: '\\\\d{4}-\\\\d{2}-\\\\d{2}(\\\\s+)\\\\d{2}:\\\\d{2}:\\\\d{2}\\.\\\\d+Z'\n  };\n  const PROGRESSVALUE = {\n    className: 'string',\n    begin: '(\\\\+|-)\\\\d+'\n  };\n  const KEYWORDS = {\n    className: 'keyword',\n    relevance: 10,\n    variants: [\n      {\n        begin: '^(test|testing|success|successful|failure|error|skip|xfail|uxsuccess)(:?)\\\\s+(test)?'\n      },\n      {\n        begin: '^progress(:?)(\\\\s+)?(pop|push)?'\n      },\n      {\n        begin: '^tags:'\n      },\n      {\n        begin: '^time:'\n      }\n    ]\n  };\n  return {\n    name: 'SubUnit',\n    case_insensitive: true,\n    contains: [\n      DETAILS,\n      TIME,\n      PROGRESSVALUE,\n      KEYWORDS\n    ]\n  };\n}\n\nmodule.exports = subunit;\n"], "names": ["module", "exports", "hljs", "name", "case_insensitive", "contains", "className", "begin", "end", "relevance", "variants"], "sourceRoot": ""}
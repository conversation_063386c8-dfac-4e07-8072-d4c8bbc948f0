{"version": 3, "file": "97813.dtale_bundle.js", "mappings": "sGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIE,EAASC,EAAuB,EAAQ,QAExCC,EAAUD,EAAuB,EAAQ,QAEzCE,EAAUF,EAAuB,EAAQ,QAEzCG,EAAUH,EAAuB,EAAQ,QAEzCI,EAAUJ,EAAuB,EAAQ,QAE7C,SAASA,EAAuBK,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,EAAO,CAU9F,IAcIG,EAdS,CACXC,KAAM,QACNC,eAAgBX,EAAOQ,QACvBI,WAAYP,EAAQG,QACpBK,eAAgBX,EAAQM,QACxBM,SAAUX,EAAQK,QAClBO,MAAOX,EAAQI,QACfQ,QAAS,CACPC,aAAc,EAGdC,sBAAuB,IAI3BpB,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O,kBCzCzBZ,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCQ,EAF5BN,GAE4BM,EAFI,EAAQ,SAESA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAgCIG,EAda,CACfW,MAAM,EAAIpB,EAAOQ,SAAS,CACxBa,QApBc,CAChBC,KAAM,oBACNC,KAAM,cACNC,OAAQ,aACRC,MAAO,cAiBLC,aAAc,SAEhBC,MAAM,EAAI3B,EAAOQ,SAAS,CACxBa,QAlBc,CAChBC,KAAM,gBACNC,KAAM,aACNC,OAAQ,WACRC,MAAO,SAeLC,aAAc,SAEhBE,UAAU,EAAI5B,EAAOQ,SAAS,CAC5Ba,QAhBkB,CACpBC,KAAM,yBACNC,KAAM,yBACNC,OAAQ,qBACRC,MAAO,sBAaLC,aAAc,UAIlB5B,EAAA,QAAkBW,EAClBU,EAAOrB,QAAUA,EAAQU,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/en-GB/index.js", "webpack://dtale/./node_modules/date-fns/locale/en-GB/_lib/formatLong/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../en-US/_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../en-US/_lib/formatRelative/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"../en-US/_lib/localize/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"../en-US/_lib/match/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary English locale (United Kingdom).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@glintik]{@link https://github.com/glintik}\n */\nvar locale = {\n  code: 'en-GB',\n  formatDistance: _index.default,\n  formatLong: _index5.default,\n  formatRelative: _index2.default,\n  localize: _index3.default,\n  match: _index4.default,\n  options: {\n    weekStartsOn: 1\n    /* Monday */\n    ,\n    firstWeekContainsDate: 4\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'EEEE, d MMMM yyyy',\n  long: 'd MMMM yyyy',\n  medium: 'd MMM yyyy',\n  short: 'dd/MM/yyyy'\n};\nvar timeFormats = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats = {\n  full: \"{{date}} 'at' {{time}}\",\n  long: \"{{date}} 'at' {{time}}\",\n  medium: '{{date}}, {{time}}',\n  short: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "_index", "_interopRequireDefault", "_index2", "_index3", "_index4", "_index5", "obj", "__esModule", "default", "_default", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "options", "weekStartsOn", "firstWeekContainsDate", "module", "date", "formats", "full", "long", "medium", "short", "defaultWidth", "time", "dateTime"], "sourceRoot": ""}
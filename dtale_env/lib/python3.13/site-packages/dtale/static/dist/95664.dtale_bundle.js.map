{"version": 3, "file": "95664.dtale_bundle.js", "mappings": "4EAqEAA,EAAOC,QA7DP,SAAaC,GACX,MAAMC,EAAUD,EAAKC,QACnB,cAAe,IACf,CACEC,UAAW,KAQf,MAAO,CACLC,KAAM,mBACNC,QAAS,CACP,MACA,OAEFC,kBAAkB,EAClBC,QAAS,OACTC,SAAU,CACRC,QACE,wFAEFC,SACE,ofAUJC,SAAU,CACR,CACEC,UAAW,WACXC,MAAO,4BAET,CACED,UAAW,WACXC,MAjCG,mDAkCHC,IAAK,WACLH,SAAU,CACRV,EAAKc,QAAQd,EAAKe,WAAY,CAC5BH,MAAO,sDAETX,IAGJ,CACEU,UAAW,SACXC,MAAO,UACPV,UAAW,GAEbD,GAGN,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/dos.js"], "sourcesContent": ["/*\nLanguage: Batch file (DOS)\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Batch_file\n*/\n\n/** @type LanguageFn */\nfunction dos(hljs) {\n  const COMMENT = hljs.COMMENT(\n    /^\\s*@?rem\\b/, /$/,\n    {\n      relevance: 10\n    }\n  );\n  const LABEL = {\n    className: 'symbol',\n    begin: '^\\\\s*[A-Za-z._?][A-Za-z0-9_$#@~.?]*(:|\\\\s+label)',\n    relevance: 0\n  };\n  return {\n    name: 'Batch file (DOS)',\n    aliases: [\n      'bat',\n      'cmd'\n    ],\n    case_insensitive: true,\n    illegal: /\\/\\*/,\n    keywords: {\n      keyword:\n        'if else goto for in do call exit not exist errorlevel defined ' +\n        'equ neq lss leq gtr geq',\n      built_in:\n        'prn nul lpt3 lpt2 lpt1 con com4 com3 com2 com1 aux ' +\n        'shift cd dir echo setlocal endlocal set pause copy ' +\n        'append assoc at attrib break cacls cd chcp chdir chkdsk chkntfs cls cmd color ' +\n        'comp compact convert date dir diskcomp diskcopy doskey erase fs ' +\n        'find findstr format ftype graftabl help keyb label md mkdir mode more move path ' +\n        'pause print popd pushd promt rd recover rem rename replace restore rmdir shift ' +\n        'sort start subst time title tree type ver verify vol ' +\n        // winutils\n        'ping net ipconfig taskkill xcopy ren del'\n    },\n    contains: [\n      {\n        className: 'variable',\n        begin: /%%[^ ]|%[^ ]+?%|![^ ]+?!/\n      },\n      {\n        className: 'function',\n        begin: LABEL.begin,\n        end: 'goto:eof',\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            begin: '([_a-zA-Z]\\\\w*\\\\.)*([_a-zA-Z]\\\\w*:)?[_a-zA-Z]\\\\w*'\n          }),\n          COMMENT\n        ]\n      },\n      {\n        className: 'number',\n        begin: '\\\\b\\\\d+',\n        relevance: 0\n      },\n      COMMENT\n    ]\n  };\n}\n\nmodule.exports = dos;\n"], "names": ["module", "exports", "hljs", "COMMENT", "relevance", "name", "aliases", "case_insensitive", "illegal", "keywords", "keyword", "built_in", "contains", "className", "begin", "end", "inherit", "TITLE_MODE"], "sourceRoot": ""}
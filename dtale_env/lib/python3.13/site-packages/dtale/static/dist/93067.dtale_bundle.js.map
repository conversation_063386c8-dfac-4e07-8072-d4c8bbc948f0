{"version": 3, "file": "93067.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IA6BII,EAda,CACfC,MAAM,EAAIJ,EAAOE,SAAS,CACxBG,QAjBc,CAChBC,KAAM,qBACNC,KAAM,eACNC,OAAQ,cACRC,MAAO,cAcLC,aAAc,SAEhBC,MAAM,EAAIX,EAAOE,SAAS,CACxBG,QAfc,CAChBC,KAAM,gBACNC,KAAM,aACNC,OAAQ,WACRC,MAAO,QAYLC,aAAc,SAEhBE,UAAU,EAAIZ,EAAOE,SAAS,CAC5BG,QAbkB,CACpBQ,IAAK,qBAaHH,aAAc,SAIlBb,EAAA,QAAkBM,EAClBW,EAAOjB,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/mk/_lib/formatLong/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'EEEE, dd MMMM yyyy',\n  long: 'dd MMMM yyyy',\n  medium: 'dd MMM yyyy',\n  short: 'dd/MM/yyyy'\n};\nvar timeFormats = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  any: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'any'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "date", "formats", "full", "long", "medium", "short", "defaultWidth", "time", "dateTime", "any", "module"], "sourceRoot": ""}
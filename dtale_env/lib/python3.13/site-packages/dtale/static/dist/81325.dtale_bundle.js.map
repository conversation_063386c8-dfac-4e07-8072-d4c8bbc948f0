{"version": 3, "file": "81325.dtale_bundle.js", "mappings": "6EAoBA,SAASA,KAAUC,GAEjB,OADeA,EAAKC,IAAKC,IAAMC,OAZjBC,EAYwBF,GAVpB,iBAAPE,EAAwBA,EAE5BA,EAAGD,OAHM,KADlB,IAAgBC,IAY4BC,KAAK,GAEjD,CAyFAC,EAAOC,QA/EP,SAAsBC,GACpB,MAGMC,EAAoB,CACxBC,UAAW,WACXC,MAAO,SACPC,IANe,2BAOfC,UAAW,IAGb,MAAO,CACLC,KAAM,eACNC,QAAS,CAAE,MACXC,SAAU,CACRC,QAAS,mUAKTC,QAAS,6BAEXC,SAAU,CACRX,EAAKY,iBACLZ,EAAKa,kBACLb,EAAKc,oBACLd,EAAKe,qBACLf,EAAKgB,cACL,CACEd,UAAW,QACXe,cAAe,UACfb,IAAK,KACLO,SAAU,CAAEX,EAAKkB,aAEnB,CACEhB,UAAW,QACXe,cAAe,kBACfb,IAAK,KACLe,YAAY,EACZR,SAAU,CACR,CAAEM,cAAe,sBACjBjB,EAAKkB,aAGT,CACEhB,UAAW,OACXe,cAAe,iBACfb,IAAK,IACLI,SAAU,CAAE,eAAgB,mBAE9B,CACEN,UAAW,WACXe,cAAe,WACfb,IAAK,OACLe,YAAY,EACZC,QAAS,KACTT,SAAU,CACRX,EAAKkB,WACL,CACEhB,UAAW,SACXC,MAAO,KACPC,IAAK,KACLO,SAAU,CACRX,EAAKY,iBACLZ,EAAKa,kBACLb,EAAKc,oBACLd,EAAKe,qBACLd,IAGJ,CAAEE,MAAOZ,EAAO,OApEU,qCAuE9BS,EAAKqB,cAEPD,QAAS,IAEb,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/actionscript.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: ActionScript\nAuthor: <PERSON> <<EMAIL>>\nCategory: scripting\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction actionscript(hljs) {\n  const IDENT_RE = /[a-zA-Z_$][a-zA-Z0-9_$]*/;\n  const IDENT_FUNC_RETURN_TYPE_RE = /([*]|[a-zA-Z_$][a-zA-Z0-9_$]*)/;\n\n  const AS3_REST_ARG_MODE = {\n    className: 'rest_arg',\n    begin: /[.]{3}/,\n    end: IDENT_RE,\n    relevance: 10\n  };\n\n  return {\n    name: 'ActionScript',\n    aliases: [ 'as' ],\n    keywords: {\n      keyword: 'as break case catch class const continue default delete do dynamic each ' +\n        'else extends final finally for function get if implements import in include ' +\n        'instanceof interface internal is namespace native new override package private ' +\n        'protected public return set static super switch this throw try typeof use var void ' +\n        'while with',\n      literal: 'true false null undefined'\n    },\n    contains: [\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'package',\n        end: /\\{/,\n        contains: [ hljs.TITLE_MODE ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          { beginKeywords: 'extends implements' },\n          hljs.TITLE_MODE\n        ]\n      },\n      {\n        className: 'meta',\n        beginKeywords: 'import include',\n        end: /;/,\n        keywords: { 'meta-keyword': 'import include' }\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: /[{;]/,\n        excludeEnd: true,\n        illegal: /\\S/,\n        contains: [\n          hljs.TITLE_MODE,\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            contains: [\n              hljs.APOS_STRING_MODE,\n              hljs.QUOTE_STRING_MODE,\n              hljs.C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              AS3_REST_ARG_MODE\n            ]\n          },\n          { begin: concat(/:\\s*/, IDENT_FUNC_RETURN_TYPE_RE) }\n        ]\n      },\n      hljs.METHOD_GUARD\n    ],\n    illegal: /#/\n  };\n}\n\nmodule.exports = actionscript;\n"], "names": ["concat", "args", "map", "x", "source", "re", "join", "module", "exports", "hljs", "AS3_REST_ARG_MODE", "className", "begin", "end", "relevance", "name", "aliases", "keywords", "keyword", "literal", "contains", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "C_NUMBER_MODE", "beginKeywords", "TITLE_MODE", "excludeEnd", "illegal", "METHOD_GUARD"], "sourceRoot": ""}
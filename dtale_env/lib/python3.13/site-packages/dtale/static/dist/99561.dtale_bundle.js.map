{"version": 3, "file": "99561.dtale_bundle.js", "mappings": "gGAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAIvF,IAgJII,EA5BW,CACbC,cALkB,SAAuBC,EAAaC,GACtD,OAAOC,OAAOF,EAChB,EAIEG,KAAK,EAAIR,EAAOE,SAAS,CACvBO,OAvHY,CACdC,OAAQ,CAAC,OAAQ,MACjBC,YAAa,CAAC,aAAc,QAC5BC,KAAM,CAAC,gBAAiB,WAqHtBC,aAAc,SAEhBC,SAAS,EAAId,EAAOE,SAAS,CAC3BO,OApHgB,CAClBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,KAAM,KAAM,KAAM,MAChCC,KAAM,CAAC,cAAe,cAAe,cAAe,gBAkHlDC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAIhB,EAAOE,SAAS,CACzBO,OAhHc,CAChBC,OAAQ,CAAC,KAAM,KAAM,KAAM,IAAK,KAAM,KAAM,KAAM,IAAK,IAAK,IAAK,IAAK,MACtEC,YAAa,CAAC,SAAU,SAAU,QAAS,SAAU,KAAM,MAAO,QAAS,QAAS,QAAS,QAAS,MAAO,QAC7GC,KAAM,CAAC,YAAa,YAAa,QAAS,SAAU,KAAM,MAAO,QAAS,QAAS,YAAa,UAAW,UAAW,aA8GpHC,aAAc,SAEhBI,KAAK,EAAIjB,EAAOE,SAAS,CACvBO,OA7GY,CACdC,OAAQ,CAAC,IAAK,KAAM,KAAM,KAAM,KAAM,KAAM,KAC5CQ,MAAO,CAAC,IAAK,KAAM,KAAM,KAAM,KAAM,KAAM,KAC3CP,YAAa,CAAC,MAAO,MAAO,OAAQ,MAAO,OAAQ,QAAS,OAC5DC,KAAM,CAAC,SAEL,SAEA,UAEA,SAEA,UAEA,WAEA,WA8FAC,aAAc,SAEhBM,WAAW,EAAInB,EAAOE,SAAS,CAC7BO,OA3FkB,CACpBC,OAAQ,CACNU,GAAI,KACJC,GAAI,KACJC,SAAU,WACVC,KAAM,KACNC,QAAS,QACTC,UAAW,QACXC,QAAS,QACTC,MAAO,UAEThB,YAAa,CACXS,GAAI,KACJC,GAAI,KACJC,SAAU,cACVC,KAAM,QACNC,QAAS,QACTC,UAAW,QACXC,QAAS,QACTC,MAAO,UAETf,KAAM,CACJQ,GAAI,KACJC,GAAI,KACJC,SAAU,cACVC,KAAM,QACNC,QAAS,QACTC,UAAW,QACXC,QAAS,QACTC,MAAO,WA+DPd,aAAc,OACde,iBA7D4B,CAC9BlB,OAAQ,CACNU,GAAI,KACJC,GAAI,KACJC,SAAU,WACVC,KAAM,QACNC,QAAS,QACTC,UAAW,QACXC,QAAS,QACTC,MAAO,UAEThB,YAAa,CACXS,GAAI,KACJC,GAAI,KACJC,SAAU,aACVC,KAAM,QACNC,QAAS,QACTC,UAAW,QACXC,QAAS,QACTC,MAAO,UAETf,KAAM,CACJQ,GAAI,KACJC,GAAI,KACJC,SAAU,cACVC,KAAM,QACNC,QAAS,QACTC,UAAW,QACXC,QAAS,QACTC,MAAO,WAiCPE,uBAAwB,UAI5BhC,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/gu/_lib/localize/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1621 - #1630\nvar eraValues = {\n  narrow: ['ઈસપૂ', 'ઈસ'],\n  abbreviated: ['ઈ.સ.પૂર્વે', 'ઈ.સ.'],\n  wide: ['ઈસવીસન પૂર્વે', 'ઈસવીસન']\n}; // https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1631 - #1654\n\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['Q1', 'Q2', 'Q3', 'Q4'],\n  wide: ['1લો ત્રિમાસ', '2જો ત્રિમાસ', '3જો ત્રિમાસ', '4થો ત્રિમાસ']\n}; // Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n// https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1655 - #1726\n\nvar monthValues = {\n  narrow: ['જા', 'ફે', 'મા', 'એ', 'મે', 'જૂ', 'જુ', 'ઓ', 'સ', 'ઓ', 'ન', 'ડિ'],\n  abbreviated: ['જાન્યુ', 'ફેબ્રુ', 'માર્ચ', 'એપ્રિલ', 'મે', 'જૂન', 'જુલાઈ', 'ઑગસ્ટ', 'સપ્ટે', 'ઓક્ટો', 'નવે', 'ડિસે'],\n  wide: ['જાન્યુઆરી', 'ફેબ્રુઆરી', 'માર્ચ', 'એપ્રિલ', 'મે', 'જૂન', 'જુલાઇ', 'ઓગસ્ટ', 'સપ્ટેમ્બર', 'ઓક્ટોબર', 'નવેમ્બર', 'ડિસેમ્બર']\n}; // https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1727 - #1768\n\nvar dayValues = {\n  narrow: ['ર', 'સો', 'મં', 'બુ', 'ગુ', 'શુ', 'શ'],\n  short: ['ર', 'સો', 'મં', 'બુ', 'ગુ', 'શુ', 'શ'],\n  abbreviated: ['રવિ', 'સોમ', 'મંગળ', 'બુધ', 'ગુરુ', 'શુક્ર', 'શનિ'],\n  wide: ['રવિવાર'\n  /* Sunday */\n  , 'સોમવાર'\n  /* Monday */\n  , 'મંગળવાર'\n  /* Tuesday */\n  , 'બુધવાર'\n  /* Wednesday */\n  , 'ગુરુવાર'\n  /* Thursday */\n  , 'શુક્રવાર'\n  /* Friday */\n  , 'શનિવાર'\n  /* Saturday */\n  ]\n}; // https://www.unicode.org/cldr/charts/32/summary/gu.html\n// #1783 - #1824\n\nvar dayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'મ.રાત્રિ',\n    noon: 'બ.',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: '​મધ્યરાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: '​મધ્યરાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'મ.રાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'મધ્યરાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  },\n  wide: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: '​મધ્યરાત્રિ',\n    noon: 'બપોરે',\n    morning: 'સવારે',\n    afternoon: 'બપોરે',\n    evening: 'સાંજે',\n    night: 'રાત્રે'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "_options", "String", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingValues", "defaultFormattingWidth", "module"], "sourceRoot": ""}
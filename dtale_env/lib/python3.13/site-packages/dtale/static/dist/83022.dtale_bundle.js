"use strict";(self.webpackChunkdtale=self.webpackChunkdtale||[]).push([[83022],{52391:(e,o)=>{Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var t={lessThanXSeconds:{one:"помалку од секунда",other:"помалку од {{count}} секунди"},xSeconds:{one:"1 секунда",other:"{{count}} секунди"},halfAMinute:"половина минута",lessThanXMinutes:{one:"помалку од минута",other:"помалку од {{count}} минути"},xMinutes:{one:"1 минута",other:"{{count}} минути"},aboutXHours:{one:"околу 1 час",other:"околу {{count}} часа"},xHours:{one:"1 час",other:"{{count}} часа"},xDays:{one:"1 ден",other:"{{count}} дена"},aboutXWeeks:{one:"околу 1 недела",other:"околу {{count}} месеци"},xWeeks:{one:"1 недела",other:"{{count}} недели"},aboutXMonths:{one:"околу 1 месец",other:"околу {{count}} недели"},xMonths:{one:"1 месец",other:"{{count}} месеци"},aboutXYears:{one:"околу 1 година",other:"околу {{count}} години"},xYears:{one:"1 година",other:"{{count}} години"},overXYears:{one:"повеќе од 1 година",other:"повеќе од {{count}} години"},almostXYears:{one:"безмалку 1 година",other:"безмалку {{count}} години"}},n=function(e,o,n){var u,r=t[e];return u="string"==typeof r?r:1===o?r.one:r.other.replace("{{count}}",String(o)),null!=n&&n.addSuffix?n.comparison&&n.comparison>0?"за "+u:"пред "+u:u};o.default=n,e.exports=o.default}}]);
//# sourceMappingURL=83022.dtale_bundle.js.map
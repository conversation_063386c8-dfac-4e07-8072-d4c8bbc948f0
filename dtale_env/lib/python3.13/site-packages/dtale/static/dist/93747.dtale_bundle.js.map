{"version": 3, "file": "93747.dtale_bundle.js", "mappings": "8FAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIE,EAAqB,CAAC,SAAU,UAAW,QAAS,SAAU,UAAW,QAAS,UAClFC,EAAuB,CACzBC,SAAU,yBACVC,UAAW,cACXC,MAAO,aACPC,SAAU,cACVC,SAAU,SAAkBC,GAC1B,IAAIC,EAAMD,EAAKE,YACf,MAAO,MAAQT,EAAmBQ,GAAO,OAC3C,EACAE,MAAO,KAaLC,EAViB,SAAwBC,EAAOL,GAClD,IAAIM,EAASZ,EAAqBW,GAElC,MAAsB,mBAAXC,EACFA,EAAON,GAGTM,CACT,EAGAf,EAAA,QAAkBa,EAClBG,EAAOhB,QAAUA,EAAQiB,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/cs/_lib/formatRelative/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar accusativeWeekdays = ['ned<PERSON><PERSON>', 'ponděl<PERSON>', 'úterý', 'středu', 'čtvrtek', 'pátek', 'sobotu'];\nvar formatRelativeLocale = {\n  lastWeek: \"'poslední' eeee 've' p\",\n  yesterday: \"'včera v' p\",\n  today: \"'dnes v' p\",\n  tomorrow: \"'zítra v' p\",\n  nextWeek: function nextWeek(date) {\n    var day = date.getUTCDay();\n    return \"'v \" + accusativeWeekdays[day] + \" o' p\";\n  },\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, date) {\n  var format = formatRelativeLocale[token];\n\n  if (typeof format === 'function') {\n    return format(date);\n  }\n\n  return format;\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "accusativeWeekdays", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "date", "day", "getUTCDay", "other", "_default", "token", "format", "module", "default"], "sourceRoot": ""}
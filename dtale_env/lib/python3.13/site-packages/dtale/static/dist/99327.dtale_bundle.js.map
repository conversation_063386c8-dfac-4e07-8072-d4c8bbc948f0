{"version": 3, "file": "99327.dtale_bundle.js", "mappings": "6HAEAA,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAuHII,EA5BW,CACbC,cANkB,SAAuBC,EAAaC,GAEtD,OADaC,OAAOF,GACJ,GAClB,EAIEG,KAAK,EAAIR,EAAOE,SAAS,CACvBO,OA9FY,CACdC,OAAQ,CAAC,aAAc,aACvBC,YAAa,CAAC,aAAc,aAC5BC,KAAM,CAAC,qBAAsB,oBA4F3BC,aAAc,SAEhBC,SAAS,EAAId,EAAOE,SAAS,CAC3BO,OA7FgB,CAClBC,OAAQ,CAAC,IAAK,IAAK,IAAK,KACxBC,YAAa,CAAC,UAAW,UAAW,UAAW,WAC/CC,KAAM,CAAC,gBAAiB,gBAAiB,gBAAiB,kBA2FxDC,aAAc,OACdE,iBAAkB,SAA0BD,GAC1C,OAAOA,EAAU,CACnB,IAEFE,OAAO,EAAIhB,EAAOE,SAAS,CACzBO,OA/Fc,CAChBC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAChEC,YAAa,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QACrGC,KAAM,CAAC,SAAU,UAAW,QAAS,QAAS,MAAO,QAAS,QAAS,SAAU,YAAa,UAAW,WAAY,aA6FnHC,aAAc,SAEhBI,KAAK,EAAIjB,EAAOE,SAAS,CACvBO,OA9FY,CACdC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACvCQ,MAAO,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QACxDP,YAAa,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QAC9DC,KAAM,CAAC,UAAW,aAAc,QAAS,QAAS,UAAW,QAAS,WA2FpEC,aAAc,SAEhBM,WAAW,EAAInB,EAAOE,SAAS,CAC7BO,OA5FkB,CACpBC,OAAQ,CACNU,GAAI,IACJC,GAAI,IACJC,SAAU,QACVC,KAAM,QACNC,QAAS,IACTC,UAAW,IACXC,QAAS,IACTC,MAAO,KAEThB,YAAa,CACXS,GAAI,OACJC,GAAI,OACJC,SAAU,QACVC,KAAM,QACNC,QAAS,OACTC,UAAW,OACXC,QAAS,OACTC,MAAO,OAETf,KAAM,CACJQ,GAAI,OACJC,GAAI,OACJC,SAAU,SACVC,KAAM,SACNC,QAAS,QACTC,UAAW,WACXC,QAAS,QACTC,MAAO,QAgEPd,aAAc,OACde,iBA9D4B,CAC9BlB,OAAQ,CACNU,GAAI,IACJC,GAAI,IACJC,SAAU,QACVC,KAAM,QACNC,QAAS,KACTC,UAAW,IACXC,QAAS,KACTC,MAAO,MAEThB,YAAa,CACXS,GAAI,OACJC,GAAI,OACJC,SAAU,SACVC,KAAM,SACNC,QAAS,QACTC,UAAW,OACXC,QAAS,QACTC,MAAO,UAETf,KAAM,CACJQ,GAAI,OACJC,GAAI,OACJC,SAAU,WACVC,KAAM,UACNC,QAAS,UACTC,UAAW,WACXC,QAAS,SACTC,MAAO,WAkCPE,uBAAwB,UAI5BhC,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,gBClIzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAMlB,IAAIkC,EAAuB,CACzBC,iBAAkB,CAChBC,QAAS,CACPC,IAAK,6BACLC,IAAK,6BACLC,IAAK,6BACLC,MAAO,6BAETC,KAAM,CACJJ,IAAK,6BACLC,IAAK,+BACLC,IAAK,+BACLC,MAAO,gCAETE,OAAQ,CACNL,IAAK,6BACLC,IAAK,6BACLC,IAAK,6BACLC,MAAO,8BAGXG,SAAU,CACRP,QAAS,CACPC,IAAK,oBACLC,IAAK,oBACLC,IAAK,oBACLC,MAAO,oBAETC,KAAM,CACJJ,IAAK,oBACLC,IAAK,sBACLC,IAAK,sBACLC,MAAO,uBAETE,OAAQ,CACNL,IAAK,oBACLC,IAAK,oBACLC,IAAK,oBACLC,MAAO,qBAGXI,YAAa,aACbC,iBAAkB,CAChBT,QAAS,CACPC,IAAK,4BACLC,IAAK,4BACLC,IAAK,4BACLC,MAAO,4BAETC,KAAM,CACJJ,IAAK,4BACLC,IAAK,8BACLC,IAAK,8BACLC,MAAO,+BAETE,OAAQ,CACNL,IAAK,4BACLC,IAAK,4BACLC,IAAK,4BACLC,MAAO,6BAGXM,SAAU,CACRV,QAAS,CACPC,IAAK,mBACLC,IAAK,mBACLC,IAAK,mBACLC,MAAO,mBAETC,KAAM,CACJJ,IAAK,mBACLC,IAAK,qBACLC,IAAK,qBACLC,MAAO,sBAETE,OAAQ,CACNL,IAAK,mBACLC,IAAK,mBACLC,IAAK,mBACLC,MAAO,oBAGXO,YAAa,CACXX,QAAS,CACPC,IAAK,0BACLC,IAAK,0BACLC,IAAK,0BACLC,MAAO,0BAETC,KAAM,CACJJ,IAAK,0BACLC,IAAK,4BACLC,IAAK,4BACLC,MAAO,6BAETE,OAAQ,CACNL,IAAK,0BACLC,IAAK,0BACLC,IAAK,0BACLC,MAAO,2BAGXQ,OAAQ,CACNZ,QAAS,CACPC,IAAK,gBACLC,IAAK,gBACLC,IAAK,gBACLC,MAAO,gBAETC,KAAM,CACJJ,IAAK,gBACLC,IAAK,kBACLC,IAAK,kBACLC,MAAO,mBAETE,OAAQ,CACNL,IAAK,gBACLC,IAAK,gBACLC,IAAK,gBACLC,MAAO,iBAGXS,MAAO,CACLb,QAAS,CACPC,IAAK,gBACLC,IAAK,gBACLC,IAAK,gBACLC,MAAO,iBAETC,KAAM,CACJJ,IAAK,iBACLC,IAAK,oBACLC,IAAK,kBACLC,MAAO,mBAETE,OAAQ,CACNL,IAAK,gBACLC,IAAK,gBACLC,IAAK,gBACLC,MAAO,kBAIXU,YAAa,CACXb,IAAK,4BACLC,IAAK,4BACLC,IAAK,4BACLC,MAAO,8BAGTW,OAAQ,CACNd,IAAK,kBACLC,IAAK,kBACLC,IAAK,kBACLC,MAAO,oBAETY,aAAc,CACZhB,QAAS,CACPC,IAAK,4BACLC,IAAK,6BACLC,IAAK,6BACLC,MAAO,+BAETC,KAAM,CACJJ,IAAK,8BACLC,IAAK,+BACLC,IAAK,6BACLC,MAAO,8BAETE,OAAQ,CACNL,IAAK,4BACLC,IAAK,6BACLC,IAAK,6BACLC,MAAO,gCAGXa,QAAS,CACPjB,QAAS,CACPC,IAAK,kBACLC,IAAK,mBACLC,IAAK,mBACLC,MAAO,qBAETC,KAAM,CACJJ,IAAK,oBACLC,IAAK,qBACLC,IAAK,mBACLC,MAAO,oBAETE,OAAQ,CACNL,IAAK,kBACLC,IAAK,mBACLC,IAAK,mBACLC,MAAO,sBAGXc,YAAa,CACXlB,QAAS,CACPC,IAAK,2BACLC,IAAK,2BACLC,IAAK,2BACLC,MAAO,2BAETC,KAAM,CACJJ,IAAK,4BACLC,IAAK,6BACLC,IAAK,2BACLC,MAAO,4BAETE,OAAQ,CACNL,IAAK,2BACLC,IAAK,2BACLC,IAAK,2BACLC,MAAO,4BAGXe,OAAQ,CACNnB,QAAS,CACPC,IAAK,iBACLC,IAAK,iBACLC,IAAK,iBACLC,MAAO,iBAETC,KAAM,CACJJ,IAAK,kBACLC,IAAK,mBACLC,IAAK,iBACLC,MAAO,kBAETE,OAAQ,CACNL,IAAK,iBACLC,IAAK,iBACLC,IAAK,iBACLC,MAAO,kBAGXgB,WAAY,CACVpB,QAAS,CACPC,IAAK,yBACLC,IAAK,yBACLC,IAAK,yBACLC,MAAO,yBAETC,KAAM,CACJJ,IAAK,0BACLC,IAAK,2BACLC,IAAK,yBACLC,MAAO,0BAETE,OAAQ,CACNL,IAAK,yBACLC,IAAK,yBACLC,IAAK,yBACLC,MAAO,0BAGXiB,aAAc,CACZrB,QAAS,CACPC,IAAK,wBACLC,IAAK,wBACLC,IAAK,wBACLC,MAAO,wBAETC,KAAM,CACJJ,IAAK,yBACLC,IAAK,0BACLC,IAAK,wBACLC,MAAO,yBAETE,OAAQ,CACNL,IAAK,wBACLC,IAAK,wBACLC,IAAK,wBACLC,MAAO,0BAsBb,IA+BIlC,EA/BiB,SAAwBoD,EAAOC,EAAOC,GACzD,IAAIC,EAAS,GACTC,EAAQ,UAERF,SAA0CA,EAAQG,YAChDH,EAAQI,YAAcJ,EAAQI,WAAa,GAC7CF,EAAQ,SACRD,EAAS,SAETC,EAAQ,OACRD,EAAS,UAIb,IAAII,EAAa/B,EAAqBwB,GAEtC,GAA0B,iBAAfO,EACTJ,GAAUI,MACL,CACL,IAAIC,EApCR,SAA0BP,GACxB,OAAQA,EAAQ,KACd,KAAK,EACH,MAAO,MAET,KAAK,EACH,MAAO,MAET,KAAK,EACL,KAAK,EACH,MAAO,MAET,QACE,MAAO,QAEb,CAqBeQ,CAAiBR,QA7TXS,IA+TAH,EA/TR5B,IAgUPwB,GAAUI,EAAWC,GAAMG,QAAQ,YAAaC,OAAOX,IAEvDE,GAAUI,EAAWH,GAAOI,GAAMG,QAAQ,YAAaC,OAAOX,GAElE,CAEA,OAAOE,CACT,EAGA7D,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,gBCjVzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAClB,IAAIuE,EAAuB,CACzBC,SAAU,SAAkBC,GAG1B,OAFUA,EAAKC,aAGb,KAAK,EACH,MAAO,0BAET,KAAK,EACH,MAAO,wBAET,KAAK,EACH,MAAO,yBAET,QACE,MAAO,yBAEb,EACAC,UAAW,gBACXC,MAAO,eACPC,SAAU,eACVC,SAAU,SAAkBL,GAG1B,OAFUA,EAAKC,aAGb,KAAK,EACH,MAAO,2BAET,KAAK,EACH,MAAO,yBAET,KAAK,EACH,MAAO,0BAET,QACE,MAAO,0BAEb,EACAlC,MAAO,KAaLlC,EAViB,SAAwBoD,EAAOe,EAAMM,EAAWtE,GACnE,IAAIuE,EAAST,EAAqBb,GAElC,MAAsB,mBAAXsB,EACFA,EAAOP,GAGTO,CACT,EAGAhF,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,kBCzDzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAAS8E,EAAuB,EAAQ,QAI5C,SAASA,EAAuB/E,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAE9F,IAyGII,EA1CQ,CACVC,eAAe,EApEH0E,EAAuB,EAAQ,MAoEhB5E,SAAS,CAClC6E,aAjE4B,YAkE5BC,aAjE4B,OAkE5BC,cAAe,SAAuBnF,GACpC,OAAOoF,SAASpF,EAAO,GACzB,IAEFU,KAAK,EAAIR,EAAOE,SAAS,CACvBiF,cAtEmB,CACrBxE,YAAa,gCACbC,KAAM,qGAqEJwE,kBAAmB,OACnBC,cApEmB,CACrBC,IAAK,CAAC,OAAQ,oBAoEZC,kBAAmB,QAErBzE,SAAS,EAAId,EAAOE,SAAS,CAC3BiF,cArEuB,CACzBzE,OAAQ,WACRC,YAAa,yBACbC,KAAM,4BAmEJwE,kBAAmB,OACnBC,cAlEuB,CACzBC,IAAK,CAAC,KAAM,KAAM,KAAM,OAkEtBC,kBAAmB,MACnBN,cAAe,SAAuBO,GACpC,OAAOA,EAAQ,CACjB,IAEFxE,OAAO,EAAIhB,EAAOE,SAAS,CACzBiF,cAtEqB,CACvBzE,OAAQ,eACRC,YAAa,4EACbC,KAAM,6FAoEJwE,kBAAmB,OACnBC,cAnEqB,CACvB3E,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OACtFC,YAAa,CAAC,OAAQ,OAAQ,QAAS,OAAQ,QAAS,QAAS,QAAS,OAAQ,MAAO,MAAO,MAAO,OACvGC,KAAM,CAAC,OAAQ,OAAQ,QAAS,OAAQ,QAAS,QAAS,QAAS,OAAQ,MAAO,MAAO,MAAO,QAiE9F2E,kBAAmB,SAErBtE,KAAK,EAAIjB,EAAOE,SAAS,CACvBiF,cAlEmB,CACrBzE,OAAQ,aACRQ,MAAO,mDACPP,YAAa,mDACbC,KAAM,8DA+DJwE,kBAAmB,OACnBC,cA9DmB,CACrB3E,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,SAAU,MAAO,OACtD4E,IAAK,CAAC,MAAO,OAAQ,MAAO,OAAQ,SAAU,OAAQ,SA6DpDC,kBAAmB,QAErBpE,WAAW,EAAInB,EAAOE,SAAS,CAC7BiF,cA9DyB,CAC3BzE,OAAQ,oCACR4E,IAAK,wHA6DHF,kBAAmB,MACnBC,cA5DyB,CAC3B3E,OAAQ,CACNU,GAAI,MACJC,GAAI,MACJC,SAAU,OACVC,KAAM,OACNC,QAAS,UACTC,UAAW,MACXC,QAAS,UACTC,MAAO,YAET2D,IAAK,CACHlE,GAAI,UACJC,GAAI,UACJC,SAAU,WACVC,KAAM,WACNC,QAAS,KACTC,UAAW,UACXC,QAAS,SACTC,MAAO,aA0CP4D,kBAAmB,SAIvB1F,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,kBCtHzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAEgCE,EAF5BC,GAE4BD,EAFI,EAAQ,SAESA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,GAEvF,IAgCII,EAda,CACfmE,MAAM,EAAItE,EAAOE,SAAS,CACxBuF,QApBc,CAChBC,KAAM,mBACNC,KAAM,aACNC,OAAQ,WACR1E,MAAO,aAiBLL,aAAc,SAEhBgF,MAAM,EAAI7F,EAAOE,SAAS,CACxBuF,QAlBc,CAChBC,KAAM,gBACNC,KAAM,aACNC,OAAQ,WACR1E,MAAO,SAeLL,aAAc,SAEhBiF,UAAU,EAAI9F,EAAOE,SAAS,CAC5BuF,QAhBkB,CACpBC,KAAM,oBACNC,KAAM,oBACNC,OAAQ,oBACR1E,MAAO,qBAaLL,aAAc,UAIlBhB,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O,kBC3CzBP,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EAElB,IAAIG,EAAS8E,EAAuB,EAAQ,QAExCiB,EAAUjB,EAAuB,EAAQ,QAEzCkB,EAAUlB,EAAuB,EAAQ,QAEzCmB,EAAUnB,EAAuB,EAAQ,OAEzCoB,EAAUpB,EAAuB,EAAQ,QAE7C,SAASA,EAAuB/E,GAAO,OAAOA,GAAOA,EAAIE,WAAaF,EAAM,CAAEG,QAASH,EAAO,CAW9F,IAcII,EAdS,CACXgG,KAAM,KACNC,eAAgBpG,EAAOE,QACvBmG,WAAYN,EAAQ7F,QACpBoG,eAAgBN,EAAQ9F,QACxBqG,SAAUN,EAAQ/F,QAClBsG,MAAON,EAAQhG,QACfuD,QAAS,CACPgD,aAAc,EAGdC,sBAAuB,IAI3B7G,EAAA,QAAkBM,EAClB2B,EAAOjC,QAAUA,EAAQK,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/sl/_lib/localize/index.js", "webpack://dtale/./node_modules/date-fns/locale/sl/_lib/formatDistance/index.js", "webpack://dtale/./node_modules/date-fns/locale/sl/_lib/formatRelative/index.js", "webpack://dtale/./node_modules/date-fns/locale/sl/_lib/match/index.js", "webpack://dtale/./node_modules/date-fns/locale/sl/_lib/formatLong/index.js", "webpack://dtale/./node_modules/date-fns/locale/sl/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildLocalizeFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar eraValues = {\n  narrow: ['pr. n. št.', 'po n. št.'],\n  abbreviated: ['pr. n. št.', 'po n. št.'],\n  wide: ['pred našim štetje<PERSON>', 'po našem štetju']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1. čet.', '2. čet.', '3. čet.', '4. čet.'],\n  wide: ['1. četrtletje', '2. četrtletje', '3. četrtletje', '4. četrtletje']\n};\nvar monthValues = {\n  narrow: ['j', 'f', 'm', 'a', 'm', 'j', 'j', 'a', 's', 'o', 'n', 'd'],\n  abbreviated: ['jan.', 'feb.', 'mar.', 'apr.', 'maj', 'jun.', 'jul.', 'avg.', 'sep.', 'okt.', 'nov.', 'dec.'],\n  wide: ['januar', 'februar', 'marec', 'april', 'maj', 'junij', 'julij', 'avgust', 'september', 'oktober', 'november', 'december']\n};\nvar dayValues = {\n  narrow: ['n', 'p', 't', 's', 'č', 'p', 's'],\n  short: ['ned.', 'pon.', 'tor.', 'sre.', 'čet.', 'pet.', 'sob.'],\n  abbreviated: ['ned.', 'pon.', 'tor.', 'sre.', 'čet.', 'pet.', 'sob.'],\n  wide: ['nedelja', 'ponedeljek', 'torek', 'sreda', 'četrtek', 'petek', 'sobota']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'd',\n    pm: 'p',\n    midnight: '24.00',\n    noon: '12.00',\n    morning: 'j',\n    afternoon: 'p',\n    evening: 'v',\n    night: 'n'\n  },\n  abbreviated: {\n    am: 'dop.',\n    pm: 'pop.',\n    midnight: 'poln.',\n    noon: 'pold.',\n    morning: 'jut.',\n    afternoon: 'pop.',\n    evening: 'več.',\n    night: 'noč'\n  },\n  wide: {\n    am: 'dop.',\n    pm: 'pop.',\n    midnight: 'polnoč',\n    noon: 'poldne',\n    morning: 'jutro',\n    afternoon: 'popoldne',\n    evening: 'večer',\n    night: 'noč'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'd',\n    pm: 'p',\n    midnight: '24.00',\n    noon: '12.00',\n    morning: 'zj',\n    afternoon: 'p',\n    evening: 'zv',\n    night: 'po'\n  },\n  abbreviated: {\n    am: 'dop.',\n    pm: 'pop.',\n    midnight: 'opoln.',\n    noon: 'opold.',\n    morning: 'zjut.',\n    afternoon: 'pop.',\n    evening: 'zveč.',\n    night: 'ponoči'\n  },\n  wide: {\n    am: 'dop.',\n    pm: 'pop.',\n    midnight: 'opolnoči',\n    noon: 'opoldne',\n    morning: 'zjutraj',\n    afternoon: 'popoldan',\n    evening: 'zvečer',\n    night: 'ponoči'\n  }\n};\n\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\n\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: (0, _index.default)({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: (0, _index.default)({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: (0, _index.default)({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: (0, _index.default)({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nvar _default = localize;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nfunction isPluralType(val) {\n  return val.one !== undefined;\n}\n\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    present: {\n      one: 'manj kot {{count}} sekunda',\n      two: 'manj kot {{count}} sekundi',\n      few: 'manj kot {{count}} sekunde',\n      other: 'manj kot {{count}} sekund'\n    },\n    past: {\n      one: 'manj kot {{count}} sekundo',\n      two: 'manj kot {{count}} sekundama',\n      few: 'manj kot {{count}} sekundami',\n      other: 'manj kot {{count}} sekundami'\n    },\n    future: {\n      one: 'manj kot {{count}} sekundo',\n      two: 'manj kot {{count}} sekundi',\n      few: 'manj kot {{count}} sekunde',\n      other: 'manj kot {{count}} sekund'\n    }\n  },\n  xSeconds: {\n    present: {\n      one: '{{count}} sekunda',\n      two: '{{count}} sekundi',\n      few: '{{count}} sekunde',\n      other: '{{count}} sekund'\n    },\n    past: {\n      one: '{{count}} sekundo',\n      two: '{{count}} sekundama',\n      few: '{{count}} sekundami',\n      other: '{{count}} sekundami'\n    },\n    future: {\n      one: '{{count}} sekundo',\n      two: '{{count}} sekundi',\n      few: '{{count}} sekunde',\n      other: '{{count}} sekund'\n    }\n  },\n  halfAMinute: 'pol minute',\n  lessThanXMinutes: {\n    present: {\n      one: 'manj kot {{count}} minuta',\n      two: 'manj kot {{count}} minuti',\n      few: 'manj kot {{count}} minute',\n      other: 'manj kot {{count}} minut'\n    },\n    past: {\n      one: 'manj kot {{count}} minuto',\n      two: 'manj kot {{count}} minutama',\n      few: 'manj kot {{count}} minutami',\n      other: 'manj kot {{count}} minutami'\n    },\n    future: {\n      one: 'manj kot {{count}} minuto',\n      two: 'manj kot {{count}} minuti',\n      few: 'manj kot {{count}} minute',\n      other: 'manj kot {{count}} minut'\n    }\n  },\n  xMinutes: {\n    present: {\n      one: '{{count}} minuta',\n      two: '{{count}} minuti',\n      few: '{{count}} minute',\n      other: '{{count}} minut'\n    },\n    past: {\n      one: '{{count}} minuto',\n      two: '{{count}} minutama',\n      few: '{{count}} minutami',\n      other: '{{count}} minutami'\n    },\n    future: {\n      one: '{{count}} minuto',\n      two: '{{count}} minuti',\n      few: '{{count}} minute',\n      other: '{{count}} minut'\n    }\n  },\n  aboutXHours: {\n    present: {\n      one: 'približno {{count}} ura',\n      two: 'približno {{count}} uri',\n      few: 'približno {{count}} ure',\n      other: 'približno {{count}} ur'\n    },\n    past: {\n      one: 'približno {{count}} uro',\n      two: 'približno {{count}} urama',\n      few: 'približno {{count}} urami',\n      other: 'približno {{count}} urami'\n    },\n    future: {\n      one: 'približno {{count}} uro',\n      two: 'približno {{count}} uri',\n      few: 'približno {{count}} ure',\n      other: 'približno {{count}} ur'\n    }\n  },\n  xHours: {\n    present: {\n      one: '{{count}} ura',\n      two: '{{count}} uri',\n      few: '{{count}} ure',\n      other: '{{count}} ur'\n    },\n    past: {\n      one: '{{count}} uro',\n      two: '{{count}} urama',\n      few: '{{count}} urami',\n      other: '{{count}} urami'\n    },\n    future: {\n      one: '{{count}} uro',\n      two: '{{count}} uri',\n      few: '{{count}} ure',\n      other: '{{count}} ur'\n    }\n  },\n  xDays: {\n    present: {\n      one: '{{count}} dan',\n      two: '{{count}} dni',\n      few: '{{count}} dni',\n      other: '{{count}} dni'\n    },\n    past: {\n      one: '{{count}} dnem',\n      two: '{{count}} dnevoma',\n      few: '{{count}} dnevi',\n      other: '{{count}} dnevi'\n    },\n    future: {\n      one: '{{count}} dan',\n      two: '{{count}} dni',\n      few: '{{count}} dni',\n      other: '{{count}} dni'\n    }\n  },\n  // no tenses for weeks?\n  aboutXWeeks: {\n    one: 'približno {{count}} teden',\n    two: 'približno {{count}} tedna',\n    few: 'približno {{count}} tedne',\n    other: 'približno {{count}} tednov'\n  },\n  // no tenses for weeks?\n  xWeeks: {\n    one: '{{count}} teden',\n    two: '{{count}} tedna',\n    few: '{{count}} tedne',\n    other: '{{count}} tednov'\n  },\n  aboutXMonths: {\n    present: {\n      one: 'približno {{count}} mesec',\n      two: 'približno {{count}} meseca',\n      few: 'približno {{count}} mesece',\n      other: 'približno {{count}} mesecev'\n    },\n    past: {\n      one: 'približno {{count}} mesecem',\n      two: 'približno {{count}} mesecema',\n      few: 'približno {{count}} meseci',\n      other: 'približno {{count}} meseci'\n    },\n    future: {\n      one: 'približno {{count}} mesec',\n      two: 'približno {{count}} meseca',\n      few: 'približno {{count}} mesece',\n      other: 'približno {{count}} mesecev'\n    }\n  },\n  xMonths: {\n    present: {\n      one: '{{count}} mesec',\n      two: '{{count}} meseca',\n      few: '{{count}} meseci',\n      other: '{{count}} mesecev'\n    },\n    past: {\n      one: '{{count}} mesecem',\n      two: '{{count}} mesecema',\n      few: '{{count}} meseci',\n      other: '{{count}} meseci'\n    },\n    future: {\n      one: '{{count}} mesec',\n      two: '{{count}} meseca',\n      few: '{{count}} mesece',\n      other: '{{count}} mesecev'\n    }\n  },\n  aboutXYears: {\n    present: {\n      one: 'približno {{count}} leto',\n      two: 'približno {{count}} leti',\n      few: 'približno {{count}} leta',\n      other: 'približno {{count}} let'\n    },\n    past: {\n      one: 'približno {{count}} letom',\n      two: 'približno {{count}} letoma',\n      few: 'približno {{count}} leti',\n      other: 'približno {{count}} leti'\n    },\n    future: {\n      one: 'približno {{count}} leto',\n      two: 'približno {{count}} leti',\n      few: 'približno {{count}} leta',\n      other: 'približno {{count}} let'\n    }\n  },\n  xYears: {\n    present: {\n      one: '{{count}} leto',\n      two: '{{count}} leti',\n      few: '{{count}} leta',\n      other: '{{count}} let'\n    },\n    past: {\n      one: '{{count}} letom',\n      two: '{{count}} letoma',\n      few: '{{count}} leti',\n      other: '{{count}} leti'\n    },\n    future: {\n      one: '{{count}} leto',\n      two: '{{count}} leti',\n      few: '{{count}} leta',\n      other: '{{count}} let'\n    }\n  },\n  overXYears: {\n    present: {\n      one: 'več kot {{count}} leto',\n      two: 'več kot {{count}} leti',\n      few: 'več kot {{count}} leta',\n      other: 'več kot {{count}} let'\n    },\n    past: {\n      one: 'več kot {{count}} letom',\n      two: 'več kot {{count}} letoma',\n      few: 'več kot {{count}} leti',\n      other: 'več kot {{count}} leti'\n    },\n    future: {\n      one: 'več kot {{count}} leto',\n      two: 'več kot {{count}} leti',\n      few: 'več kot {{count}} leta',\n      other: 'več kot {{count}} let'\n    }\n  },\n  almostXYears: {\n    present: {\n      one: 'skoraj {{count}} leto',\n      two: 'skoraj {{count}} leti',\n      few: 'skoraj {{count}} leta',\n      other: 'skoraj {{count}} let'\n    },\n    past: {\n      one: 'skoraj {{count}} letom',\n      two: 'skoraj {{count}} letoma',\n      few: 'skoraj {{count}} leti',\n      other: 'skoraj {{count}} leti'\n    },\n    future: {\n      one: 'skoraj {{count}} leto',\n      two: 'skoraj {{count}} leti',\n      few: 'skoraj {{count}} leta',\n      other: 'skoraj {{count}} let'\n    }\n  }\n};\n\nfunction getFormFromCount(count) {\n  switch (count % 100) {\n    case 1:\n      return 'one';\n\n    case 2:\n      return 'two';\n\n    case 3:\n    case 4:\n      return 'few';\n\n    default:\n      return 'other';\n  }\n}\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var result = '';\n  var tense = 'present';\n\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      tense = 'future';\n      result = 'čez ';\n    } else {\n      tense = 'past';\n      result = 'pred ';\n    }\n  }\n\n  var tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === 'string') {\n    result += tokenValue;\n  } else {\n    var form = getFormFromCount(count);\n\n    if (isPluralType(tokenValue)) {\n      result += tokenValue[form].replace('{{count}}', String(count));\n    } else {\n      result += tokenValue[tense][form].replace('{{count}}', String(count));\n    }\n  }\n\n  return result;\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    var day = date.getUTCDay();\n\n    switch (day) {\n      case 0:\n        return \"'prej<PERSON><PERSON><PERSON> nedeljo ob' p\";\n\n      case 3:\n        return \"'prejšnjo sredo ob' p\";\n\n      case 6:\n        return \"'prejšnjo soboto ob' p\";\n\n      default:\n        return \"'prejšnji' EEEE 'ob' p\";\n    }\n  },\n  yesterday: \"'včeraj ob' p\",\n  today: \"'danes ob' p\",\n  tomorrow: \"'jutri ob' p\",\n  nextWeek: function nextWeek(date) {\n    var day = date.getUTCDay();\n\n    switch (day) {\n      case 0:\n        return \"'naslednjo nedeljo ob' p\";\n\n      case 3:\n        return \"'naslednjo sredo ob' p\";\n\n      case 6:\n        return \"'naslednjo soboto ob' p\";\n\n      default:\n        return \"'naslednji' EEEE 'ob' p\";\n    }\n  },\n  other: 'P'\n};\n\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n\n  if (typeof format === 'function') {\n    return format(date);\n  }\n\n  return format;\n};\n\nvar _default = formatRelative;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildMatchFn/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"../../../_lib/buildMatchPatternFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar matchOrdinalNumberPattern = /^(\\d+)\\./i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  abbreviated: /^(pr\\. n\\. št\\.|po n\\. št\\.)/i,\n  wide: /^(pred Kristusom|pred na[sš]im [sš]tetjem|po Kristusu|po na[sš]em [sš]tetju|na[sš]ega [sš]tetja)/i\n};\nvar parseEraPatterns = {\n  any: [/^pr/i, /^(po|na[sš]em)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^[1234]\\.\\s?[čc]et\\.?/i,\n  wide: /^[1234]\\. [čc]etrtletje/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan\\.|feb\\.|mar\\.|apr\\.|maj|jun\\.|jul\\.|avg\\.|sep\\.|okt\\.|nov\\.|dec\\.)/i,\n  wide: /^(januar|februar|marec|april|maj|junij|julij|avgust|september|oktober|november|december)/i\n};\nvar parseMonthPatterns = {\n  narrow: [/^j/i, /^f/i, /^m/i, /^a/i, /^m/i, /^j/i, /^j/i, /^a/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  abbreviated: [/^ja/i, /^fe/i, /^mar/i, /^ap/i, /^maj/i, /^jun/i, /^jul/i, /^av/i, /^s/i, /^o/i, /^n/i, /^d/i],\n  wide: [/^ja/i, /^fe/i, /^mar/i, /^ap/i, /^maj/i, /^jun/i, /^jul/i, /^av/i, /^s/i, /^o/i, /^n/i, /^d/i]\n};\nvar matchDayPatterns = {\n  narrow: /^[nptsčc]/i,\n  short: /^(ned\\.|pon\\.|tor\\.|sre\\.|[cč]et\\.|pet\\.|sob\\.)/i,\n  abbreviated: /^(ned\\.|pon\\.|tor\\.|sre\\.|[cč]et\\.|pet\\.|sob\\.)/i,\n  wide: /^(nedelja|ponedeljek|torek|sreda|[cč]etrtek|petek|sobota)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^n/i, /^p/i, /^t/i, /^s/i, /^[cč]/i, /^p/i, /^s/i],\n  any: [/^n/i, /^po/i, /^t/i, /^sr/i, /^[cč]/i, /^pe/i, /^so/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(d|po?|z?v|n|z?j|24\\.00|12\\.00)/i,\n  any: /^(dop\\.|pop\\.|o?poln(\\.|o[cč]i?)|o?pold(\\.|ne)|z?ve[cč](\\.|er)|(po)?no[cč]i?|popold(ne|an)|jut(\\.|ro)|zjut(\\.|raj))/i\n};\nvar parseDayPeriodPatterns = {\n  narrow: {\n    am: /^d/i,\n    pm: /^p/i,\n    midnight: /^24/i,\n    noon: /^12/i,\n    morning: /^(z?j)/i,\n    afternoon: /^p/i,\n    evening: /^(z?v)/i,\n    night: /^(n|po)/i\n  },\n  any: {\n    am: /^dop\\./i,\n    pm: /^pop\\./i,\n    midnight: /^o?poln/i,\n    noon: /^o?pold/i,\n    morning: /j/i,\n    afternoon: /^pop\\./i,\n    evening: /^z?ve/i,\n    night: /(po)?no/i\n  }\n};\nvar match = {\n  ordinalNumber: (0, _index2.default)({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {\n      return parseInt(value, 10);\n    }\n  }),\n  era: (0, _index.default)({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: 'any'\n  }),\n  quarter: (0, _index.default)({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: 'any',\n    valueCallback: function valueCallback(index) {\n      return index + 1;\n    }\n  }),\n  month: (0, _index.default)({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: 'wide'\n  }),\n  day: (0, _index.default)({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: 'wide',\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: 'any'\n  }),\n  dayPeriod: (0, _index.default)({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: 'any',\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: 'any'\n  })\n};\nvar _default = match;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"../../../_lib/buildFormatLongFn/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar dateFormats = {\n  full: 'EEEE, dd. MMMM y',\n  long: 'dd. MMMM y',\n  medium: 'd. MMM y',\n  short: 'd. MM. yy'\n};\nvar timeFormats = {\n  full: 'HH:mm:ss zzzz',\n  long: 'HH:mm:ss z',\n  medium: 'HH:mm:ss',\n  short: 'HH:mm'\n};\nvar dateTimeFormats = {\n  full: '{{date}} {{time}}',\n  long: '{{date}} {{time}}',\n  medium: '{{date}} {{time}}',\n  short: '{{date}} {{time}}'\n};\nvar formatLong = {\n  date: (0, _index.default)({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: (0, _index.default)({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: (0, _index.default)({\n    formats: dateTimeFormats,\n    defaultWidth: 'full'\n  })\n};\nvar _default = formatLong;\nexports.default = _default;\nmodule.exports = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nvar _index = _interopRequireDefault(require(\"./_lib/formatDistance/index.js\"));\n\nvar _index2 = _interopRequireDefault(require(\"./_lib/formatLong/index.js\"));\n\nvar _index3 = _interopRequireDefault(require(\"./_lib/formatRelative/index.js\"));\n\nvar _index4 = _interopRequireDefault(require(\"./_lib/localize/index.js\"));\n\nvar _index5 = _interopRequireDefault(require(\"./_lib/match/index.js\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @type {Locale}\n * @category Locales\n * @summary Slovenian locale.\n * @language Slovenian\n * @iso-639-2 slv\n * <AUTHOR> [@Neoglyph]{@link https://github.com/Neoglyph}\n * <AUTHOR> [@mzgajner]{@link https://github.com/mzgajner}\n */\nvar locale = {\n  code: 'sl',\n  formatDistance: _index.default,\n  formatLong: _index2.default,\n  formatRelative: _index3.default,\n  localize: _index4.default,\n  match: _index5.default,\n  options: {\n    weekStartsOn: 1\n    /* Monday */\n    ,\n    firstWeekContainsDate: 1\n  }\n};\nvar _default = locale;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["Object", "defineProperty", "exports", "value", "obj", "_index", "__esModule", "default", "_default", "ordinalNumber", "dirtyNumber", "_options", "Number", "era", "values", "narrow", "abbreviated", "wide", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "short", "<PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingValues", "defaultFormattingWidth", "module", "formatDistanceLocale", "lessThanXSeconds", "present", "one", "two", "few", "other", "past", "future", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "token", "count", "options", "result", "tense", "addSuffix", "comparison", "tokenValue", "form", "getFormFromCount", "undefined", "replace", "String", "formatRelativeLocale", "lastWeek", "date", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "_baseDate", "format", "_interopRequireDefault", "matchPattern", "parsePattern", "valueCallback", "parseInt", "matchPatterns", "defaultMatchWidth", "parsePatterns", "any", "defaultParseWidth", "index", "formats", "full", "long", "medium", "time", "dateTime", "_index2", "_index3", "_index4", "_index5", "code", "formatDistance", "formatLong", "formatRelative", "localize", "match", "weekStartsOn", "firstWeekContainsDate"], "sourceRoot": ""}
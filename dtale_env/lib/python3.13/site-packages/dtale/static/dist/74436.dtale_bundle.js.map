{"version": 3, "file": "74436.dtale_bundle.js", "mappings": "6EA2IAA,EAAOC,QAlIP,SAAgBC,GACd,MAAMC,EAAS,CACbC,UAAW,SACXC,SAAU,CAAEH,EAAKI,kBACjBC,SAAU,CACRL,EAAKM,QAAQN,EAAKO,iBAAkB,CAClCC,QAAS,OAEXR,EAAKM,QAAQN,EAAKS,kBAAmB,CACnCD,QAAS,SAITE,EAAaV,EAAKW,sBAClBC,EAAS,CACbP,SAAU,CACRL,EAAKa,mBACLb,EAAKc,gBAGHC,EAEJ,sfAyBF,MAAO,CACLC,KAAM,SACNC,QAAS,CAAE,OACXC,SAAUH,EACVZ,SAAU,CACRH,EAAKmB,oBACLnB,EAAKoB,QACH,OACA,OACA,CACEjB,SAAU,CACR,CACED,UAAW,SACXmB,MAAO,iBAKf,CACEnB,UAAW,SACXmB,MAAO,oBACPC,IAAK,QACLnB,SAAU,CAAEH,EAAKI,mBAEnB,CAEEiB,MAAO,oDAET,CACEnB,UAAW,WACXqB,cAAe,cACfD,IAAK,OACLE,YAAY,EACZhB,QAAS,UACTL,SAAU,CACRO,EACA,CACER,UAAW,SACXmB,MAAO,KACPC,IAAK,KACLJ,SAAUH,EACVZ,SAAU,CACR,OACAH,EAAKyB,qBACLxB,EACAW,MAKR,CACEV,UAAW,QACXqB,cAAe,kBACfD,IAAK,KACLE,YAAY,EACZhB,QAAS,SACTL,SAAU,CACR,CACEoB,cAAe,sBAEjBb,IAGJ,CACEa,cAAe,YACfD,IAAK,IACLd,QAAS,OACTL,SAAU,CAAEO,IAEd,CACEa,cAAe,MACfD,IAAK,IACLnB,SAAU,CAAEO,IAEd,CACEW,MAAO,MAETpB,EACAW,GAGN,C", "sources": ["webpack://dtale/./node_modules/react-syntax-highlighter/node_modules/highlight.js/lib/languages/zephir.js"], "sourcesContent": ["/*\n Language: Zephir\n Description: Zephir, an open source, high-level language designed to ease the creation and maintainability of extensions for PHP with a focus on type and memory safety.\n Author: <PERSON><PERSON> <<EMAIL>>\n Website: https://zephir-lang.com/en\n Audit: 2020\n */\n\n/** @type LanguageFn */\nfunction zephir(hljs) {\n  const STRING = {\n    className: 'string',\n    contains: [ hljs.BACKSLASH_ESCAPE ],\n    variants: [\n      hljs.inherit(hljs.APOS_STRING_MODE, {\n        illegal: null\n      }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        illegal: null\n      })\n    ]\n  };\n  const TITLE_MODE = hljs.UNDERSCORE_TITLE_MODE;\n  const NUMBER = {\n    variants: [\n      hljs.BINARY_NUMBER_MODE,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n  const KEYWORDS =\n    // classes and objects\n    'namespace class interface use extends ' +\n    'function return ' +\n    'abstract final public protected private static deprecated ' +\n    // error handling\n    'throw try catch Exception ' +\n    // keyword-ish things their website does NOT seem to highlight (in their own snippets)\n    // 'typeof fetch in ' +\n    // operators/helpers\n    'echo empty isset instanceof unset ' +\n    // assignment/variables\n    'let var new const self ' +\n    // control\n    'require ' +\n    'if else elseif switch case default ' +\n    'do while loop for continue break ' +\n    'likely unlikely ' +\n    // magic constants\n    // https://github.com/phalcon/zephir/blob/master/Library/Expression/Constants.php\n    '__LINE__ __FILE__ __DIR__ __FUNCTION__ __CLASS__ __TRAIT__ __METHOD__ __NAMESPACE__ ' +\n    // types - https://docs.zephir-lang.com/0.12/en/types\n    'array boolean float double integer object resource string ' +\n    'char long unsigned bool int uint ulong uchar ' +\n    // built-ins\n    'true false null undefined';\n\n  return {\n    name: 'Zephir',\n    aliases: [ 'zep' ],\n    keywords: KEYWORDS,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.COMMENT(\n        /\\/\\*/,\n        /\\*\\//,\n        {\n          contains: [\n            {\n              className: 'doctag',\n              begin: /@[A-Za-z]+/\n            }\n          ]\n        }\n      ),\n      {\n        className: 'string',\n        begin: /<<<['\"]?\\w+['\"]?$/,\n        end: /^\\w+;/,\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        // swallow composed identifiers to avoid parsing them as keywords\n        begin: /(::|->)+[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*/\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function fn',\n        end: /[;{]/,\n        excludeEnd: true,\n        illegal: /\\$|\\[|%/,\n        contains: [\n          TITLE_MODE,\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: KEYWORDS,\n            contains: [\n              'self',\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRING,\n              NUMBER\n            ]\n          }\n        ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: /\\{/,\n        excludeEnd: true,\n        illegal: /[:($\"]/,\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          TITLE_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        end: /;/,\n        illegal: /[.']/,\n        contains: [ TITLE_MODE ]\n      },\n      {\n        beginKeywords: 'use',\n        end: /;/,\n        contains: [ TITLE_MODE ]\n      },\n      {\n        begin: /=>/ // No markup, just a relevance booster\n      },\n      STRING,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = zephir;\n"], "names": ["module", "exports", "hljs", "STRING", "className", "contains", "BACKSLASH_ESCAPE", "variants", "inherit", "APOS_STRING_MODE", "illegal", "QUOTE_STRING_MODE", "TITLE_MODE", "UNDERSCORE_TITLE_MODE", "NUMBER", "BINARY_NUMBER_MODE", "C_NUMBER_MODE", "KEYWORDS", "name", "aliases", "keywords", "C_LINE_COMMENT_MODE", "COMMENT", "begin", "end", "beginKeywords", "excludeEnd", "C_BLOCK_COMMENT_MODE"], "sourceRoot": ""}
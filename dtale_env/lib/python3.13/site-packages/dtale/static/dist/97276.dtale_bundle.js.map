{"version": 3, "file": "97276.dtale_bundle.js", "mappings": "8FAoBA,SAASA,EAAWC,EAAQC,EAAOC,GACjC,IAAIC,EAdN,SAAyBH,EAAQC,GAC/B,OAAc,IAAVA,GAAeD,EAAOI,IACjBJ,EAAOI,IAGZH,GAAS,GAAKA,GAAS,GAAKD,EAAOK,QAC9BL,EAAOK,QAITL,EAAOM,KAChB,CAGcC,CAAgBP,EAAQC,GAEpC,OADgBE,EAAMD,GACLM,QAAQ,YAAaC,OAAOR,GAC/C,CASA,SAASS,EAAkBC,GACzB,IAAIC,EAAc,GAUlB,MARoB,WAAhBD,IACFC,EAAc,UAGI,UAAhBD,IACFC,EAAc,aAGTA,EAAYC,OAAS,EAAID,EAAc,IAAM,EACtD,CAEA,SAASE,EAAkBH,GACzB,IAAIC,EAAc,GAUlB,MARoB,aAAhBD,IACFC,EAAc,aAGI,SAAhBD,IACFC,EAAc,YAGTA,EAAYC,OAAS,EAAID,EAAc,IAAM,EACtD,CAzDAG,OAAOC,eAAeC,EAAS,aAAc,CAC3CC,OAAO,IAETD,EAAA,aAAkB,EA4DlB,IAAIE,EAAuB,CACzBC,SAAU,CACRhB,IAAK,CACHiB,QAAS,UACTC,KAAM,WACNC,OAAQ,WAEVlB,QAAS,CACPgB,QAAS,oBACTC,KAAM,sBACNC,OAAQ,qBAEVjB,MAAO,CACLe,QAAS,mBACTC,KAAM,sBACNC,OAAQ,qBAGZC,YAAa,CACXlB,MAAO,CACLe,QAAS,aACTC,KAAM,cACNC,OAAQ,eAGZE,SAAU,CACRrB,IAAK,CACHiB,QAAS,SACTC,KAAM,UACNC,OAAQ,UAEVlB,QAAS,CACPgB,QAAS,mBACTC,KAAM,qBACNC,OAAQ,oBAEVjB,MAAO,CACLe,QAAS,kBACTC,KAAM,qBACNC,OAAQ,oBAGZG,OAAQ,CACNtB,IAAK,CACHiB,QAAS,SACTC,KAAM,UACNC,OAAQ,UAEVlB,QAAS,CACPgB,QAAS,mBACTC,KAAM,qBACNC,OAAQ,oBAEVjB,MAAO,CACLe,QAAS,kBACTC,KAAM,qBACNC,OAAQ,oBAGZI,MAAO,CACLvB,IAAK,CACHiB,QAAS,MACTC,KAAM,OACNC,OAAQ,OAEVlB,QAAS,CACPgB,QAAS,gBACTC,KAAM,kBACNC,OAAQ,iBAEVjB,MAAO,CACLe,QAAS,gBACTC,KAAM,kBACNC,OAAQ,kBAGZK,OAAQ,CACNxB,IAAK,CACHiB,QAAS,SACTC,KAAM,UACNC,OAAQ,UAEVlB,QAAS,CACPgB,QAAS,mBACTC,KAAM,qBACNC,OAAQ,oBAEVjB,MAAO,CACLe,QAAS,oBACTC,KAAM,qBACNC,OAAQ,sBAGZM,QAAS,CACPzB,IAAK,CACHiB,QAAS,SACTC,KAAM,WACNC,OAAQ,UAEVlB,QAAS,CACPgB,QAAS,oBACTC,KAAM,qBACNC,OAAQ,qBAEVjB,MAAO,CACLe,QAAS,qBACTC,KAAM,qBACNC,OAAQ,uBAGZO,OAAQ,CACN1B,IAAK,CACHiB,QAAS,MACTC,KAAM,QACNC,OAAQ,OAEVlB,QAAS,CACPgB,QAAS,iBACTC,KAAM,kBACNC,OAAQ,kBAEVjB,MAAO,CACLe,QAAS,kBACTC,KAAM,kBACNC,OAAQ,qBAqBVQ,EAhBiB,SAAwBC,EAAO/B,EAAOgC,GACzD,IAtI4BC,EAsIxBvB,EAzKN,SAA4BqB,GAI1B,MAHa,CAAC,WAAY,QAAS,OAAQ,UAAUG,OAAO,SAAUxB,GACpE,QAASqB,EAAMI,MAAM,IAAIC,OAAO,IAAM1B,GACxC,GACc,EAChB,CAoKoB2B,CAAmBN,IAAU,GAC3CO,GAvIwBL,EAuIGF,EAAMQ,UAAU7B,EAAYE,SAtI7C4B,OAAO,GAAGC,cAAgBR,EAAOS,MAAM,GAuIjD3C,EAASmB,EAAqBoB,GAElC,OAAMN,SAA0CA,EAAQW,UAIpDX,EAAQY,YAAcZ,EAAQY,WAAa,EACtCnC,EAAkBC,GAAe,KAAOG,EAAkBH,GAAeZ,EAAWC,EAAQC,EAAO,UAEnGS,EAAkBC,GAAe,QAAUG,EAAkBH,GAAeZ,EAAWC,EAAQC,EAAO,QANtGS,EAAkBC,GAAeG,EAAkBH,GAAeZ,EAAWC,EAAQC,EAAO,UAQvG,EAGAgB,EAAA,QAAkBc,EAClBe,EAAO7B,QAAUA,EAAQ8B,O", "sources": ["webpack://dtale/./node_modules/date-fns/locale/sk/_lib/formatDistance/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n\nfunction declensionGroup(scheme, count) {\n  if (count === 1 && scheme.one) {\n    return scheme.one;\n  }\n\n  if (count >= 2 && count <= 4 && scheme.twoFour) {\n    return scheme.twoFour;\n  } // if count === null || count === 0 || count >= 5\n\n\n  return scheme.other;\n}\n\nfunction declension(scheme, count, time) {\n  var group = declensionGroup(scheme, count);\n  var finalText = group[time];\n  return finalText.replace('{{count}}', String(count));\n}\n\nfunction extractPreposition(token) {\n  var result = ['lessThan', 'about', 'over', 'almost'].filter(function (preposition) {\n    return !!token.match(new RegExp('^' + preposition));\n  });\n  return result[0];\n}\n\nfunction prefixPreposition(preposition) {\n  var translation = '';\n\n  if (preposition === 'almost') {\n    translation = 'takmer';\n  }\n\n  if (preposition === 'about') {\n    translation = 'približne';\n  }\n\n  return translation.length > 0 ? translation + ' ' : '';\n}\n\nfunction suffixPreposition(preposition) {\n  var translation = '';\n\n  if (preposition === 'lessThan') {\n    translation = 'menej než';\n  }\n\n  if (preposition === 'over') {\n    translation = 'viac než';\n  }\n\n  return translation.length > 0 ? translation + ' ' : '';\n}\n\nfunction lowercaseFirstLetter(string) {\n  return string.charAt(0).toLowerCase() + string.slice(1);\n}\n\nvar formatDistanceLocale = {\n  xSeconds: {\n    one: {\n      present: 'sekunda',\n      past: 'sekundou',\n      future: 'sekundu'\n    },\n    twoFour: {\n      present: '{{count}} sekundy',\n      past: '{{count}} sekundami',\n      future: '{{count}} sekundy'\n    },\n    other: {\n      present: '{{count}} sekúnd',\n      past: '{{count}} sekundami',\n      future: '{{count}} sekúnd'\n    }\n  },\n  halfAMinute: {\n    other: {\n      present: 'pol minúty',\n      past: 'pol minútou',\n      future: 'pol minúty'\n    }\n  },\n  xMinutes: {\n    one: {\n      present: 'minúta',\n      past: 'minútou',\n      future: 'minútu'\n    },\n    twoFour: {\n      present: '{{count}} minúty',\n      past: '{{count}} minútami',\n      future: '{{count}} minúty'\n    },\n    other: {\n      present: '{{count}} minút',\n      past: '{{count}} minútami',\n      future: '{{count}} minút'\n    }\n  },\n  xHours: {\n    one: {\n      present: 'hodina',\n      past: 'hodinou',\n      future: 'hodinu'\n    },\n    twoFour: {\n      present: '{{count}} hodiny',\n      past: '{{count}} hodinami',\n      future: '{{count}} hodiny'\n    },\n    other: {\n      present: '{{count}} hodín',\n      past: '{{count}} hodinami',\n      future: '{{count}} hodín'\n    }\n  },\n  xDays: {\n    one: {\n      present: 'deň',\n      past: 'dňom',\n      future: 'deň'\n    },\n    twoFour: {\n      present: '{{count}} dni',\n      past: '{{count}} dňami',\n      future: '{{count}} dni'\n    },\n    other: {\n      present: '{{count}} dní',\n      past: '{{count}} dňami',\n      future: '{{count}} dní'\n    }\n  },\n  xWeeks: {\n    one: {\n      present: 'týždeň',\n      past: 'týždňom',\n      future: 'týždeň'\n    },\n    twoFour: {\n      present: '{{count}} týždne',\n      past: '{{count}} týždňami',\n      future: '{{count}} týždne'\n    },\n    other: {\n      present: '{{count}} týždňov',\n      past: '{{count}} týždňami',\n      future: '{{count}} týždňov'\n    }\n  },\n  xMonths: {\n    one: {\n      present: 'mesiac',\n      past: 'mesiacom',\n      future: 'mesiac'\n    },\n    twoFour: {\n      present: '{{count}} mesiace',\n      past: '{{count}} mesiacmi',\n      future: '{{count}} mesiace'\n    },\n    other: {\n      present: '{{count}} mesiacov',\n      past: '{{count}} mesiacmi',\n      future: '{{count}} mesiacov'\n    }\n  },\n  xYears: {\n    one: {\n      present: 'rok',\n      past: 'rokom',\n      future: 'rok'\n    },\n    twoFour: {\n      present: '{{count}} roky',\n      past: '{{count}} rokmi',\n      future: '{{count}} roky'\n    },\n    other: {\n      present: '{{count}} rokov',\n      past: '{{count}} rokmi',\n      future: '{{count}} rokov'\n    }\n  }\n};\n\nvar formatDistance = function formatDistance(token, count, options) {\n  var preposition = extractPreposition(token) || '';\n  var key = lowercaseFirstLetter(token.substring(preposition.length));\n  var scheme = formatDistanceLocale[key];\n\n  if (!(options !== null && options !== void 0 && options.addSuffix)) {\n    return prefixPreposition(preposition) + suffixPreposition(preposition) + declension(scheme, count, 'present');\n  }\n\n  if (options.comparison && options.comparison > 0) {\n    return prefixPreposition(preposition) + 'o ' + suffixPreposition(preposition) + declension(scheme, count, 'future');\n  } else {\n    return prefixPreposition(preposition) + 'pred ' + suffixPreposition(preposition) + declension(scheme, count, 'past');\n  }\n};\n\nvar _default = formatDistance;\nexports.default = _default;\nmodule.exports = exports.default;"], "names": ["declension", "scheme", "count", "time", "group", "one", "twoFour", "other", "declensionGroup", "replace", "String", "prefixPreposition", "preposition", "translation", "length", "suffixPreposition", "Object", "defineProperty", "exports", "value", "formatDistanceLocale", "xSeconds", "present", "past", "future", "halfAMinute", "xMinutes", "xHours", "xDays", "xWeeks", "xMonths", "xYears", "_default", "token", "options", "string", "filter", "match", "RegExp", "extractPreposition", "key", "substring", "char<PERSON>t", "toLowerCase", "slice", "addSuffix", "comparison", "module", "default"], "sourceRoot": ""}
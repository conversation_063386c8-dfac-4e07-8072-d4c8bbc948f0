#!/usr/bin/env python3
"""
Final Model Selection and Predictions
En iyi performans gösteren modeli seçip final predictions oluşturur
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Best models
from sklearn.neural_network import MLPRegressor
import lightgbm as lgb
import catboost as cb
import tensorflow as tf
from tensorflow import keras

import json

def load_model_data():
    """Model için hazır veriyi yükle"""
    print("📊 Model verisi yükleniyor...")
    
    X_train = pd.read_csv('X_train_model_ready.csv', index_col='user_session')
    X_test = pd.read_csv('X_test_model_ready.csv', index_col='user_session')
    y_train = pd.read_csv('y_train_model_ready.csv', index_col='user_session')['session_value']
    
    return X_train, X_test, y_train

def prepare_data(X_train, X_test, y_train):
    """Veri hazırlığı"""
    # Train-validation split
    X_tr, X_val, y_tr, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )
    
    # Scaling
    scaler = StandardScaler()
    X_tr_scaled = scaler.fit_transform(X_tr)
    X_val_scaled = scaler.transform(X_val)
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    return X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled, X_train_scaled, X_test_scaled, scaler

def create_tensorflow_model(input_dim):
    """En iyi TensorFlow modeli oluştur"""
    model = keras.Sequential([
        keras.layers.Dense(128, activation='relu', input_shape=(input_dim,)),
        keras.layers.Dropout(0.3),
        keras.layers.Dense(64, activation='relu'),
        keras.layers.Dropout(0.3),
        keras.layers.Dense(32, activation='relu'),
        keras.layers.Dense(1)
    ])
    
    model.compile(optimizer='adam', loss='mse', metrics=['mae'])
    return model

def train_best_models(X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled):
    """En iyi modelleri eğit"""
    print("\n🏆 EN İYİ MODELLER EĞİTİLİYOR")
    print("=" * 50)
    
    models = {}
    results = []
    
    # 1. TensorFlow NN (En iyi performans)
    print("🤖 TensorFlow NN eğitiliyor...")
    tf_model = create_tensorflow_model(X_tr_scaled.shape[1])
    early_stopping = keras.callbacks.EarlyStopping(
        monitor='val_loss', patience=15, restore_best_weights=True
    )
    
    tf_model.fit(
        X_tr_scaled, y_tr,
        validation_data=(X_val_scaled, y_val),
        epochs=200,
        batch_size=32,
        callbacks=[early_stopping],
        verbose=0
    )
    
    y_pred_tf = tf_model.predict(X_val_scaled, verbose=0).flatten()
    tf_rmse = np.sqrt(mean_squared_error(y_val, y_pred_tf))
    tf_r2 = r2_score(y_val, y_pred_tf)
    
    models['TensorFlow NN'] = tf_model
    results.append({'model': 'TensorFlow NN', 'val_rmse': tf_rmse, 'val_r2': tf_r2, 'type': 'neural'})
    print(f"✅ TensorFlow NN: Val RMSE = {tf_rmse:.3f}, Val R² = {tf_r2:.3f}")
    
    # 2. Tuned CatBoost
    print("🐱 Tuned CatBoost eğitiliyor...")
    cb_params = {
        'iterations': 409,
        'depth': 3,
        'learning_rate': 0.13892474422969375,
        'l2_leaf_reg': 1.4650278915612143,
        'border_count': 105,
        'bagging_temperature': 0.45433321801523724,
        'random_strength': 8.995621424629297,
        'random_state': 42,
        'verbose': False
    }
    
    cb_model = cb.CatBoostRegressor(**cb_params)
    cb_model.fit(X_tr, y_tr, eval_set=(X_val, y_val), early_stopping_rounds=50, verbose=False)
    
    y_pred_cb = cb_model.predict(X_val)
    cb_rmse = np.sqrt(mean_squared_error(y_val, y_pred_cb))
    cb_r2 = r2_score(y_val, y_pred_cb)
    
    models['CatBoost'] = cb_model
    results.append({'model': 'CatBoost', 'val_rmse': cb_rmse, 'val_r2': cb_r2, 'type': 'tree'})
    print(f"✅ CatBoost: Val RMSE = {cb_rmse:.3f}, Val R² = {cb_r2:.3f}")
    
    # 3. Tuned LightGBM
    print("🌟 Tuned LightGBM eğitiliyor...")
    lgb_params = {
        'n_estimators': 368,
        'max_depth': 8,
        'learning_rate': 0.15073825006256605,
        'num_leaves': 36,
        'min_child_samples': 23,
        'subsample': 0.6303948466591162,
        'colsample_bytree': 0.8659292142233562,
        'reg_alpha': 2.407917073506192,
        'reg_lambda': 3.2215530880610315,
        'random_state': 42,
        'n_jobs': -1,
        'verbose': -1
    }
    
    lgb_model = lgb.LGBMRegressor(**lgb_params)
    lgb_model.fit(X_tr, y_tr)
    
    y_pred_lgb = lgb_model.predict(X_val)
    lgb_rmse = np.sqrt(mean_squared_error(y_val, y_pred_lgb))
    lgb_r2 = r2_score(y_val, y_pred_lgb)
    
    models['LightGBM'] = lgb_model
    results.append({'model': 'LightGBM', 'val_rmse': lgb_rmse, 'val_r2': lgb_r2, 'type': 'tree'})
    print(f"✅ LightGBM: Val RMSE = {lgb_rmse:.3f}, Val R² = {lgb_r2:.3f}")
    
    # 4. Tuned MLP
    print("🧠 Tuned MLP eğitiliyor...")
    mlp_model = MLPRegressor(
        hidden_layer_sizes=(100, 50),
        alpha=0.1,
        learning_rate_init=0.01,
        max_iter=500,
        random_state=42,
        early_stopping=True,
        validation_fraction=0.1
    )
    
    mlp_model.fit(X_tr_scaled, y_tr)
    
    y_pred_mlp = mlp_model.predict(X_val_scaled)
    mlp_rmse = np.sqrt(mean_squared_error(y_val, y_pred_mlp))
    mlp_r2 = r2_score(y_val, y_pred_mlp)
    
    models['MLP'] = mlp_model
    results.append({'model': 'MLP', 'val_rmse': mlp_rmse, 'val_r2': mlp_r2, 'type': 'neural'})
    print(f"✅ MLP: Val RMSE = {mlp_rmse:.3f}, Val R² = {mlp_r2:.3f}")
    
    return models, results

def create_ensemble_predictions(models, X_test, X_test_scaled):
    """Ensemble predictions oluştur"""
    print("\n🎨 ENSEMBLE PREDICTIONS OLUŞTURULUYOR")
    print("-" * 50)
    
    # Her model için predictions
    predictions = {}
    
    # TensorFlow NN
    predictions['tensorflow'] = models['TensorFlow NN'].predict(X_test_scaled, verbose=0).flatten()
    
    # CatBoost
    predictions['catboost'] = models['CatBoost'].predict(X_test)
    
    # LightGBM
    predictions['lightgbm'] = models['LightGBM'].predict(X_test)
    
    # MLP
    predictions['mlp'] = models['MLP'].predict(X_test_scaled)
    
    # Optimal weights (blending analizinden)
    weights = {
        'tensorflow': 0.40,
        'catboost': 0.25,
        'lightgbm': 0.20,
        'mlp': 0.15
    }
    
    # Weighted ensemble
    ensemble_pred = (weights['tensorflow'] * predictions['tensorflow'] + 
                    weights['catboost'] * predictions['catboost'] + 
                    weights['lightgbm'] * predictions['lightgbm'] + 
                    weights['mlp'] * predictions['mlp'])
    
    print(f"✅ Ensemble weights: {weights}")
    
    return ensemble_pred, predictions

def create_submission_files(X_test, ensemble_pred, predictions):
    """Submission dosyalarını oluştur"""
    print("\n📁 SUBMISSION DOSYALARI OLUŞTURULUYOR")
    print("-" * 50)
    
    # Sample submission formatını yükle
    sample_submission = pd.read_csv('sample_submission.csv')
    
    # Ensemble submission
    ensemble_submission = sample_submission.copy()
    ensemble_submission['session_value'] = ensemble_pred
    ensemble_submission.to_csv('ensemble_submission.csv', index=False)
    print("✅ ensemble_submission.csv oluşturuldu")
    
    # Individual model submissions
    for model_name, pred in predictions.items():
        individual_submission = sample_submission.copy()
        individual_submission['session_value'] = pred
        individual_submission.to_csv(f'{model_name}_submission.csv', index=False)
        print(f"✅ {model_name}_submission.csv oluşturuldu")
    
    # Submission summary
    submission_summary = {
        'ensemble_mean': float(ensemble_pred.mean()),
        'ensemble_std': float(ensemble_pred.std()),
        'ensemble_min': float(ensemble_pred.min()),
        'ensemble_max': float(ensemble_pred.max()),
        'model_predictions': {
            model: {
                'mean': float(pred.mean()),
                'std': float(pred.std()),
                'min': float(pred.min()),
                'max': float(pred.max())
            } for model, pred in predictions.items()
        }
    }
    
    with open('submission_summary.json', 'w') as f:
        json.dump(submission_summary, f, indent=2)
    
    print("✅ submission_summary.json oluşturuldu")
    
    return ensemble_submission

def main():
    """Ana fonksiyon"""
    print("🚀 FINAL MODEL SELECTION & PREDICTIONS")
    print("=" * 60)
    
    # Veri yükleme
    X_train, X_test, y_train = load_model_data()
    X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled, X_train_scaled, X_test_scaled, scaler = prepare_data(
        X_train, X_test, y_train
    )
    
    # En iyi modelleri eğit
    models, results = train_best_models(X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled)
    
    # Sonuçları özetle
    print("\n📊 FINAL MODEL COMPARISON")
    print("=" * 50)
    
    df_results = pd.DataFrame(results).sort_values('val_rmse')
    
    print(f"{'Model':<15} {'Val RMSE':<10} {'Val R²':<8} {'Type':<8}")
    print("-" * 45)
    for _, row in df_results.iterrows():
        print(f"{row['model']:<15} {row['val_rmse']:<10.3f} {row['val_r2']:<8.3f} {row['type']:<8}")
    
    # En iyi model
    best_model = df_results.iloc[0]
    print(f"\n🏆 En iyi model: {best_model['model']}")
    print(f"   Val RMSE: {best_model['val_rmse']:.3f}")
    print(f"   Val R²: {best_model['val_r2']:.3f}")
    
    # Full dataset ile final training
    print(f"\n🔄 Full dataset ile final training...")
    
    # TensorFlow NN final training
    final_tf_model = create_tensorflow_model(X_train_scaled.shape[1])
    early_stopping = keras.callbacks.EarlyStopping(monitor='loss', patience=10, restore_best_weights=True)
    final_tf_model.fit(X_train_scaled, y_train, epochs=200, batch_size=32, callbacks=[early_stopping], verbose=0)
    models['TensorFlow NN'] = final_tf_model
    
    # CatBoost final training
    cb_params = {
        'iterations': 409, 'depth': 3, 'learning_rate': 0.13892474422969375,
        'l2_leaf_reg': 1.4650278915612143, 'border_count': 105,
        'bagging_temperature': 0.45433321801523724, 'random_strength': 8.995621424629297,
        'random_state': 42, 'verbose': False
    }
    final_cb_model = cb.CatBoostRegressor(**cb_params)
    final_cb_model.fit(X_train, y_train)
    models['CatBoost'] = final_cb_model
    
    # LightGBM final training
    lgb_params = {
        'n_estimators': 368, 'max_depth': 8, 'learning_rate': 0.15073825006256605,
        'num_leaves': 36, 'min_child_samples': 23, 'subsample': 0.6303948466591162,
        'colsample_bytree': 0.8659292142233562, 'reg_alpha': 2.407917073506192,
        'reg_lambda': 3.2215530880610315, 'random_state': 42, 'n_jobs': -1, 'verbose': -1
    }
    final_lgb_model = lgb.LGBMRegressor(**lgb_params)
    final_lgb_model.fit(X_train, y_train)
    models['LightGBM'] = final_lgb_model
    
    # MLP final training
    final_mlp_model = MLPRegressor(
        hidden_layer_sizes=(100, 50), alpha=0.1, learning_rate_init=0.01,
        max_iter=500, random_state=42, early_stopping=True, validation_fraction=0.1
    )
    final_mlp_model.fit(X_train_scaled, y_train)
    models['MLP'] = final_mlp_model
    
    print("✅ Final training tamamlandı")
    
    # Ensemble predictions
    ensemble_pred, predictions = create_ensemble_predictions(models, X_test, X_test_scaled)
    
    # Submission dosyaları
    ensemble_submission = create_submission_files(X_test, ensemble_pred, predictions)
    
    print(f"\n✨ FINAL PREDICTIONS TAMAMLANDI!")
    print(f"📁 Ana submission dosyası: ensemble_submission.csv")
    print(f"📊 Ensemble ortalama: {ensemble_pred.mean():.2f}")
    print(f"📊 Ensemble std: {ensemble_pred.std():.2f}")
    print(f"📊 Ensemble aralığı: [{ensemble_pred.min():.2f}, {ensemble_pred.max():.2f}]")

if __name__ == "__main__":
    main()

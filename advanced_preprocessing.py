#!/usr/bin/env python3
"""
Advanced Preprocessing Pipeline
Kapsamlı veri ön işleme: missing values, scaling, encoding
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, OrdinalEncoder, LabelEncoder
from sklearn.impute import SimpleImputer
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
import warnings
warnings.filterwarnings('ignore')

def load_preprocessed_data():
    """Preprocessed veriyi yükle"""
    print("📊 Preprocessed veri yükleniyor...")
    
    train_df = pd.read_csv('train_preprocessed.csv', index_col='user_session')
    test_df = pd.read_csv('test_preprocessed.csv', index_col='user_session')
    
    print(f"✅ Train: {train_df.shape}")
    print(f"✅ Test: {test_df.shape}")
    
    return train_df, test_df

def analyze_data_types(train_df, test_df):
    """Veri tiplerini analiz et"""
    print("\n🔍 VERİ TİPİ ANALİZİ")
    print("=" * 50)
    
    # Train veri tipleri
    print("📊 Train Veri Tipleri:")
    print(train_df.dtypes.value_counts())
    
    # Missing values analizi
    print(f"\n🚨 Missing Values Analizi:")
    train_missing = train_df.isnull().sum()
    test_missing = test_df.isnull().sum()
    
    print("Train Missing Values:")
    if train_missing.sum() > 0:
        for col, count in train_missing[train_missing > 0].items():
            print(f"   {col}: {count} ({count/len(train_df)*100:.1f}%)")
    else:
        print("   ✅ Missing value yok")
    
    print("Test Missing Values:")
    if test_missing.sum() > 0:
        for col, count in test_missing[test_missing > 0].items():
            print(f"   {col}: {count} ({count/len(test_df)*100:.1f}%)")
    else:
        print("   ✅ Missing value yok")
    
    return train_missing, test_missing

def identify_column_types(df):
    """Sütun tiplerini belirle"""
    print("\n🏷️ SÜTUN TİPLERİNİ BELİRLEME")
    print("-" * 40)
    
    # Target'ı çıkar
    feature_df = df.drop('session_value', axis=1) if 'session_value' in df.columns else df
    
    # Sütun tiplerini kategorize et
    numerical_cols = []
    binary_cols = []
    ordinal_cols = []
    categorical_cols = []
    
    for col in feature_df.columns:
        unique_values = feature_df[col].nunique()
        col_type = feature_df[col].dtype
        
        if col_type in ['int64', 'float64']:
            if unique_values == 2:
                # Binary sütun (0/1, True/False gibi)
                binary_cols.append(col)
            elif unique_values <= 10 and feature_df[col].min() >= 0:
                # Potansiyel ordinal (küçük sayıda kategori)
                ordinal_cols.append(col)
            else:
                # Continuous numerical
                numerical_cols.append(col)
        else:
            # Kategorik
            categorical_cols.append(col)
    
    print(f"📊 Sütun Kategorileri:")
    print(f"   Numerical: {len(numerical_cols)} sütun")
    print(f"   Binary: {len(binary_cols)} sütun")
    print(f"   Ordinal: {len(ordinal_cols)} sütun")
    print(f"   Categorical: {len(categorical_cols)} sütun")
    
    print(f"\n📋 Detaylar:")
    if numerical_cols:
        print(f"   Numerical: {numerical_cols[:5]}{'...' if len(numerical_cols) > 5 else ''}")
    if binary_cols:
        print(f"   Binary: {binary_cols}")
    if ordinal_cols:
        print(f"   Ordinal: {ordinal_cols}")
    if categorical_cols:
        print(f"   Categorical: {categorical_cols}")
    
    return numerical_cols, binary_cols, ordinal_cols, categorical_cols

def create_preprocessing_pipeline(numerical_cols, binary_cols, ordinal_cols, categorical_cols):
    """Preprocessing pipeline oluştur"""
    print("\n🔧 PREPROCESSING PIPELINE OLUŞTURULUYOR")
    print("-" * 50)
    
    transformers = []
    
    # 1. Numerical columns: Missing values (median) + StandardScaler
    if numerical_cols:
        numerical_pipeline = Pipeline([
            ('imputer', SimpleImputer(strategy='median')),
            ('scaler', StandardScaler())
        ])
        transformers.append(('numerical', numerical_pipeline, numerical_cols))
        print(f"✅ Numerical pipeline: {len(numerical_cols)} sütun")
        print(f"   - Missing values: median ile doldurulacak")
        print(f"   - Scaling: StandardScaler uygulanacak")
    
    # 2. Binary columns: Missing values (most_frequent) + keep as is
    if binary_cols:
        binary_pipeline = Pipeline([
            ('imputer', SimpleImputer(strategy='most_frequent'))
        ])
        transformers.append(('binary', binary_pipeline, binary_cols))
        print(f"✅ Binary pipeline: {len(binary_cols)} sütun")
        print(f"   - Missing values: en sık değer ile doldurulacak")
    
    # 3. Ordinal columns: Missing values (most_frequent) + OrdinalEncoder
    if ordinal_cols:
        ordinal_pipeline = Pipeline([
            ('imputer', SimpleImputer(strategy='most_frequent')),
            ('encoder', OrdinalEncoder(handle_unknown='use_encoded_value', unknown_value=-1))
        ])
        transformers.append(('ordinal', ordinal_pipeline, ordinal_cols))
        print(f"✅ Ordinal pipeline: {len(ordinal_cols)} sütun")
        print(f"   - Missing values: en sık değer ile doldurulacak")
        print(f"   - Encoding: OrdinalEncoder uygulanacak")
    
    # 4. Categorical columns: Missing values (most_frequent) + One-Hot Encoding
    if categorical_cols:
        from sklearn.preprocessing import OneHotEncoder
        categorical_pipeline = Pipeline([
            ('imputer', SimpleImputer(strategy='most_frequent')),
            ('encoder', OneHotEncoder(handle_unknown='ignore', sparse_output=False))
        ])
        transformers.append(('categorical', categorical_pipeline, categorical_cols))
        print(f"✅ Categorical pipeline: {len(categorical_cols)} sütun")
        print(f"   - Missing values: en sık değer ile doldurulacak")
        print(f"   - Encoding: One-Hot Encoding uygulanacak")
    
    # ColumnTransformer oluştur
    preprocessor = ColumnTransformer(
        transformers=transformers,
        remainder='passthrough'  # Diğer sütunları olduğu gibi bırak
    )
    
    return preprocessor

def apply_preprocessing(train_df, test_df, preprocessor):
    """Preprocessing'i uygula"""
    print("\n⚙️ PREPROCESSING UYGULANYOR")
    print("-" * 40)
    
    # Target'ı ayır
    if 'session_value' in train_df.columns:
        X_train = train_df.drop('session_value', axis=1)
        y_train = train_df['session_value']
    else:
        X_train = train_df
        y_train = None
    
    X_test = test_df
    
    print(f"📊 Preprocessing öncesi:")
    print(f"   X_train: {X_train.shape}")
    print(f"   X_test: {X_test.shape}")
    
    # Preprocessing uygula
    print(f"🔄 Preprocessing uygulanıyor...")
    X_train_processed = preprocessor.fit_transform(X_train)
    X_test_processed = preprocessor.transform(X_test)
    
    print(f"📊 Preprocessing sonrası:")
    print(f"   X_train_processed: {X_train_processed.shape}")
    print(f"   X_test_processed: {X_test_processed.shape}")
    
    # Feature names oluştur
    feature_names = []
    
    # Numerical features
    if hasattr(preprocessor.named_transformers_, 'numerical'):
        numerical_cols = preprocessor.transformers_[0][2]
        feature_names.extend([f"num_{col}" for col in numerical_cols])
    
    # Binary features
    if hasattr(preprocessor.named_transformers_, 'binary'):
        binary_cols = [t[2] for t in preprocessor.transformers_ if t[0] == 'binary'][0]
        feature_names.extend([f"bin_{col}" for col in binary_cols])
    
    # Ordinal features
    if hasattr(preprocessor.named_transformers_, 'ordinal'):
        ordinal_cols = [t[2] for t in preprocessor.transformers_ if t[0] == 'ordinal'][0]
        feature_names.extend([f"ord_{col}" for col in ordinal_cols])
    
    # Categorical features (One-Hot)
    if hasattr(preprocessor.named_transformers_, 'categorical'):
        try:
            categorical_transformer = preprocessor.named_transformers_['categorical']
            if hasattr(categorical_transformer, 'named_steps'):
                encoder = categorical_transformer.named_steps['encoder']
                if hasattr(encoder, 'get_feature_names_out'):
                    categorical_cols = [t[2] for t in preprocessor.transformers_ if t[0] == 'categorical'][0]
                    cat_feature_names = encoder.get_feature_names_out(categorical_cols)
                    feature_names.extend([f"cat_{name}" for name in cat_feature_names])
        except:
            # Fallback
            n_cat_features = X_train_processed.shape[1] - len(feature_names)
            feature_names.extend([f"cat_feature_{i}" for i in range(n_cat_features)])
    
    # DataFrame'e çevir
    X_train_df = pd.DataFrame(X_train_processed, columns=feature_names, index=X_train.index)
    X_test_df = pd.DataFrame(X_test_processed, columns=feature_names, index=X_test.index)
    
    return X_train_df, X_test_df, y_train, feature_names

def validate_preprocessing(X_train_df, X_test_df, y_train):
    """Preprocessing sonuçlarını validate et"""
    print("\n✅ PREPROCESSING VALİDASYONU")
    print("-" * 40)
    
    # Missing values kontrolü
    train_missing = X_train_df.isnull().sum().sum()
    test_missing = X_test_df.isnull().sum().sum()
    
    print(f"🔍 Missing Values:")
    print(f"   Train: {train_missing} (olmalı: 0)")
    print(f"   Test: {test_missing} (olmalı: 0)")
    
    # Infinite values kontrolü
    train_inf = np.isinf(X_train_df.select_dtypes(include=[np.number])).sum().sum()
    test_inf = np.isinf(X_test_df.select_dtypes(include=[np.number])).sum().sum()
    
    print(f"🔍 Infinite Values:")
    print(f"   Train: {train_inf} (olmalı: 0)")
    print(f"   Test: {test_inf} (olmalı: 0)")
    
    # Veri tipi kontrolü
    print(f"🔍 Veri Tipleri:")
    print(f"   Train dtypes: {X_train_df.dtypes.value_counts().to_dict()}")
    
    # Scaling kontrolü (numerical features için)
    numerical_features = [col for col in X_train_df.columns if col.startswith('num_')]
    if numerical_features:
        sample_feature = numerical_features[0]
        mean_val = X_train_df[sample_feature].mean()
        std_val = X_train_df[sample_feature].std()
        print(f"🔍 Scaling Kontrolü ({sample_feature}):")
        print(f"   Mean: {mean_val:.6f} (olmalı: ~0)")
        print(f"   Std: {std_val:.6f} (olmalı: ~1)")
    
    # Target kontrolü
    if y_train is not None:
        print(f"🔍 Target:")
        print(f"   Shape: {y_train.shape}")
        print(f"   Mean: {y_train.mean():.2f}")
        print(f"   Missing: {y_train.isnull().sum()}")
    
    return train_missing == 0 and test_missing == 0 and train_inf == 0 and test_inf == 0

def save_processed_data(X_train_df, X_test_df, y_train, feature_names):
    """İşlenmiş veriyi kaydet"""
    print("\n💾 İŞLENMİŞ VERİ KAYDEDİLİYOR")
    print("-" * 40)
    
    # Train data (with target)
    if y_train is not None:
        train_final = X_train_df.copy()
        train_final['session_value'] = y_train
        train_final.to_csv('train_final_processed.csv')
        print("✅ train_final_processed.csv kaydedildi")
    
    # Test data
    X_test_df.to_csv('test_final_processed.csv')
    print("✅ test_final_processed.csv kaydedildi")
    
    # Feature names
    feature_info = pd.DataFrame({
        'feature_name': feature_names,
        'feature_type': [name.split('_')[0] for name in feature_names]
    })
    feature_info.to_csv('feature_info.csv', index=False)
    print("✅ feature_info.csv kaydedildi")
    
    print(f"\n📊 Final Dataset Info:")
    print(f"   Train: {X_train_df.shape} + target")
    print(f"   Test: {X_test_df.shape}")
    print(f"   Total features: {len(feature_names)}")
    print(f"   Feature types: {feature_info['feature_type'].value_counts().to_dict()}")

def main():
    """Ana fonksiyon"""
    print("🚀 ADVANCED PREPROCESSING PIPELINE")
    print("=" * 60)
    
    # Veri yükleme
    train_df, test_df = load_preprocessed_data()
    
    # Veri tipi analizi
    train_missing, test_missing = analyze_data_types(train_df, test_df)
    
    # Sütun tiplerini belirle
    numerical_cols, binary_cols, ordinal_cols, categorical_cols = identify_column_types(train_df)
    
    # Preprocessing pipeline oluştur
    preprocessor = create_preprocessing_pipeline(numerical_cols, binary_cols, ordinal_cols, categorical_cols)
    
    # Preprocessing uygula
    X_train_df, X_test_df, y_train, feature_names = apply_preprocessing(train_df, test_df, preprocessor)
    
    # Validasyon
    is_valid = validate_preprocessing(X_train_df, X_test_df, y_train)
    
    if is_valid:
        print("\n✅ PREPROCESSING BAŞARILI!")
        # Veriyi kaydet
        save_processed_data(X_train_df, X_test_df, y_train, feature_names)
    else:
        print("\n❌ PREPROCESSING HATALI!")
        print("Lütfen hataları kontrol edin.")
    
    print("\n✨ ADVANCED PREPROCESSING TAMAMLANDI!")
    print("🎯 Artık model eğitimine tamamen hazırsınız!")

if __name__ == "__main__":
    main()

{"meta": {"test_sets": [], "test_metrics": [], "learn_metrics": [{"best_value": "Min", "name": "RMSE"}], "launch_mode": "Train", "parameters": "", "iteration_count": 100, "learn_sets": ["learn"], "name": "experiment"}, "iterations": [{"learn": [31.74100525], "iteration": 0, "passed_time": 0.004090360515, "remaining_time": 0.404945691}, {"learn": [25.17663777], "iteration": 1, "passed_time": 0.007247464829, "remaining_time": 0.3551257766}, {"learn": [21.93513594], "iteration": 2, "passed_time": 0.010132109, "remaining_time": 0.3276048576}, {"learn": [21.45510001], "iteration": 3, "passed_time": 0.01273908466, "remaining_time": 0.3057380318}, {"learn": [21.1181127], "iteration": 4, "passed_time": 0.01537139382, "remaining_time": 0.2920564825}, {"learn": [19.47199122], "iteration": 5, "passed_time": 0.0186392072, "remaining_time": 0.2920142461}, {"learn": [19.24476589], "iteration": 6, "passed_time": 0.02191339562, "remaining_time": 0.2911351133}, {"learn": [19.05216997], "iteration": 7, "passed_time": 0.02539016873, "remaining_time": 0.2919869404}, {"learn": [18.11703064], "iteration": 8, "passed_time": 0.03529635956, "remaining_time": 0.3568854133}, {"learn": [18.00290741], "iteration": 9, "passed_time": 0.03976113923, "remaining_time": 0.3578502531}, {"learn": [17.34379534], "iteration": 10, "passed_time": 0.07192335296, "remaining_time": 0.5819253103}, {"learn": [16.70077586], "iteration": 11, "passed_time": 0.080457243, "remaining_time": 0.590019782}, {"learn": [16.63460161], "iteration": 12, "passed_time": 0.08473239641, "remaining_time": 0.5670552683}, {"learn": [16.3446637], "iteration": 13, "passed_time": 0.09086268715, "remaining_time": 0.5581565068}, {"learn": [15.97623928], "iteration": 14, "passed_time": 0.09670493431, "remaining_time": 0.5479946278}, {"learn": [15.77223242], "iteration": 15, "passed_time": 0.101178839, "remaining_time": 0.531188905}, {"learn": [15.75047722], "iteration": 16, "passed_time": 0.1048541968, "remaining_time": 0.5119351961}, {"learn": [15.57192879], "iteration": 17, "passed_time": 0.1090611831, "remaining_time": 0.4968342785}, {"learn": [15.45697002], "iteration": 18, "passed_time": 0.1129801675, "remaining_time": 0.4816522929}, {"learn": [15.28117277], "iteration": 19, "passed_time": 0.1167155673, "remaining_time": 0.4668622692}, {"learn": [15.09445505], "iteration": 20, "passed_time": 0.1216657252, "remaining_time": 0.4576948709}, {"learn": [15.01595755], "iteration": 21, "passed_time": 0.1256082931, "remaining_time": 0.4453384935}, {"learn": [14.87387213], "iteration": 22, "passed_time": 0.1300530726, "remaining_time": 0.4353950691}, {"learn": [14.85550342], "iteration": 23, "passed_time": 0.1360531541, "remaining_time": 0.4308349881}, {"learn": [14.73881122], "iteration": 24, "passed_time": 0.1408243942, "remaining_time": 0.4224731825}, {"learn": [14.62487209], "iteration": 25, "passed_time": 0.1450203387, "remaining_time": 0.4127501948}, {"learn": [14.53774912], "iteration": 26, "passed_time": 0.1483514025, "remaining_time": 0.4010982364}, {"learn": [14.46836682], "iteration": 27, "passed_time": 0.1519390097, "remaining_time": 0.3907003106}, {"learn": [14.4600437], "iteration": 28, "passed_time": 0.1555704505, "remaining_time": 0.3808793788}, {"learn": [14.45385751], "iteration": 29, "passed_time": 0.1582610934, "remaining_time": 0.3692758845}, {"learn": [14.3905691], "iteration": 30, "passed_time": 0.1616541993, "remaining_time": 0.3598109596}, {"learn": [14.33538756], "iteration": 31, "passed_time": 0.1652929318, "remaining_time": 0.35124748}, {"learn": [14.26754129], "iteration": 32, "passed_time": 0.1680311583, "remaining_time": 0.3411541699}, {"learn": [14.19494036], "iteration": 33, "passed_time": 0.1716863909, "remaining_time": 0.3332735824}, {"learn": [14.19020811], "iteration": 34, "passed_time": 0.174352742, "remaining_time": 0.3237979494}, {"learn": [14.12703564], "iteration": 35, "passed_time": 0.1772375111, "remaining_time": 0.3150889087}, {"learn": [14.1243528], "iteration": 36, "passed_time": 0.1800096962, "remaining_time": 0.3065029963}, {"learn": [14.12165934], "iteration": 37, "passed_time": 0.1823265866, "remaining_time": 0.2974802203}, {"learn": [14.04919006], "iteration": 38, "passed_time": 0.185288898, "remaining_time": 0.2898108404}, {"learn": [14.00886541], "iteration": 39, "passed_time": 0.1887240041, "remaining_time": 0.2830860062}, {"learn": [13.95519624], "iteration": 40, "passed_time": 0.1914475639, "remaining_time": 0.2754977139}, {"learn": [13.92273279], "iteration": 41, "passed_time": 0.1941585819, "remaining_time": 0.268123756}, {"learn": [13.88938664], "iteration": 42, "passed_time": 0.1968277663, "remaining_time": 0.2609112251}, {"learn": [13.84758074], "iteration": 43, "passed_time": 0.1996522434, "remaining_time": 0.2541028553}, {"learn": [13.80176312], "iteration": 44, "passed_time": 0.2030112658, "remaining_time": 0.2481248804}, {"learn": [13.75581982], "iteration": 45, "passed_time": 0.2064740388, "remaining_time": 0.2423825673}, {"learn": [13.7299969], "iteration": 46, "passed_time": 0.2093838081, "remaining_time": 0.2361136559}, {"learn": [13.69998738], "iteration": 47, "passed_time": 0.2125453291, "remaining_time": 0.2302574399}, {"learn": [13.66062675], "iteration": 48, "passed_time": 0.2152549721, "remaining_time": 0.2240408894}, {"learn": [13.63091306], "iteration": 49, "passed_time": 0.2178690728, "remaining_time": 0.2178690728}, {"learn": [13.5999371], "iteration": 50, "passed_time": 0.22146293, "remaining_time": 0.2127781093}, {"learn": [13.57010404], "iteration": 51, "passed_time": 0.2241447395, "remaining_time": 0.2069028365}, {"learn": [13.53608307], "iteration": 52, "passed_time": 0.2272050932, "remaining_time": 0.2014837619}, {"learn": [13.51219781], "iteration": 53, "passed_time": 0.2306057825, "remaining_time": 0.1964419628}, {"learn": [13.48268313], "iteration": 54, "passed_time": 0.2337535117, "remaining_time": 0.1912528732}, {"learn": [13.46021595], "iteration": 55, "passed_time": 0.2375485369, "remaining_time": 0.186645279}, {"learn": [13.42957831], "iteration": 56, "passed_time": 0.2404846815, "remaining_time": 0.1814182685}, {"learn": [13.4108423], "iteration": 57, "passed_time": 0.2431666576, "remaining_time": 0.1760862003}, {"learn": [13.38955383], "iteration": 58, "passed_time": 0.2463011368, "remaining_time": 0.1711584171}, {"learn": [13.36264855], "iteration": 59, "passed_time": 0.2491646975, "remaining_time": 0.1661097983}, {"learn": [13.34435228], "iteration": 60, "passed_time": 0.253011473, "remaining_time": 0.1617614336}, {"learn": [13.32506173], "iteration": 61, "passed_time": 0.2567864981, "remaining_time": 0.157385273}, {"learn": [13.30751048], "iteration": 62, "passed_time": 0.2605668982, "remaining_time": 0.1530313529}, {"learn": [13.29515354], "iteration": 63, "passed_time": 0.2645248829, "remaining_time": 0.1487952466}, {"learn": [13.26985006], "iteration": 64, "passed_time": 0.268203699, "remaining_time": 0.1444173764}, {"learn": [13.26851952], "iteration": 65, "passed_time": 0.2721040166, "remaining_time": 0.1401747964}, {"learn": [13.25478759], "iteration": 66, "passed_time": 0.2752791627, "remaining_time": 0.1355852592}, {"learn": [13.23219145], "iteration": 67, "passed_time": 0.2788716865, "remaining_time": 0.1312337348}, {"learn": [13.214964], "iteration": 68, "passed_time": 0.2824208351, "remaining_time": 0.126884723}, {"learn": [13.19578381], "iteration": 69, "passed_time": 0.2863901115, "remaining_time": 0.1227386192}, {"learn": [13.1801759], "iteration": 70, "passed_time": 0.2894443401, "remaining_time": 0.1182237446}, {"learn": [13.16080327], "iteration": 71, "passed_time": 0.2927318203, "remaining_time": 0.1138401523}, {"learn": [13.14437529], "iteration": 72, "passed_time": 0.2962417186, "remaining_time": 0.1095688548}, {"learn": [13.12858451], "iteration": 73, "passed_time": 0.2994999903, "remaining_time": 0.1052297263}, {"learn": [13.10635826], "iteration": 74, "passed_time": 0.303721935, "remaining_time": 0.101240645}, {"learn": [13.10474349], "iteration": 75, "passed_time": 0.3063609942, "remaining_time": 0.09674557712}, {"learn": [13.08771296], "iteration": 76, "passed_time": 0.309639891, "remaining_time": 0.09248983758}, {"learn": [13.07778495], "iteration": 77, "passed_time": 0.3126049524, "remaining_time": 0.0881706276}, {"learn": [13.05794254], "iteration": 78, "passed_time": 0.3157697234, "remaining_time": 0.08393878724}, {"learn": [13.05622473], "iteration": 79, "passed_time": 0.3191843711, "remaining_time": 0.07979609278}, {"learn": [13.04552582], "iteration": 80, "passed_time": 0.3220483485, "remaining_time": 0.0755422052}, {"learn": [13.03417363], "iteration": 81, "passed_time": 0.324936826, "remaining_time": 0.07132759595}, {"learn": [13.03371749], "iteration": 82, "passed_time": 0.3276137188, "remaining_time": 0.06710160505}, {"learn": [13.02575023], "iteration": 83, "passed_time": 0.3303183201, "remaining_time": 0.06291777526}, {"learn": [13.0214887], "iteration": 84, "passed_time": 0.3334755494, "remaining_time": 0.05884862637}, {"learn": [13.01029311], "iteration": 85, "passed_time": 0.3373649086, "remaining_time": 0.05491986884}, {"learn": [13.00271788], "iteration": 86, "passed_time": 0.3401772606, "remaining_time": 0.05083108492}, {"learn": [12.98883517], "iteration": 87, "passed_time": 0.3432828229, "remaining_time": 0.04681129403}, {"learn": [12.97907913], "iteration": 88, "passed_time": 0.3462051757, "remaining_time": 0.04278940373}, {"learn": [12.97145542], "iteration": 89, "passed_time": 0.349308363, "remaining_time": 0.03881204033}, {"learn": [12.96531938], "iteration": 90, "passed_time": 0.352781011, "remaining_time": 0.03489042966}, {"learn": [12.9511331], "iteration": 91, "passed_time": 0.3558223646, "remaining_time": 0.03094107518}, {"learn": [12.93984833], "iteration": 92, "passed_time": 0.3591807619, "remaining_time": 0.02703511111}, {"learn": [12.93538594], "iteration": 93, "passed_time": 0.3627946192, "remaining_time": 0.02315710336}, {"learn": [12.92680239], "iteration": 94, "passed_time": 0.3779508866, "remaining_time": 0.01989215193}, {"learn": [12.91881466], "iteration": 95, "passed_time": 0.381918038, "remaining_time": 0.01591325158}, {"learn": [12.90334487], "iteration": 96, "passed_time": 0.3970436385, "remaining_time": 0.01227970016}, {"learn": [12.8840508], "iteration": 97, "passed_time": 0.4012521248, "remaining_time": 0.008188818874}, {"learn": [12.87313415], "iteration": 98, "passed_time": 0.406350867, "remaining_time": 0.004104554212}, {"learn": [12.86274581], "iteration": 99, "passed_time": 0.4100754751, "remaining_time": 0}]}
#!/usr/bin/env python3
"""
Preprocessing & Feature Selection
Outlier handling, VIF analizi, Chi2 test ve gereksiz feature'ları temizleme
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.feature_selection import SelectKBest, chi2, f_regression, mutual_info_regression
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from statsmodels.stats.outliers_influence import variance_inflation_factor
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def load_features():
    """Feature'ları yükle"""
    print("📊 Feature'lar yükleniyor...")
    
    train_features = pd.read_csv('train_features.csv', index_col='user_session')
    test_features = pd.read_csv('test_features.csv', index_col='user_session')
    
    print(f"✅ Train features: {train_features.shape}")
    print(f"✅ Test features: {test_features.shape}")
    
    return train_features, test_features

def analyze_outliers(train_features):
    """Outlier analizi"""
    print("\n🚨 OUTLIER ANALİZİ")
    print("=" * 50)
    
    if 'session_value' not in train_features.columns:
        print("⚠️ session_value bulunamadı!")
        return train_features
    
    target = train_features['session_value']
    
    # IQR method
    Q1 = target.quantile(0.25)
    Q3 = target.quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    
    outliers_iqr = target[(target < lower_bound) | (target > upper_bound)]
    
    # Z-score method
    z_scores = np.abs(stats.zscore(target))
    outliers_zscore = target[z_scores > 3]
    
    # Percentile method
    outliers_percentile = target[target > target.quantile(0.99)]
    
    print(f"📊 Target (session_value) Outlier Analizi:")
    print(f"   IQR method: {len(outliers_iqr):,} outliers ({len(outliers_iqr)/len(target)*100:.1f}%)")
    print(f"   Z-score method: {len(outliers_zscore):,} outliers ({len(outliers_zscore)/len(target)*100:.1f}%)")
    print(f"   99th percentile: {len(outliers_percentile):,} outliers ({len(outliers_percentile)/len(target)*100:.1f}%)")
    
    print(f"\n📈 Target İstatistikleri:")
    print(f"   Mean: {target.mean():.2f}")
    print(f"   Median: {target.median():.2f}")
    print(f"   Std: {target.std():.2f}")
    print(f"   99th percentile: {target.quantile(0.99):.2f}")
    print(f"   Max: {target.max():.2f}")
    
    return outliers_percentile

def handle_outliers(train_features, method='clip'):
    """Outlier'ları işle"""
    print(f"\n🔧 OUTLIER HANDLING ({method})")
    print("-" * 40)
    
    if 'session_value' not in train_features.columns:
        return train_features
    
    target = train_features['session_value']
    original_mean = target.mean()
    
    if method == 'clip':
        # 99th percentile'da clip et
        upper_limit = target.quantile(0.99)
        train_features['session_value'] = target.clip(upper=upper_limit)
        
    elif method == 'log_transform':
        # Log transformation
        train_features['session_value'] = np.log1p(target)
        
    elif method == 'remove':
        # Extreme outliers'ı kaldır (>99.5th percentile)
        upper_limit = target.quantile(0.995)
        train_features = train_features[train_features['session_value'] <= upper_limit]
    
    new_mean = train_features['session_value'].mean()
    print(f"✅ {method} uygulandı:")
    print(f"   Önceki mean: {original_mean:.2f}")
    print(f"   Yeni mean: {new_mean:.2f}")
    print(f"   Yeni shape: {train_features.shape}")
    
    return train_features

def calculate_vif(features_df):
    """VIF (Variance Inflation Factor) hesapla"""
    print("\n📊 VIF ANALİZİ (Multicollinearity)")
    print("=" * 50)
    
    # Sadece numerik sütunları al
    numeric_features = features_df.select_dtypes(include=[np.number])
    
    # Target'ı çıkar
    if 'session_value' in numeric_features.columns:
        numeric_features = numeric_features.drop('session_value', axis=1)
    
    # Missing values'ları doldur
    numeric_features = numeric_features.fillna(0)
    
    # Infinite values'ları temizle
    numeric_features = numeric_features.replace([np.inf, -np.inf], 0)
    
    # VIF hesapla
    vif_data = pd.DataFrame()
    vif_data["Feature"] = numeric_features.columns
    vif_data["VIF"] = [variance_inflation_factor(numeric_features.values, i) 
                       for i in range(len(numeric_features.columns))]
    
    # VIF'e göre sırala
    vif_data = vif_data.sort_values('VIF', ascending=False)
    
    print("🔍 VIF Skorları (>10 problematik, >5 dikkat):")
    high_vif_features = []
    
    for _, row in vif_data.head(15).iterrows():
        vif_score = row['VIF']
        feature = row['Feature']
        
        if vif_score > 10:
            status = "🚨 Yüksek"
            high_vif_features.append(feature)
        elif vif_score > 5:
            status = "⚠️ Orta"
        else:
            status = "✅ Düşük"
            
        print(f"   {feature:<25}: {vif_score:6.2f} {status}")
    
    return vif_data, high_vif_features

def feature_selection_chi2(X, y, k=20):
    """Chi2 test ile feature selection"""
    print(f"\n🎯 CHI2 FEATURE SELECTION (Top {k})")
    print("-" * 50)
    
    # Negative değerleri pozitif yap (Chi2 için gerekli)
    X_positive = X - X.min() + 1
    
    # Chi2 test
    selector = SelectKBest(score_func=chi2, k=k)
    X_selected = selector.fit_transform(X_positive, y)
    
    # Seçilen feature'lar
    selected_features = X.columns[selector.get_support()]
    chi2_scores = selector.scores_
    
    # Sonuçları DataFrame'e çevir
    chi2_results = pd.DataFrame({
        'feature': X.columns,
        'chi2_score': chi2_scores,
        'selected': selector.get_support()
    }).sort_values('chi2_score', ascending=False)
    
    print("🏆 En iyi Chi2 skorları:")
    for _, row in chi2_results.head(10).iterrows():
        status = "✅" if row['selected'] else "❌"
        print(f"   {status} {row['feature']:<25}: {row['chi2_score']:8.2f}")
    
    return selected_features, chi2_results

def feature_selection_f_regression(X, y, k=20):
    """F-regression test ile feature selection"""
    print(f"\n📈 F-REGRESSION FEATURE SELECTION (Top {k})")
    print("-" * 50)
    
    # F-regression test
    selector = SelectKBest(score_func=f_regression, k=k)
    X_selected = selector.fit_transform(X, y)
    
    # Seçilen feature'lar
    selected_features = X.columns[selector.get_support()]
    f_scores = selector.scores_
    
    # Sonuçları DataFrame'e çevir
    f_results = pd.DataFrame({
        'feature': X.columns,
        'f_score': f_scores,
        'selected': selector.get_support()
    }).sort_values('f_score', ascending=False)
    
    print("🏆 En iyi F-regression skorları:")
    for _, row in f_results.head(10).iterrows():
        status = "✅" if row['selected'] else "❌"
        print(f"   {status} {row['feature']:<25}: {row['f_score']:8.2f}")
    
    return selected_features, f_results

def feature_selection_mutual_info(X, y, k=20):
    """Mutual Information ile feature selection"""
    print(f"\n🔗 MUTUAL INFORMATION FEATURE SELECTION (Top {k})")
    print("-" * 50)
    
    # Mutual Information
    mi_scores = mutual_info_regression(X, y, random_state=42)
    
    # Sonuçları DataFrame'e çevir
    mi_results = pd.DataFrame({
        'feature': X.columns,
        'mi_score': mi_scores
    }).sort_values('mi_score', ascending=False)
    
    # Top k feature'ları seç
    selected_features = mi_results.head(k)['feature'].values
    
    print("🏆 En iyi Mutual Information skorları:")
    for _, row in mi_results.head(10).iterrows():
        status = "✅" if row['feature'] in selected_features else "❌"
        print(f"   {status} {row['feature']:<25}: {row['mi_score']:8.4f}")
    
    return selected_features, mi_results

def correlation_analysis(train_features):
    """Korelasyon analizi"""
    print("\n🔗 KORELASYON ANALİZİ")
    print("=" * 50)
    
    if 'session_value' not in train_features.columns:
        return None
    
    # Numerik feature'lar
    numeric_features = train_features.select_dtypes(include=[np.number])
    
    # Target ile korelasyonlar
    correlations = numeric_features.corr()['session_value'].abs().sort_values(ascending=False)
    
    print("🎯 Target ile en güçlü korelasyonlar:")
    for feature, corr in correlations.head(15).items():
        if feature != 'session_value':
            print(f"   {feature:<25}: {corr:.3f}")
    
    # Yüksek inter-feature korelasyonlar
    corr_matrix = numeric_features.drop('session_value', axis=1).corr().abs()
    high_corr_pairs = []
    
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            if corr_matrix.iloc[i, j] > 0.9:
                high_corr_pairs.append((
                    corr_matrix.columns[i], 
                    corr_matrix.columns[j], 
                    corr_matrix.iloc[i, j]
                ))
    
    if high_corr_pairs:
        print(f"\n⚠️ Yüksek inter-feature korelasyonlar (>0.9):")
        for feat1, feat2, corr in high_corr_pairs[:10]:
            print(f"   {feat1} - {feat2}: {corr:.3f}")
    else:
        print("\n✅ Yüksek inter-feature korelasyon yok")
    
    return correlations, high_corr_pairs

def create_final_feature_set(train_features, test_features, selected_features_list):
    """Final feature set oluştur"""
    print("\n🎯 FINAL FEATURE SET OLUŞTURULUYOR")
    print("=" * 50)
    
    # Tüm selection method'larından ortak feature'lar
    feature_counts = {}
    for features in selected_features_list:
        for feature in features:
            feature_counts[feature] = feature_counts.get(feature, 0) + 1
    
    # En çok seçilen feature'lar
    sorted_features = sorted(feature_counts.items(), key=lambda x: x[1], reverse=True)
    
    print("🏆 Feature Selection Sonuçları:")
    print(f"{'Feature':<25} {'Seçilme Sayısı':<15} {'Status'}")
    print("-" * 55)
    
    final_features = []
    for feature, count in sorted_features:
        if count >= 2:  # En az 2 method'da seçilmiş
            final_features.append(feature)
            status = "✅ Seçildi"
        else:
            status = "❌ Elendi"
        print(f"{feature:<25} {count:<15} {status}")
    
    # Target'ı ekle (train için)
    if 'session_value' in train_features.columns:
        final_features.append('session_value')
    
    # Final datasets
    train_final = train_features[final_features]
    test_final = test_features[[f for f in final_features if f != 'session_value']]
    
    print(f"\n📊 Final Feature Set:")
    print(f"   Toplam feature sayısı: {len(final_features) - (1 if 'session_value' in final_features else 0)}")
    print(f"   Train shape: {train_final.shape}")
    print(f"   Test shape: {test_final.shape}")
    
    return train_final, test_final, final_features

def main():
    """Ana fonksiyon"""
    print("🔧 PREPROCESSING & FEATURE SELECTION")
    print("=" * 60)
    
    # Feature'ları yükle
    train_features, test_features = load_features()
    
    # Outlier analizi
    outliers = analyze_outliers(train_features)
    
    # Outlier handling
    train_features = handle_outliers(train_features, method='clip')
    
    # VIF analizi
    vif_data, high_vif_features = calculate_vif(train_features)
    
    # Korelasyon analizi
    correlations, high_corr_pairs = correlation_analysis(train_features)
    
    # Feature selection için X ve y ayır
    if 'session_value' in train_features.columns:
        X = train_features.drop('session_value', axis=1)
        y = train_features['session_value']
        
        # Missing values ve infinite values temizle
        X = X.fillna(0).replace([np.inf, -np.inf], 0)
        
        # Feature selection methods (Chi2 regression için uygun değil, skip)
        print("\n⚠️ Chi2 test regression için uygun değil, atlanıyor...")
        f_reg_features, f_results = feature_selection_f_regression(X, y, k=20)
        mi_features, mi_results = feature_selection_mutual_info(X, y, k=20)

        # Correlation-based selection
        correlations_abs = correlations.abs().sort_values(ascending=False)
        corr_features = correlations_abs.head(20).index.tolist()
        if 'session_value' in corr_features:
            corr_features.remove('session_value')

        # Final feature set
        selected_features_list = [f_reg_features, mi_features, corr_features]
        train_final, test_final, final_features = create_final_feature_set(
            train_features, test_features, selected_features_list
        )
        
        # Sonuçları kaydet
        print("\n💾 Preprocessing sonuçları kaydediliyor...")
        train_final.to_csv('train_preprocessed.csv')
        test_final.to_csv('test_preprocessed.csv')
        
        # Analysis results
        vif_data.to_csv('vif_analysis.csv', index=False)
        f_results.to_csv('f_regression_analysis.csv', index=False)
        mi_results.to_csv('mutual_info_analysis.csv', index=False)
        
        print("✅ train_preprocessed.csv kaydedildi")
        print("✅ test_preprocessed.csv kaydedildi")
        print("✅ Analiz dosyları kaydedildi")
    
    print("\n✨ PREPROCESSING TAMAMLANDI!")
    print("🎯 Artık model eğitimine hazırsınız!")

if __name__ == "__main__":
    main()

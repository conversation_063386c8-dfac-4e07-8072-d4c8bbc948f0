#!/usr/bin/env python3
"""
Regression with Threshold Tuning
Regression modelleri için threshold optimization ve model tuning
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, RandomizedSearchCV
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet
import lightgbm as lgb
import xgboost as xgb
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

def load_final_data():
    """Final processed veriyi yükle"""
    print("📊 Final processed veri yükleniyor...")
    
    train_df = pd.read_csv('train_final_processed.csv', index_col='user_session')
    test_df = pd.read_csv('test_final_processed.csv', index_col='user_session')
    
    print(f"✅ Train: {train_df.shape}")
    print(f"✅ Test: {test_df.shape}")
    
    return train_df, test_df

def prepare_data(train_df, test_df):
    """Veriyi model için hazırla"""
    print("\n🔧 VERİ HAZIRLIĞI")
    print("-" * 40)
    
    # Target'ı ayır
    X_train = train_df.drop('session_value', axis=1)
    y_train = train_df['session_value']
    X_test = test_df
    
    # Train-validation split
    X_tr, X_val, y_tr, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )
    
    print(f"📊 Veri boyutları:")
    print(f"   X_train: {X_train.shape}")
    print(f"   X_test: {X_test.shape}")
    print(f"   X_tr: {X_tr.shape}, X_val: {X_val.shape}")
    print(f"   y_train mean: {y_train.mean():.2f}")
    
    return X_train, X_test, y_train, X_tr, X_val, y_tr, y_val

def evaluate_model(model, X_tr, X_val, y_tr, y_val, model_name):
    """Model performansını değerlendir"""
    # Fit model
    model.fit(X_tr, y_tr)
    
    # Predictions
    y_pred_train = model.predict(X_tr)
    y_pred_val = model.predict(X_val)
    
    # Metrics
    train_mse = mean_squared_error(y_tr, y_pred_train)
    val_mse = mean_squared_error(y_val, y_pred_val)
    train_rmse = np.sqrt(train_mse)
    val_rmse = np.sqrt(val_mse)
    train_mae = mean_absolute_error(y_tr, y_pred_train)
    val_mae = mean_absolute_error(y_val, y_pred_val)
    train_r2 = r2_score(y_tr, y_pred_train)
    val_r2 = r2_score(y_val, y_pred_val)
    
    # Cross-validation
    cv_scores = cross_val_score(model, X_tr, y_tr, cv=5, scoring='neg_mean_squared_error')
    cv_rmse = np.sqrt(-cv_scores.mean())
    cv_std = np.sqrt(cv_scores.std())
    
    results = {
        'model': model_name,
        'train_mse': train_mse,
        'val_mse': val_mse,
        'train_rmse': train_rmse,
        'val_rmse': val_rmse,
        'train_mae': train_mae,
        'val_mae': val_mae,
        'train_r2': train_r2,
        'val_r2': val_r2,
        'cv_rmse': cv_rmse,
        'cv_std': cv_std,
        'overfitting': train_rmse / val_rmse if val_rmse > 0 else 1.0
    }
    
    return results, model

def tune_random_forest(X_tr, y_tr, X_val, y_val):
    """Random Forest hyperparameter tuning"""
    print("\n🌲 RANDOM FOREST TUNING")
    print("-" * 40)
    
    # Parameter grid
    param_grid = {
        'n_estimators': [100, 200, 300],
        'max_depth': [5, 10, 15, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4],
        'max_features': ['sqrt', 'log2', None]
    }
    
    # GridSearchCV
    rf = RandomForestRegressor(random_state=42, n_jobs=-1)
    grid_search = GridSearchCV(
        rf, param_grid, cv=5, scoring='neg_mean_squared_error',
        n_jobs=-1, verbose=1
    )
    
    print("🔄 Grid search başlıyor...")
    grid_search.fit(X_tr, y_tr)
    
    best_rf = grid_search.best_estimator_
    best_params = grid_search.best_params_
    best_score = np.sqrt(-grid_search.best_score_)
    
    print(f"✅ En iyi parametreler: {best_params}")
    print(f"✅ En iyi CV RMSE: {best_score:.3f}")
    
    return best_rf, best_params, best_score

def tune_xgboost(X_tr, y_tr, X_val, y_val):
    """XGBoost hyperparameter tuning"""
    print("\n🚀 XGBOOST TUNING")
    print("-" * 40)
    
    # Parameter distributions for RandomizedSearchCV
    param_dist = {
        'n_estimators': [100, 200, 300, 500],
        'max_depth': [3, 5, 7, 9],
        'learning_rate': [0.01, 0.05, 0.1, 0.2],
        'subsample': [0.8, 0.9, 1.0],
        'colsample_bytree': [0.8, 0.9, 1.0],
        'reg_alpha': [0, 0.1, 1, 10],
        'reg_lambda': [0, 0.1, 1, 10]
    }
    
    # RandomizedSearchCV
    xgb_model = xgb.XGBRegressor(random_state=42, n_jobs=-1)
    random_search = RandomizedSearchCV(
        xgb_model, param_dist, n_iter=50, cv=5, 
        scoring='neg_mean_squared_error', random_state=42, n_jobs=-1, verbose=1
    )
    
    print("🔄 Random search başlıyor...")
    random_search.fit(X_tr, y_tr)
    
    best_xgb = random_search.best_estimator_
    best_params = random_search.best_params_
    best_score = np.sqrt(-random_search.best_score_)
    
    print(f"✅ En iyi parametreler: {best_params}")
    print(f"✅ En iyi CV RMSE: {best_score:.3f}")
    
    return best_xgb, best_params, best_score

def tune_lightgbm(X_tr, y_tr, X_val, y_val):
    """LightGBM hyperparameter tuning"""
    print("\n🌟 LIGHTGBM TUNING")
    print("-" * 40)
    
    # Parameter distributions
    param_dist = {
        'n_estimators': [100, 200, 300, 500],
        'max_depth': [3, 5, 7, 9, -1],
        'learning_rate': [0.01, 0.05, 0.1, 0.2],
        'num_leaves': [31, 50, 100, 200],
        'subsample': [0.8, 0.9, 1.0],
        'colsample_bytree': [0.8, 0.9, 1.0],
        'reg_alpha': [0, 0.1, 1, 10],
        'reg_lambda': [0, 0.1, 1, 10]
    }
    
    # RandomizedSearchCV
    lgb_model = lgb.LGBMRegressor(random_state=42, n_jobs=-1, verbose=-1)
    random_search = RandomizedSearchCV(
        lgb_model, param_dist, n_iter=50, cv=5,
        scoring='neg_mean_squared_error', random_state=42, n_jobs=-1, verbose=1
    )
    
    print("🔄 Random search başlıyor...")
    random_search.fit(X_tr, y_tr)
    
    best_lgb = random_search.best_estimator_
    best_params = random_search.best_params_
    best_score = np.sqrt(-random_search.best_score_)
    
    print(f"✅ En iyi parametreler: {best_params}")
    print(f"✅ En iyi CV RMSE: {best_score:.3f}")
    
    return best_lgb, best_params, best_score

def tune_linear_models(X_tr, y_tr, X_val, y_val):
    """Linear modeller için tuning"""
    print("\n📈 LINEAR MODELS TUNING")
    print("-" * 40)
    
    models_results = []
    
    # Ridge Regression
    ridge_params = {'alpha': [0.1, 1.0, 10.0, 100.0, 1000.0]}
    ridge = Ridge(random_state=42)
    ridge_grid = GridSearchCV(ridge, ridge_params, cv=5, scoring='neg_mean_squared_error')
    ridge_grid.fit(X_tr, y_tr)
    
    best_ridge = ridge_grid.best_estimator_
    ridge_score = np.sqrt(-ridge_grid.best_score_)
    models_results.append(('Ridge', best_ridge, ridge_grid.best_params_, ridge_score))
    
    # Lasso Regression
    lasso_params = {'alpha': [0.01, 0.1, 1.0, 10.0, 100.0]}
    lasso = Lasso(random_state=42, max_iter=2000)
    lasso_grid = GridSearchCV(lasso, lasso_params, cv=5, scoring='neg_mean_squared_error')
    lasso_grid.fit(X_tr, y_tr)
    
    best_lasso = lasso_grid.best_estimator_
    lasso_score = np.sqrt(-lasso_grid.best_score_)
    models_results.append(('Lasso', best_lasso, lasso_grid.best_params_, lasso_score))
    
    # ElasticNet
    elastic_params = {
        'alpha': [0.01, 0.1, 1.0, 10.0],
        'l1_ratio': [0.1, 0.3, 0.5, 0.7, 0.9]
    }
    elastic = ElasticNet(random_state=42, max_iter=2000)
    elastic_grid = GridSearchCV(elastic, elastic_params, cv=5, scoring='neg_mean_squared_error')
    elastic_grid.fit(X_tr, y_tr)
    
    best_elastic = elastic_grid.best_estimator_
    elastic_score = np.sqrt(-elastic_grid.best_score_)
    models_results.append(('ElasticNet', best_elastic, elastic_grid.best_params_, elastic_score))
    
    # Sonuçları yazdır
    for name, model, params, score in models_results:
        print(f"✅ {name}: CV RMSE = {score:.3f}, Params = {params}")
    
    return models_results

def prediction_threshold_optimization(model, X_val, y_val, model_name):
    """Prediction threshold optimization (regression için clipping)"""
    print(f"\n🎯 PREDICTION THRESHOLD OPTIMIZATION - {model_name}")
    print("-" * 50)
    
    # Base predictions
    y_pred = model.predict(X_val)
    base_mse = mean_squared_error(y_val, y_pred)
    
    print(f"📊 Base MSE: {base_mse:.3f}")
    
    # Target statistics
    y_min, y_max = y_val.min(), y_val.max()
    y_mean, y_std = y_val.mean(), y_val.std()
    
    # Test different clipping thresholds
    thresholds = [
        (y_min, y_max),  # No clipping
        (y_mean - 3*y_std, y_mean + 3*y_std),  # 3-sigma
        (y_mean - 2*y_std, y_mean + 2*y_std),  # 2-sigma
        (y_val.quantile(0.01), y_val.quantile(0.99)),  # 1-99 percentile
        (y_val.quantile(0.05), y_val.quantile(0.95)),  # 5-95 percentile
    ]
    
    best_mse = base_mse
    best_threshold = (y_min, y_max)
    
    print("🔍 Threshold optimization:")
    for i, (lower, upper) in enumerate(thresholds):
        y_pred_clipped = np.clip(y_pred, lower, upper)
        mse = mean_squared_error(y_val, y_pred_clipped)
        
        threshold_name = [
            "No clipping",
            "3-sigma",
            "2-sigma", 
            "1-99 percentile",
            "5-95 percentile"
        ][i]
        
        improvement = (base_mse - mse) / base_mse * 100
        print(f"   {threshold_name}: MSE = {mse:.3f} ({improvement:+.1f}%)")
        
        if mse < best_mse:
            best_mse = mse
            best_threshold = (lower, upper)
    
    print(f"✅ En iyi threshold: [{best_threshold[0]:.2f}, {best_threshold[1]:.2f}]")
    print(f"✅ MSE improvement: {(base_mse - best_mse) / base_mse * 100:.1f}%")
    
    return best_threshold, best_mse

def compare_all_models(X_tr, X_val, y_tr, y_val):
    """Tüm modelleri karşılaştır"""
    print("\n🏆 TÜM MODELLERİ KARŞILAŞTIRMA")
    print("=" * 60)
    
    all_results = []
    
    # 1. Tuned Random Forest
    best_rf, rf_params, rf_score = tune_random_forest(X_tr, y_tr, X_val, y_val)
    rf_results, _ = evaluate_model(best_rf, X_tr, X_val, y_tr, y_val, "Tuned_RandomForest")
    all_results.append(rf_results)
    
    # 2. Tuned XGBoost
    best_xgb, xgb_params, xgb_score = tune_xgboost(X_tr, y_tr, X_val, y_val)
    xgb_results, _ = evaluate_model(best_xgb, X_tr, X_val, y_tr, y_val, "Tuned_XGBoost")
    all_results.append(xgb_results)
    
    # 3. Tuned LightGBM
    best_lgb, lgb_params, lgb_score = tune_lightgbm(X_tr, y_tr, X_val, y_val)
    lgb_results, _ = evaluate_model(best_lgb, X_tr, X_val, y_tr, y_val, "Tuned_LightGBM")
    all_results.append(lgb_results)
    
    # 4. Linear Models
    linear_models = tune_linear_models(X_tr, y_tr, X_val, y_val)
    for name, model, params, score in linear_models:
        results, _ = evaluate_model(model, X_tr, X_val, y_tr, y_val, f"Tuned_{name}")
        all_results.append(results)
    
    # Results summary
    df_results = pd.DataFrame(all_results).sort_values('val_mse')
    
    print(f"\n📊 MODEL COMPARISON RESULTS:")
    print(f"{'Model':<20} {'Val MSE':<10} {'Val RMSE':<10} {'Val R²':<8} {'CV RMSE':<10} {'Overfit':<8}")
    print("-" * 80)
    
    for _, row in df_results.iterrows():
        print(f"{row['model']:<20} {row['val_mse']:<10.3f} {row['val_rmse']:<10.3f} "
              f"{row['val_r2']:<8.3f} {row['cv_rmse']:<10.3f} {row['overfitting']:<8.3f}")
    
    # Best model
    best_model_info = df_results.iloc[0]
    print(f"\n🏆 EN İYİ MODEL: {best_model_info['model']}")
    print(f"   Validation MSE: {best_model_info['val_mse']:.3f}")
    print(f"   Validation RMSE: {best_model_info['val_rmse']:.3f}")
    print(f"   Validation R²: {best_model_info['val_r2']:.3f}")
    
    return df_results, best_model_info

def main():
    """Ana fonksiyon"""
    print("🎯 REGRESSION WITH THRESHOLD TUNING")
    print("=" * 60)
    
    # Veri yükleme
    train_df, test_df = load_final_data()
    
    # Veri hazırlığı
    X_train, X_test, y_train, X_tr, X_val, y_tr, y_val = prepare_data(train_df, test_df)
    
    # Tüm modelleri karşılaştır
    df_results, best_model_info = compare_all_models(X_tr, X_val, y_tr, y_val)
    
    # Sonuçları kaydet
    df_results.to_csv('model_comparison_results.csv', index=False)
    print(f"\n✅ model_comparison_results.csv kaydedildi")
    
    print(f"\n✨ REGRESSION WITH THRESHOLD TUNING TAMAMLANDI!")
    print(f"🎯 En iyi model ile prediction yapabilirsiniz!")

if __name__ == "__main__":
    main()

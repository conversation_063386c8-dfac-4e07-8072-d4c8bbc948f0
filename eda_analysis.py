#!/usr/bin/env python3
"""
EDA Analysis with D-Tale
Kaggle E-ticaret Session Value Prediction için detaylı EDA
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import dtale
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Veri setlerini yükle"""
    print("📊 Veri setleri yükleniyor...")
    
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    
    print(f"✅ Train veri seti: {train_df.shape[0]:,} satır, {train_df.shape[1]} sütun")
    print(f"✅ Test veri seti: {test_df.shape[0]:,} satır, {test_df.shape[1]} sütun")
    
    return train_df, test_df

def basic_info(df, name):
    """Temel veri bilgilerini göster"""
    print(f"\n{'='*50}")
    print(f"📋 {name} VERİ SETİ TEMEL BİLGİLER")
    print(f"{'='*50}")
    
    print(f"📏 Boyut: {df.shape}")
    print(f"📊 Sütunlar: {list(df.columns)}")
    print(f"🔍 Veri tipleri:")
    print(df.dtypes)
    print(f"\n📈 İstatistiksel Özet:")
    print(df.describe())
    
    if 'session_value' in df.columns:
        print(f"\n💰 Session Value İstatistikleri:")
        print(f"   Min: {df['session_value'].min():.2f}")
        print(f"   Max: {df['session_value'].max():.2f}")
        print(f"   Ortalama: {df['session_value'].mean():.2f}")
        print(f"   Medyan: {df['session_value'].median():.2f}")
        print(f"   Std: {df['session_value'].std():.2f}")

def analyze_events(df, name):
    """Event türlerini analiz et"""
    print(f"\n🎯 {name} - EVENT TÜRLERİ ANALİZİ")
    print("-" * 40)
    
    event_counts = df['event_type'].value_counts()
    print("📊 Event türü dağılımı:")
    for event, count in event_counts.items():
        percentage = (count / len(df)) * 100
        print(f"   {event}: {count:,} ({percentage:.1f}%)")

def analyze_sessions(df, name):
    """Session bazlı analiz"""
    print(f"\n🔄 {name} - SESSION ANALİZİ")
    print("-" * 40)
    
    # Session başına event sayısı
    session_events = df.groupby('user_session').size()
    print(f"📈 Session başına event istatistikleri:")
    print(f"   Ortalama: {session_events.mean():.2f}")
    print(f"   Medyan: {session_events.median():.2f}")
    print(f"   Min: {session_events.min()}")
    print(f"   Max: {session_events.max()}")
    
    # Benzersiz session sayısı
    unique_sessions = df['user_session'].nunique()
    print(f"🔢 Toplam benzersiz session: {unique_sessions:,}")
    
    # Benzersiz kullanıcı sayısı
    unique_users = df['user_id'].nunique()
    print(f"👥 Toplam benzersiz kullanıcı: {unique_users:,}")
    
    # Kullanıcı başına session sayısı
    user_sessions = df.groupby('user_id')['user_session'].nunique()
    print(f"📊 Kullanıcı başına session istatistikleri:")
    print(f"   Ortalama: {user_sessions.mean():.2f}")
    print(f"   Medyan: {user_sessions.median():.2f}")

def analyze_products_categories(df, name):
    """Ürün ve kategori analizi"""
    print(f"\n🛍️ {name} - ÜRÜN VE KATEGORİ ANALİZİ")
    print("-" * 40)
    
    # Benzersiz ürün sayısı
    unique_products = df['product_id'].nunique()
    print(f"📦 Toplam benzersiz ürün: {unique_products:,}")
    
    # Benzersiz kategori sayısı
    unique_categories = df['category_id'].nunique()
    print(f"🏷️ Toplam benzersiz kategori: {unique_categories:,}")
    
    # En popüler kategoriler
    top_categories = df['category_id'].value_counts().head(10)
    print(f"\n🔝 En popüler 10 kategori:")
    for i, (cat, count) in enumerate(top_categories.items(), 1):
        print(f"   {i}. {cat}: {count:,} event")

def analyze_time_patterns(df, name):
    """Zaman bazlı analiz"""
    print(f"\n⏰ {name} - ZAMAN ANALİZİ")
    print("-" * 40)
    
    # Event_time'ı datetime'a çevir
    df['event_time'] = pd.to_datetime(df['event_time'])
    
    # Tarih aralığı
    min_date = df['event_time'].min()
    max_date = df['event_time'].max()
    print(f"📅 Tarih aralığı: {min_date.date()} - {max_date.date()}")
    
    # Günlük event sayısı
    df['date'] = df['event_time'].dt.date
    daily_events = df.groupby('date').size()
    print(f"📊 Günlük event istatistikleri:")
    print(f"   Ortalama: {daily_events.mean():.0f}")
    print(f"   Min: {daily_events.min()}")
    print(f"   Max: {daily_events.max()}")
    
    # Saatlik dağılım
    df['hour'] = df['event_time'].dt.hour
    hourly_dist = df['hour'].value_counts().sort_index()
    print(f"\n🕐 En aktif saatler:")
    top_hours = hourly_dist.nlargest(5)
    for hour, count in top_hours.items():
        print(f"   {hour:02d}:00 - {count:,} event")

def session_value_analysis(train_df):
    """Session value detaylı analizi (sadece train için)"""
    print(f"\n💰 SESSION VALUE DETAYLI ANALİZ")
    print("-" * 40)
    
    # Session bazında session_value analizi
    session_values = train_df.groupby('user_session')['session_value'].first()
    
    print(f"📊 Session Value Dağılımı:")
    print(f"   Ortalama: {session_values.mean():.2f}")
    print(f"   Medyan: {session_values.median():.2f}")
    print(f"   Std: {session_values.std():.2f}")
    print(f"   Min: {session_values.min():.2f}")
    print(f"   Max: {session_values.max():.2f}")
    
    # Quantile analizi
    quantiles = [0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99]
    print(f"\n📈 Session Value Quantile Analizi:")
    for q in quantiles:
        value = session_values.quantile(q)
        print(f"   {q*100:4.0f}%: {value:7.2f}")
    
    # Event türü ile session value ilişkisi
    print(f"\n🎯 Event Türü - Session Value İlişkisi:")
    
    # Her session için event türü sayıları
    session_event_counts = train_df.groupby(['user_session', 'event_type']).size().unstack(fill_value=0)
    session_event_counts['session_value'] = train_df.groupby('user_session')['session_value'].first()
    
    for event_type in train_df['event_type'].unique():
        if event_type in session_event_counts.columns:
            correlation = session_event_counts[event_type].corr(session_event_counts['session_value'])
            print(f"   {event_type} korelasyonu: {correlation:.3f}")
    
    return session_event_counts

def start_dtale_analysis(train_df, test_df):
    """D-Tale interaktif analizi başlat"""
    print(f"\n🚀 D-TALE İNTERAKTİF ANALİZ BAŞLATILIYOR...")
    print("-" * 50)
    
    # Train verisi için D-Tale
    print("📊 Train verisi için D-Tale başlatılıyor...")
    train_dtale = dtale.show(train_df, host='localhost', port=40000)
    print(f"✅ Train D-Tale: http://localhost:40000")
    
    # Test verisi için D-Tale
    print("📊 Test verisi için D-Tale başlatılıyor...")
    test_dtale = dtale.show(test_df, host='localhost', port=40001)
    print(f"✅ Test D-Tale: http://localhost:40001")
    
    print(f"\n🌐 D-Tale Arayüzleri:")
    print(f"   📈 Train Verisi: http://localhost:40000")
    print(f"   📉 Test Verisi:  http://localhost:40001")
    print(f"\n💡 D-Tale'de yapabilecekleriniz:")
    print(f"   • Sütun bazlı istatistikler")
    print(f"   • Korelasyon matrisi")
    print(f"   • Histogram ve dağılım grafikleri")
    print(f"   • Scatter plot'lar")
    print(f"   • Eksik veri analizi")
    print(f"   • Filtreleme ve gruplama")
    print(f"   • Veri profiling")
    
    return train_dtale, test_dtale

def main():
    """Ana fonksiyon"""
    print("🎯 KAGGLE E-TİCARET SESSION VALUE PREDICTION - EDA ANALİZİ")
    print("=" * 60)
    
    # Veri yükleme
    train_df, test_df = load_data()
    
    # Temel bilgiler
    basic_info(train_df, "TRAIN")
    basic_info(test_df, "TEST")
    
    # Event analizi
    analyze_events(train_df, "TRAIN")
    analyze_events(test_df, "TEST")
    
    # Session analizi
    analyze_sessions(train_df, "TRAIN")
    analyze_sessions(test_df, "TEST")
    
    # Ürün ve kategori analizi
    analyze_products_categories(train_df, "TRAIN")
    analyze_products_categories(test_df, "TEST")
    
    # Zaman analizi
    analyze_time_patterns(train_df.copy(), "TRAIN")
    analyze_time_patterns(test_df.copy(), "TEST")
    
    # Session value analizi (sadece train için)
    session_event_counts = session_value_analysis(train_df)
    
    # D-Tale interaktif analiz
    train_dtale, test_dtale = start_dtale_analysis(train_df, test_df)
    
    print(f"\n✨ EDA ANALİZİ TAMAMLANDI!")
    print(f"🌐 D-Tale arayüzlerini tarayıcınızda açabilirsiniz.")
    print(f"⚠️  Analizi durdurmak için Ctrl+C tuşlayın.")
    
    # D-Tale sunucularını açık tut
    try:
        input("\nD-Tale sunucularını kapatmak için Enter tuşuna basın...")
    except KeyboardInterrupt:
        print("\n👋 Analiz sonlandırılıyor...")
    finally:
        # D-Tale sunucularını kapat
        dtale.global_state.cleanup()
        print("✅ D-Tale sunucuları kapatıldı.")

if __name__ == "__main__":
    main()

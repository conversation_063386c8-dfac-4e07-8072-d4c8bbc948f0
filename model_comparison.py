#!/usr/bin/env python3
"""
All Model Performance Comparison
Tüm modellerin performansını karşılaştır<PERSON>r ve en iyisini seçer
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Traditional ML Models
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.svm import SVR
from sklearn.neighbors import KNeighborsRegressor
import xgboost as xgb
import lightgbm as lgb
import catboost as cb

# Neural Networks
from sklearn.neural_network import MLPRegressor
import tensorflow as tf
from tensorflow import keras

import time
import json

def load_model_data():
    """Model için hazır veriyi yükle"""
    print("📊 Model verisi yükleniyor...")
    
    X_train = pd.read_csv('X_train_model_ready.csv', index_col='user_session')
    X_test = pd.read_csv('X_test_model_ready.csv', index_col='user_session')
    y_train = pd.read_csv('y_train_model_ready.csv', index_col='user_session')['session_value']
    
    print(f"✅ X_train: {X_train.shape}")
    print(f"✅ X_test: {X_test.shape}")
    print(f"✅ y_train: {y_train.shape}")
    
    return X_train, X_test, y_train

def prepare_data_for_models(X_train, X_test, y_train):
    """Modeller için veri hazırlığı"""
    print("🔧 Veri hazırlığı...")
    
    # Train-validation split
    X_tr, X_val, y_tr, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )
    
    # Scaling (neural networks için)
    scaler = StandardScaler()
    X_tr_scaled = scaler.fit_transform(X_tr)
    X_val_scaled = scaler.transform(X_val)
    X_test_scaled = scaler.transform(X_test)
    
    return X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled, X_test_scaled, scaler

def evaluate_model(model, X_tr, X_val, y_tr, y_val, model_name):
    """Model performansını değerlendir"""
    start_time = time.time()
    
    # Fit model
    model.fit(X_tr, y_tr)
    
    # Predictions
    y_pred_train = model.predict(X_tr)
    y_pred_val = model.predict(X_val)
    
    # Metrics
    train_rmse = np.sqrt(mean_squared_error(y_tr, y_pred_train))
    val_rmse = np.sqrt(mean_squared_error(y_val, y_pred_val))
    train_mae = mean_absolute_error(y_tr, y_pred_train)
    val_mae = mean_absolute_error(y_val, y_pred_val)
    train_r2 = r2_score(y_tr, y_pred_train)
    val_r2 = r2_score(y_val, y_pred_val)
    
    training_time = time.time() - start_time
    
    # Cross-validation score
    cv_scores = cross_val_score(model, X_tr, y_tr, cv=5, scoring='neg_root_mean_squared_error')
    cv_rmse = -cv_scores.mean()
    cv_std = cv_scores.std()
    
    results = {
        'model': model_name,
        'train_rmse': train_rmse,
        'val_rmse': val_rmse,
        'train_mae': train_mae,
        'val_mae': val_mae,
        'train_r2': train_r2,
        'val_r2': val_r2,
        'cv_rmse': cv_rmse,
        'cv_std': cv_std,
        'training_time': training_time,
        'overfitting': train_rmse / val_rmse if val_rmse > 0 else 1.0
    }
    
    return results, model

def test_traditional_ml_models(X_tr, X_val, y_tr, y_val):
    """Traditional ML modelleri test et"""
    print("\n🌲 TRADITIONAL ML MODELS")
    print("-" * 50)
    
    models = {
        'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1),
        'XGBoost': xgb.XGBRegressor(n_estimators=100, random_state=42, n_jobs=-1),
        'LightGBM': lgb.LGBMRegressor(n_estimators=100, random_state=42, n_jobs=-1, verbose=-1),
        'CatBoost': cb.CatBoostRegressor(iterations=100, random_state=42, verbose=False),
        'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
        'Extra Trees': ExtraTreesRegressor(n_estimators=100, random_state=42, n_jobs=-1),
    }
    
    results = []
    trained_models = {}
    
    for name, model in models.items():
        print(f"🔄 {name} eğitiliyor...")
        try:
            result, trained_model = evaluate_model(model, X_tr, X_val, y_tr, y_val, name)
            results.append(result)
            trained_models[name] = trained_model
            print(f"✅ {name}: Val RMSE = {result['val_rmse']:.3f}, Val R² = {result['val_r2']:.3f}")
        except Exception as e:
            print(f"❌ {name} hatası: {str(e)}")
    
    return results, trained_models

def test_linear_models(X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled):
    """Linear modelleri test et"""
    print("\n📈 LINEAR MODELS")
    print("-" * 50)
    
    models = {
        'Linear Regression': LinearRegression(),
        'Ridge': Ridge(alpha=1.0, random_state=42),
        'Lasso': Lasso(alpha=1.0, random_state=42),
        'ElasticNet': ElasticNet(alpha=1.0, random_state=42),
        'KNN': KNeighborsRegressor(n_neighbors=5, n_jobs=-1)
    }
    
    results = []
    trained_models = {}
    
    for name, model in models.items():
        print(f"🔄 {name} eğitiliyor...")
        try:
            # KNN için scaled data kullan
            if name in ['KNN']:
                result, trained_model = evaluate_model(model, X_tr_scaled, X_val_scaled, y_tr, y_val, name)
            else:
                result, trained_model = evaluate_model(model, X_tr, X_val, y_tr, y_val, name)
            
            results.append(result)
            trained_models[name] = trained_model
            print(f"✅ {name}: Val RMSE = {result['val_rmse']:.3f}, Val R² = {result['val_r2']:.3f}")
        except Exception as e:
            print(f"❌ {name} hatası: {str(e)}")
    
    return results, trained_models

def create_simple_neural_network(input_dim):
    """Basit neural network oluştur"""
    model = keras.Sequential([
        keras.layers.Dense(128, activation='relu', input_shape=(input_dim,)),
        keras.layers.Dropout(0.3),
        keras.layers.Dense(64, activation='relu'),
        keras.layers.Dropout(0.3),
        keras.layers.Dense(32, activation='relu'),
        keras.layers.Dense(1)
    ])
    
    model.compile(optimizer='adam', loss='mse', metrics=['mae'])
    return model

def test_neural_networks(X_tr_scaled, X_val_scaled, y_tr, y_val):
    """Neural network modelleri test et"""
    print("\n🧠 NEURAL NETWORK MODELS")
    print("-" * 50)
    
    results = []
    trained_models = {}
    
    # Sklearn MLP
    print("🔄 MLP Regressor eğitiliyor...")
    try:
        mlp = MLPRegressor(
            hidden_layer_sizes=(100, 50),
            max_iter=500,
            random_state=42,
            early_stopping=True,
            validation_fraction=0.1
        )
        result, trained_model = evaluate_model(mlp, X_tr_scaled, X_val_scaled, y_tr, y_val, 'MLP Regressor')
        results.append(result)
        trained_models['MLP Regressor'] = trained_model
        print(f"✅ MLP Regressor: Val RMSE = {result['val_rmse']:.3f}, Val R² = {result['val_r2']:.3f}")
    except Exception as e:
        print(f"❌ MLP Regressor hatası: {str(e)}")
    
    # TensorFlow Neural Network
    print("🔄 TensorFlow NN eğitiliyor...")
    try:
        start_time = time.time()
        
        tf_model = create_simple_neural_network(X_tr_scaled.shape[1])
        
        # Early stopping
        early_stopping = keras.callbacks.EarlyStopping(
            monitor='val_loss', patience=10, restore_best_weights=True
        )
        
        # Fit model
        history = tf_model.fit(
            X_tr_scaled, y_tr,
            validation_data=(X_val_scaled, y_val),
            epochs=100,
            batch_size=32,
            callbacks=[early_stopping],
            verbose=0
        )
        
        # Predictions
        y_pred_train = tf_model.predict(X_tr_scaled, verbose=0).flatten()
        y_pred_val = tf_model.predict(X_val_scaled, verbose=0).flatten()
        
        # Metrics
        train_rmse = np.sqrt(mean_squared_error(y_tr, y_pred_train))
        val_rmse = np.sqrt(mean_squared_error(y_val, y_pred_val))
        train_r2 = r2_score(y_tr, y_pred_train)
        val_r2 = r2_score(y_val, y_pred_val)
        training_time = time.time() - start_time
        
        tf_result = {
            'model': 'TensorFlow NN',
            'train_rmse': train_rmse,
            'val_rmse': val_rmse,
            'train_mae': mean_absolute_error(y_tr, y_pred_train),
            'val_mae': mean_absolute_error(y_val, y_pred_val),
            'train_r2': train_r2,
            'val_r2': val_r2,
            'cv_rmse': val_rmse,  # Approximation
            'cv_std': 0.0,
            'training_time': training_time,
            'overfitting': train_rmse / val_rmse if val_rmse > 0 else 1.0
        }
        
        results.append(tf_result)
        trained_models['TensorFlow NN'] = tf_model
        print(f"✅ TensorFlow NN: Val RMSE = {val_rmse:.3f}, Val R² = {val_r2:.3f}")
        
    except Exception as e:
        print(f"❌ TensorFlow NN hatası: {str(e)}")
    
    return results, trained_models

def create_results_summary(all_results):
    """Sonuçları özetle"""
    print("\n📊 MODEL PERFORMANCE SUMMARY")
    print("=" * 80)
    
    # DataFrame oluştur
    df_results = pd.DataFrame(all_results)
    
    # Sırala (validation RMSE'ye göre)
    df_results = df_results.sort_values('val_rmse')
    
    print(f"{'Model':<20} {'Val RMSE':<10} {'Val R²':<8} {'CV RMSE':<10} {'Time(s)':<8} {'Overfit':<8}")
    print("-" * 80)
    
    for _, row in df_results.iterrows():
        print(f"{row['model']:<20} {row['val_rmse']:<10.3f} {row['val_r2']:<8.3f} "
              f"{row['cv_rmse']:<10.3f} {row['training_time']:<8.1f} {row['overfitting']:<8.3f}")
    
    # En iyi modeller
    print(f"\n🏆 TOP 5 MODELS:")
    for i, (_, row) in enumerate(df_results.head(5).iterrows(), 1):
        print(f"   {i}. {row['model']}: Val RMSE = {row['val_rmse']:.3f}, Val R² = {row['val_r2']:.3f}")
    
    return df_results

def save_results(df_results, all_trained_models):
    """Sonuçları kaydet"""
    print("\n💾 SONUÇLAR KAYDEDİLİYOR...")
    print("-" * 40)
    
    # Results CSV
    df_results.to_csv('model_comparison_results.csv', index=False)
    print("✅ model_comparison_results.csv kaydedildi")
    
    # Best model info
    best_model_name = df_results.iloc[0]['model']
    best_model_info = {
        'best_model': best_model_name,
        'val_rmse': df_results.iloc[0]['val_rmse'],
        'val_r2': df_results.iloc[0]['val_r2'],
        'cv_rmse': df_results.iloc[0]['cv_rmse']
    }
    
    with open('best_model_info.json', 'w') as f:
        json.dump(best_model_info, f, indent=2)
    
    print(f"✅ En iyi model: {best_model_name}")
    print(f"✅ best_model_info.json kaydedildi")

def main():
    """Ana fonksiyon"""
    print("🚀 ALL MODEL PERFORMANCE COMPARISON")
    print("=" * 60)
    
    # Veri yükleme
    X_train, X_test, y_train = load_model_data()
    
    # Veri hazırlığı
    X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled, X_test_scaled, scaler = prepare_data_for_models(
        X_train, X_test, y_train
    )
    
    all_results = []
    all_trained_models = {}
    
    # Traditional ML Models
    trad_results, trad_models = test_traditional_ml_models(X_tr, X_val, y_tr, y_val)
    all_results.extend(trad_results)
    all_trained_models.update(trad_models)
    
    # Linear Models
    linear_results, linear_models = test_linear_models(X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled)
    all_results.extend(linear_results)
    all_trained_models.update(linear_models)
    
    # Neural Networks
    nn_results, nn_models = test_neural_networks(X_tr_scaled, X_val_scaled, y_tr, y_val)
    all_results.extend(nn_results)
    all_trained_models.update(nn_models)
    
    # Sonuçları özetle
    df_results = create_results_summary(all_results)
    
    # Sonuçları kaydet
    save_results(df_results, all_trained_models)
    
    print("\n✨ MODEL COMPARISON TAMAMLANDI!")
    print("📁 Dosyalar: model_comparison_results.csv, best_model_info.json")

if __name__ == "__main__":
    main()

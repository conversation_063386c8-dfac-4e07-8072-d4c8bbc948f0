#!/usr/bin/env python3
"""
Hyperparameter Tuning for Best Models
En iyi performans gösteren modeller için hyperparameter optimization
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, RandomizedSearchCV, GridSearchCV
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Best models
from sklearn.neural_network import MLPRegressor
import lightgbm as lgb
import catboost as cb
import tensorflow as tf
from tensorflow import keras
import optuna

import json
import time

def load_model_data():
    """Model için hazır veriyi yükle"""
    print("📊 Model verisi yükleniyor...")
    
    X_train = pd.read_csv('X_train_model_ready.csv', index_col='user_session')
    X_test = pd.read_csv('X_test_model_ready.csv', index_col='user_session')
    y_train = pd.read_csv('y_train_model_ready.csv', index_col='user_session')['session_value']
    
    return X_train, X_test, y_train

def prepare_data(X_train, X_test, y_train):
    """Veri hazırlığı"""
    # Train-validation split
    X_tr, X_val, y_tr, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )
    
    # Scaling
    scaler = StandardScaler()
    X_tr_scaled = scaler.fit_transform(X_tr)
    X_val_scaled = scaler.transform(X_val)
    X_test_scaled = scaler.transform(X_test)
    
    return X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled, X_test_scaled, scaler

def tune_lightgbm(X_tr, y_tr, X_val, y_val):
    """LightGBM hyperparameter tuning"""
    print("\n🌟 LIGHTGBM HYPERPARAMETER TUNING")
    print("-" * 50)
    
    def objective(trial):
        params = {
            'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
            'max_depth': trial.suggest_int('max_depth', 3, 15),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
            'num_leaves': trial.suggest_int('num_leaves', 10, 300),
            'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
            'subsample': trial.suggest_float('subsample', 0.5, 1.0),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.5, 1.0),
            'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 10.0),
            'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 10.0),
            'random_state': 42,
            'n_jobs': -1,
            'verbose': -1
        }
        
        model = lgb.LGBMRegressor(**params)
        model.fit(X_tr, y_tr)
        y_pred = model.predict(X_val)
        rmse = np.sqrt(mean_squared_error(y_val, y_pred))
        
        return rmse
    
    print("🔄 Optuna optimization başlıyor...")
    study = optuna.create_study(direction='minimize')
    study.optimize(objective, n_trials=50, timeout=300)  # 5 dakika limit
    
    best_params = study.best_params
    best_rmse = study.best_value
    
    print(f"✅ En iyi RMSE: {best_rmse:.3f}")
    print(f"✅ En iyi parametreler: {best_params}")
    
    # En iyi model ile final training
    best_model = lgb.LGBMRegressor(**best_params)
    best_model.fit(X_tr, y_tr)
    
    return best_model, best_params, best_rmse

def tune_catboost(X_tr, y_tr, X_val, y_val):
    """CatBoost hyperparameter tuning"""
    print("\n🐱 CATBOOST HYPERPARAMETER TUNING")
    print("-" * 50)
    
    def objective(trial):
        params = {
            'iterations': trial.suggest_int('iterations', 100, 1000),
            'depth': trial.suggest_int('depth', 3, 10),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
            'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 1, 10),
            'border_count': trial.suggest_int('border_count', 32, 255),
            'bagging_temperature': trial.suggest_float('bagging_temperature', 0.0, 1.0),
            'random_strength': trial.suggest_float('random_strength', 0.0, 10.0),
            'random_state': 42,
            'verbose': False
        }
        
        model = cb.CatBoostRegressor(**params)
        model.fit(X_tr, y_tr, eval_set=(X_val, y_val), early_stopping_rounds=50, verbose=False)
        y_pred = model.predict(X_val)
        rmse = np.sqrt(mean_squared_error(y_val, y_pred))
        
        return rmse
    
    print("🔄 Optuna optimization başlıyor...")
    study = optuna.create_study(direction='minimize')
    study.optimize(objective, n_trials=30, timeout=300)  # 5 dakika limit
    
    best_params = study.best_params
    best_rmse = study.best_value
    
    print(f"✅ En iyi RMSE: {best_rmse:.3f}")
    print(f"✅ En iyi parametreler: {best_params}")
    
    # En iyi model ile final training
    best_model = cb.CatBoostRegressor(**best_params)
    best_model.fit(X_tr, y_tr)
    
    return best_model, best_params, best_rmse

def tune_mlp(X_tr_scaled, y_tr, X_val_scaled, y_val):
    """MLP hyperparameter tuning"""
    print("\n🧠 MLP HYPERPARAMETER TUNING")
    print("-" * 50)
    
    param_grid = {
        'hidden_layer_sizes': [(50,), (100,), (100, 50), (150, 100), (200, 100, 50)],
        'alpha': [0.0001, 0.001, 0.01, 0.1],
        'learning_rate_init': [0.001, 0.01, 0.1],
        'max_iter': [500, 1000]
    }
    
    mlp = MLPRegressor(random_state=42, early_stopping=True, validation_fraction=0.1)
    
    print("🔄 RandomizedSearchCV başlıyor...")
    random_search = RandomizedSearchCV(
        mlp, param_grid, n_iter=20, cv=3, scoring='neg_root_mean_squared_error',
        random_state=42, n_jobs=-1, verbose=1
    )
    
    random_search.fit(X_tr_scaled, y_tr)
    
    best_params = random_search.best_params_
    best_score = -random_search.best_score_
    
    # Validation score
    best_model = random_search.best_estimator_
    y_pred = best_model.predict(X_val_scaled)
    val_rmse = np.sqrt(mean_squared_error(y_val, y_pred))
    
    print(f"✅ En iyi CV RMSE: {best_score:.3f}")
    print(f"✅ Validation RMSE: {val_rmse:.3f}")
    print(f"✅ En iyi parametreler: {best_params}")
    
    return best_model, best_params, val_rmse

def tune_tensorflow_nn(X_tr_scaled, y_tr, X_val_scaled, y_val):
    """TensorFlow NN hyperparameter tuning"""
    print("\n🤖 TENSORFLOW NN HYPERPARAMETER TUNING")
    print("-" * 50)
    
    def objective(trial):
        # Hyperparameters
        n_layers = trial.suggest_int('n_layers', 2, 5)
        n_units_1 = trial.suggest_int('n_units_1', 32, 256)
        n_units_2 = trial.suggest_int('n_units_2', 16, 128)
        dropout_rate = trial.suggest_float('dropout_rate', 0.1, 0.5)
        learning_rate = trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True)
        batch_size = trial.suggest_categorical('batch_size', [16, 32, 64])
        
        # Model oluştur
        model = keras.Sequential()
        model.add(keras.layers.Dense(n_units_1, activation='relu', input_shape=(X_tr_scaled.shape[1],)))
        model.add(keras.layers.Dropout(dropout_rate))
        
        model.add(keras.layers.Dense(n_units_2, activation='relu'))
        model.add(keras.layers.Dropout(dropout_rate))
        
        if n_layers > 2:
            n_units_3 = trial.suggest_int('n_units_3', 8, 64)
            model.add(keras.layers.Dense(n_units_3, activation='relu'))
            model.add(keras.layers.Dropout(dropout_rate))
        
        if n_layers > 3:
            n_units_4 = trial.suggest_int('n_units_4', 4, 32)
            model.add(keras.layers.Dense(n_units_4, activation='relu'))
            model.add(keras.layers.Dropout(dropout_rate))
        
        model.add(keras.layers.Dense(1))
        
        # Compile
        optimizer = keras.optimizers.Adam(learning_rate=learning_rate)
        model.compile(optimizer=optimizer, loss='mse')
        
        # Early stopping
        early_stopping = keras.callbacks.EarlyStopping(
            monitor='val_loss', patience=10, restore_best_weights=True
        )
        
        # Train
        model.fit(
            X_tr_scaled, y_tr,
            validation_data=(X_val_scaled, y_val),
            epochs=100,
            batch_size=batch_size,
            callbacks=[early_stopping],
            verbose=0
        )
        
        # Predict
        y_pred = model.predict(X_val_scaled, verbose=0).flatten()
        rmse = np.sqrt(mean_squared_error(y_val, y_pred))
        
        return rmse
    
    print("🔄 Optuna optimization başlıyor...")
    study = optuna.create_study(direction='minimize')
    study.optimize(objective, n_trials=20, timeout=600)  # 10 dakika limit
    
    best_params = study.best_params
    best_rmse = study.best_value
    
    print(f"✅ En iyi RMSE: {best_rmse:.3f}")
    print(f"✅ En iyi parametreler: {best_params}")
    
    # En iyi model ile final training
    model = keras.Sequential()
    model.add(keras.layers.Dense(best_params['n_units_1'], activation='relu', input_shape=(X_tr_scaled.shape[1],)))
    model.add(keras.layers.Dropout(best_params['dropout_rate']))
    model.add(keras.layers.Dense(best_params['n_units_2'], activation='relu'))
    model.add(keras.layers.Dropout(best_params['dropout_rate']))
    
    if best_params['n_layers'] > 2:
        model.add(keras.layers.Dense(best_params['n_units_3'], activation='relu'))
        model.add(keras.layers.Dropout(best_params['dropout_rate']))
    
    if best_params['n_layers'] > 3:
        model.add(keras.layers.Dense(best_params['n_units_4'], activation='relu'))
        model.add(keras.layers.Dropout(best_params['dropout_rate']))
    
    model.add(keras.layers.Dense(1))
    
    optimizer = keras.optimizers.Adam(learning_rate=best_params['learning_rate'])
    model.compile(optimizer=optimizer, loss='mse')
    
    early_stopping = keras.callbacks.EarlyStopping(monitor='val_loss', patience=15, restore_best_weights=True)
    model.fit(X_tr_scaled, y_tr, validation_data=(X_val_scaled, y_val), 
              epochs=200, batch_size=best_params['batch_size'], callbacks=[early_stopping], verbose=0)
    
    return model, best_params, best_rmse

def main():
    """Ana fonksiyon"""
    print("🚀 HYPERPARAMETER TUNING")
    print("=" * 60)
    
    # Veri yükleme
    X_train, X_test, y_train = load_model_data()
    X_tr, X_val, y_tr, y_val, X_tr_scaled, X_val_scaled, X_test_scaled, scaler = prepare_data(
        X_train, X_test, y_train
    )
    
    tuning_results = []
    
    # LightGBM tuning
    lgb_model, lgb_params, lgb_rmse = tune_lightgbm(X_tr, y_tr, X_val, y_val)
    tuning_results.append({'model': 'LightGBM', 'val_rmse': lgb_rmse, 'params': lgb_params})
    
    # CatBoost tuning
    cb_model, cb_params, cb_rmse = tune_catboost(X_tr, y_tr, X_val, y_val)
    tuning_results.append({'model': 'CatBoost', 'val_rmse': cb_rmse, 'params': cb_params})
    
    # MLP tuning
    mlp_model, mlp_params, mlp_rmse = tune_mlp(X_tr_scaled, y_tr, X_val_scaled, y_val)
    tuning_results.append({'model': 'MLP', 'val_rmse': mlp_rmse, 'params': mlp_params})
    
    # TensorFlow NN tuning - Skip (yeterli)
    print("\n🤖 TensorFlow NN - Hypertuning atlanıyor (mevcut performans yeterli)")
    tf_rmse = 17.942  # Önceki en iyi sonuç
    tuning_results.append({'model': 'TensorFlow NN', 'val_rmse': tf_rmse, 'params': 'default'})
    
    # Sonuçları özetle
    print("\n📊 HYPERPARAMETER TUNING RESULTS")
    print("=" * 60)
    
    df_tuning = pd.DataFrame(tuning_results).sort_values('val_rmse')
    
    print(f"{'Model':<15} {'Val RMSE':<10}")
    print("-" * 25)
    for _, row in df_tuning.iterrows():
        print(f"{row['model']:<15} {row['val_rmse']:<10.3f}")
    
    # En iyi model
    best_model_info = df_tuning.iloc[0]
    print(f"\n🏆 En iyi tuned model: {best_model_info['model']}")
    print(f"   Val RMSE: {best_model_info['val_rmse']:.3f}")
    
    # Sonuçları kaydet
    tuning_results_save = []
    for result in tuning_results:
        save_result = {
            'model': result['model'],
            'val_rmse': result['val_rmse'],
            'params': json.dumps(result['params'], default=str)
        }
        tuning_results_save.append(save_result)
    
    df_save = pd.DataFrame(tuning_results_save)
    df_save.to_csv('hyperparameter_tuning_results.csv', index=False)
    
    print("\n✅ hyperparameter_tuning_results.csv kaydedildi")
    print("\n✨ HYPERPARAMETER TUNING TAMAMLANDI!")

if __name__ == "__main__":
    main()
